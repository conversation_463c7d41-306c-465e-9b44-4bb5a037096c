import json


from doccheck import schemas
from doccheck.models import (
    Case,
    Field,
    Entity,
    FieldChoice,
    BusinessCaseType,
    DocumentCategory,
    DocumentOption,
    DocCheck,
    FieldType,
)
from doccheck.schemas import PersonUpdate, RealEstatePropertyUpdate
from doccheck.services import (
    map_case_detail_out,
    add_person_to_case,
    add_real_estate_property_to_case,
    map_fields_out,
    add_completeness_rule_to_check,
    check_document_requirements,
    create_case,
    update_case,
    update_person,
    update_property,
)


def create_main_income(doc_check: DocCheck):
    field, _ = Field.objects.get_or_create(
        doc_check=doc_check,
        entity=Entity.Person,
        key="main_income",
        name_de="Haupteinkommen",
        type=FieldType.CHOICE,
    )
    data = json.loads(
        """
    [
  {
    "field": "main_income",
    "key": "retired",
    "name_de": "in Rente",
    "name_en": "retired",
    "name_fr": "à la retraite",
    "name_it": "in pensione",
    "order": 28
  },
  {
    "field": "main_income",
    "key": "employed",
    "name_de": "angestellt",
    "name_en": "employed",
    "name_fr": "employé(e)",
    "name_it": "impiegato",
    "order": 26
  }
]
    """
    )
    for entry in data:
        del entry["field"]
        FieldChoice.objects.update_or_create(entry, field=field, key=entry["key"])
    field.choices_default = FieldChoice.objects.get(
        field__doc_check=doc_check, field=field, key="employed"
    )
    field.save()
    assert FieldChoice.objects.filter(field=field).count() == 2


def create_property_type(doc_check: DocCheck):
    field, _ = Field.objects.get_or_create(
        doc_check=doc_check,
        entity=Entity.RealEstateProperty,
        key="type",
        name_de="Liegenschaftstyp",
        type=FieldType.CHOICE,
    )
    data = json.loads(
        """
    [
  {
    "field": "type",
    "key": "EFH",
    "name_de": "Einfamilienhaus",
    "name_en": "Single family house",
    "name_fr": "Maison individuelle",
    "name_it": "Casa unifamiliare",
    "order": 31
  },
  {
    "field": "type",
    "key": "STWE",
    "name_de": "Eigentumswohnung",
    "name_en": "Condominium",
    "name_fr": "Appartement en copropriété",
    "name_it": "Condominio",
    "order": 30
  }
]
    """
    )
    for entry in data:
        del entry["field"]
        FieldChoice.objects.update_or_create(entry, field=field, key=entry["key"])
    field.choices_default = FieldChoice.objects.get(
        field__doc_check=doc_check, field=field, key="STWE"
    )
    field.save()
    assert FieldChoice.objects.filter(field=field).count() == 2


def create_canton(doc_check: DocCheck):
    field, _ = Field.objects.get_or_create(
        doc_check=doc_check,
        entity=Entity.RealEstateProperty,
        key="address_canton",
        name_de="Standortkanton",
        type=FieldType.CHOICE,
    )
    data = json.loads(
        """
        [
      {
        "field": "address_canton",
        "key": "GE",
        "name_de": "GE",
        "name_en": "GE",
        "name_fr": "GE",
        "name_it": "GE",
        "order": 7
      },
      {
        "field": "address_canton",
        "key": "ZH",
        "name_de": "ZH",
        "name_en": "ZH",
        "name_fr": "ZH",
        "name_it": "ZH",
        "order": 25
      },
      {
        "field": "address_canton",
        "key": "BE",
        "name_de": "BE",
        "name_en": "BE",
        "name_fr": "BE",
        "name_it": "BE",
        "order": 3
      }
    ]
        """
    )
    for entry in data:
        del entry["field"]
        FieldChoice.objects.update_or_create(entry, field=field, key=entry["key"])
    field.choices_default = FieldChoice.objects.get(
        field__doc_check=doc_check, field=field, key="BE"
    )
    field.save()
    assert FieldChoice.objects.filter(field=field).count() == 3


def create_fields(doc_check: DocCheck):
    data = json.loads(
        """
[
  {
    "entity": 2,
    "key": "main_income",
    "name_de": "Haupteinkommen",
    "name_en": "Main income",
    "name_fr": "Revenu principal",
    "name_it": "Reddito principale",
    "order": 2,
    "type": 1
  },
  {
    "entity": 2,
    "key": "pledge_pillar_3_account",
    "name_de": "Verpfändung Säule 3 Konto",
    "name_en": "Pledge pillar 3 account",
    "name_fr": "Mise en gage du compte du pilier 3",
    "name_it": "Conto corrente del pilastro 3",
    "order": 3,
    "type": 2
  },
  {
    "entity": 2,
    "key": "pledge_pillar_3_insurance",
    "name_de": "Verpfändung Säule 3 Police",
    "name_en": "Pledge pillar 3 policy",
    "name_fr": "Mise en gage de la police du pilier 3",
    "name_it": "Politica del pilastro 3 promessa",
    "order": 4,
    "type": 2
  },
  {
    "entity": 2,
    "key": "has_divorce",
    "name_de": "Scheidung (sofern relevant)",
    "name_en": "Divorce (if relevant)",
    "name_fr": "Divorce (si pertinent)",
    "name_it": "Divorzio (se pertinente)",
    "order": 5,
    "type": 2
  },
  {
    "entity": 2,
    "key": "has_liabilities",
    "name_de": "Leasing oder Konsumkredit",
    "name_en": "Leasing or consumer credit",
    "name_fr": "Leasing ou crédit à la consommation",
    "name_it": "Leasing o credito al consumo",
    "order": 6,
    "type": 2
  },
  {
    "entity": 3,
    "key": "type",
    "name_de": "Liegenschaftstyp",
    "name_en": "Property type",
    "name_fr": "Type de bien immobilier",
    "name_it": "Tipo di proprietà",
    "order": 8,
    "type": 1
  },
  {
    "entity": 3,
    "key": "address_canton",
    "name_de": "Standortkanton",
    "name_en": "Location canton",
    "name_fr": "Canton d'implantation",
    "name_it": "Posizione canton",
    "order": 9,
    "type": 1
  },
  {
    "entity": 3,
    "key": "for_rent",
    "name_de": "vermietet",
    "name_en": "rented",
    "name_fr": "loué",
    "name_it": "affittato",
    "order": 10,
    "type": 2
  },
  {
    "entity": 3,
    "key": "purchase_year_lt_2y",
    "name_de": "Kaufdatum jünger als zwei Jahre",
    "name_en": "Purchase date younger than two years",
    "name_fr": "Date d'achat inférieure à deux ans",
    "name_it": "Data di acquisto inferiore a due anni",
    "order": 11,
    "type": 2
  },
  {
    "entity": 3,
    "key": "has_use_restrictions",
    "name_de": "Baurecht",
    "name_en": "Building lease",
    "name_fr": "Droit de superficie",
    "name_it": "Locazione di edifici",
    "order": 12,
    "type": 2
  },
  {
    "entity": 3,
    "key": "has_reconstructions",
    "name_de": "Umbau, Renovation",
    "name_en": "Conversion, renovation",
    "name_fr": "Transformation, rénovation",
    "name_it": "Conversione, ristrutturazione",
    "order": 13,
    "type": 2
  },
  {
    "entity": 3,
    "key": "has_construction_contract",
    "name_de": "GU, TU",
    "name_en": "General contractor, total contractor",
    "name_fr": "Entrepreneur général, entrepreneur total",
    "name_it": "Impresa generale, impresa totale",
    "order": 14,
    "type": 2
  }
]
"""
    )
    for entry in data:
        Field.objects.update_or_create(
            entry, doc_check=doc_check, entity=entry["entity"], key=entry["key"]
        )
    assert Field.objects.all().count() == 12


def create_doc_check():
    doc_check, _ = DocCheck.objects.get_or_create(key="test completeness check")
    # account = Account.objects.create(key='test-account', active_doc_check=doc_check)
    create_main_income(doc_check=doc_check)
    create_property_type(doc_check=doc_check)
    create_canton(doc_check=doc_check)
    create_fields(doc_check=doc_check)
    # The default DocCheck.BusinessCaseType with key 'UNKNOWN' needs to be present, as the respective foreign key in
    # DocCheck.Case cannot be null.
    BusinessCaseType.objects.get_or_create(doc_check=doc_check, key="UNKNOWN")
    return doc_check


def test_create_case(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = create_case(doc_check, business_case_type)
    assert case.uuid
    assert case.business_case_type.key == "test-business-case"
    assert case.person_set.count() == 1
    assert case.realestateproperty_set.count() == 1
    person_1 = case.person_set.first()
    assert (
        person_1.main_income.key
        == Field.objects.get(
            entity=Entity.Person, key="main_income"
        ).choices_default.key
    )
    assert person_1.has_divorce is False  # default
    real_estate_property = case.realestateproperty_set.first()
    assert (
        real_estate_property.type.key
        == Field.objects.get(
            entity=Entity.RealEstateProperty, key="type"
        ).choices_default.key
    )
    assert real_estate_property.has_reconstructions is False  # default


def test_update_case(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = create_case(doc_check, business_case_type)
    business_case_type_updated = BusinessCaseType.objects.create(
        key="test-business-case updated", doc_check=doc_check
    )
    update_case(
        case=case,
        case_to_update=schemas.CaseUpdate(
            business_case_type="test-business-case updated"
        ),
    )
    assert case.business_case_type == business_case_type_updated


def test_case_detail_out(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = create_case(doc_check, business_case_type)
    add_person_to_case(
        case=case,
        person_to_create=schemas.PersonCreate(main_income="retired", has_divorce=True),
    )
    case_out = map_case_detail_out(case)
    assert case_out.uuid == case.uuid

    persons = case.person_set.order_by("uuid").all()
    assert sorted([p.uuid for p in case_out.persons]) == [p.uuid for p in persons]
    idx = 0
    for person in case_out.persons:
        idx += 1
        assert person.name_de == f"Kreditnehmer {idx}"
        assert person.main_income == next(
            (p.main_income.key for p in persons if p.uuid == person.uuid), None
        )
        assert person.has_divorce == next(
            (p.has_divorce for p in persons if p.uuid == person.uuid), None
        )

    real_estate_properties = case.realestateproperty_set.order_by("uuid").all()
    assert [case_out.real_estate_property.uuid] == [
        p.uuid for p in real_estate_properties
    ]
    assert case_out.real_estate_property.name_de == "Liegenschaft"
    assert case_out.real_estate_property.type == real_estate_properties[0].type.key
    assert (
        case_out.real_estate_property.address_canton
        == real_estate_properties[0].address_canton.key
    )


def test_person_update(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = create_case(doc_check, business_case_type)
    person_1 = case.person_set.first()
    assert (
        person_1.main_income.key
        == Field.objects.get(
            entity=Entity.Person, key="main_income"
        ).choices_default.key
    )
    assert person_1.has_divorce is False
    assert person_1.has_liabilities is False
    # update without setting any attribute should not alter object
    person_1_update_1 = update_person(
        case=case, person_uuid=person_1.uuid, person_to_update=PersonUpdate()
    )
    assert person_1_update_1 == person_1
    person_1_update_2 = update_person(
        case=case,
        person_uuid=person_1.uuid,
        person_to_update=PersonUpdate(main_income="retired", has_divorce=True),
    )
    assert (
        person_1_update_2.main_income.key
        == FieldChoice.objects.get(
            field__doc_check=case.doc_check,
            field__entity=Entity.Person,
            field__key="main_income",
            key="retired",
        ).key
    )
    assert person_1_update_2.has_divorce is True
    assert person_1_update_2.has_liabilities is False
    person_1_update_3 = update_person(
        case=case,
        person_uuid=person_1.uuid,
        person_to_update=PersonUpdate(has_divorce=False),
    )
    assert person_1_update_3.has_divorce is False


def test_property_update(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = create_case(doc_check, business_case_type)
    property_1 = case.realestateproperty_set.first()
    assert (
        property_1.type.key
        == Field.objects.get(
            entity=Entity.RealEstateProperty, key="type"
        ).choices_default.key
    )
    assert property_1.has_reconstructions is False
    assert property_1.has_use_restrictions is False
    # update without setting any attribute should not alter object
    property_1_update_1 = update_property(
        case=case,
        property_uuid=property_1.uuid,
        property_to_update=RealEstatePropertyUpdate(),
    )
    assert property_1_update_1 == property_1
    property_1_update_2 = update_property(
        case=case,
        property_uuid=property_1.uuid,
        property_to_update=RealEstatePropertyUpdate(
            type="EFH", address_canton="GE", has_reconstructions=True
        ),
    )
    assert (
        property_1_update_2.type.key
        == FieldChoice.objects.get(
            field__doc_check=case.doc_check,
            field__entity=Entity.RealEstateProperty,
            field__key="type",
            key="EFH",
        ).key
    )
    assert (
        property_1_update_2.address_canton.key
        == FieldChoice.objects.get(
            field__doc_check=case.doc_check,
            field__entity=Entity.RealEstateProperty,
            field__key="address_canton",
            key="GE",
        ).key
    )
    assert property_1_update_2.has_reconstructions is True
    assert property_1_update_2.has_use_restrictions is False
    person_1_update_3 = update_property(
        case=case,
        property_uuid=property_1.uuid,
        property_to_update=RealEstatePropertyUpdate(has_reconstructions=False),
    )
    assert person_1_update_3.has_reconstructions is False


def test_fields_out(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = create_case(doc_check, business_case_type)

    # add the business_case_type of the created case to some fields
    field_main_income = Field.objects.get(key="main_income")
    field_main_income.shown_for_the_following_business_case_types.add(
        business_case_type
    )
    field_has_divorce = Field.objects.get(key="has_divorce")
    field_has_divorce.shown_for_the_following_business_case_types.add(
        business_case_type
    )
    field_type = Field.objects.get(key="type")
    field_type.shown_for_the_following_business_case_types.add(business_case_type)

    fields_out = map_fields_out(case)
    assert len(fields_out) == 4

    field_out_business_case_type = next(
        (field for field in fields_out if field.key == "business_case_type"), None
    )
    assert field_out_business_case_type
    assert field_out_business_case_type.name_de == "Geschäftsfalltyp"
    business_case_type_choices = BusinessCaseType.objects.order_by("order").all()
    assert [choice.key for choice in field_out_business_case_type.choices] == [
        choice.key for choice in business_case_type_choices
    ]

    field_out_main_income = next(
        (field for field in fields_out if field.key == "main_income"), None
    )
    assert field_out_main_income
    assert field_out_main_income.name_de == field_main_income.name_de
    main_income_choices = FieldChoice.objects.order_by("order").filter(
        field=field_main_income
    )
    assert [choice.key for choice in field_out_main_income.choices] == [
        choice.key for choice in main_income_choices
    ]

    field_out_has_divorce = next(
        (field for field in fields_out if field.key == "has_divorce"), None
    )
    assert field_out_has_divorce
    assert field_out_has_divorce.name_en == field_has_divorce.name_en

    field_out_type = next((field for field in fields_out if field.key == "type"), None)
    assert field_out_type
    assert field_out_type.name_fr == field_type.name_fr
    type_choices = FieldChoice.objects.order_by("order").filter(field=field_type)
    assert [choice.key for choice in field_out_type.choices] == [
        choice.key for choice in type_choices
    ]


def test_rule_no_business_case(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(key="test_rule", entity=Entity.Person),
    )
    result = check_document_requirements(case)
    assert len(result) == 0


def test_rule_no_entity(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    rule = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(key="test_rule", entity=Entity.Person),
    )
    rule.business_case_types.add(business_case_type)
    result = check_document_requirements(case)
    assert len(result) == 0


def test_rule_one_person(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    person_1 = add_person_to_case(case=case, person_to_create=schemas.PersonCreate())
    rule = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="test_rule_person", entity=Entity.Person
        ),
    )
    rule.business_case_types.add(business_case_type)
    result = check_document_requirements(case)
    assert len(result) == 1
    assert result[0].case_uuid == case.uuid
    assert result[0].rule_key == rule.key
    assert result[0].context_model_uuid == person_1.uuid


def test_rule_multiple_persons(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    person_1 = add_person_to_case(case=case, person_to_create=schemas.PersonCreate())
    person_2 = add_person_to_case(case=case, person_to_create=schemas.PersonCreate())
    person_3 = add_person_to_case(case=case, person_to_create=schemas.PersonCreate())
    rule = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="test_rule_person", entity=Entity.Person
        ),
    )
    rule.business_case_types.add(business_case_type)
    result = check_document_requirements(case)
    assert len(result) == 3
    for r in result:
        assert r.case_uuid == case.uuid
        assert r.rule_key == rule.key
        assert r.context_model_uuid in [p.uuid for p in [person_1, person_2, person_3]]


def test_rule_multiple_models(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    add_person_to_case(case=case, person_to_create=schemas.PersonCreate())
    add_person_to_case(case=case, person_to_create=schemas.PersonCreate())
    property_1 = add_real_estate_property_to_case(
        case=case, property_to_create=schemas.RealEstatePropertyCreate()
    )
    rule_person = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="test_rule_person", entity=Entity.Person
        ),
    )
    rule_person.business_case_types.add(business_case_type)
    rule_property = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="test_rule_property", entity=Entity.RealEstateProperty
        ),
    )
    rule_property.business_case_types.add(business_case_type)
    result = check_document_requirements(case)
    print(result)
    assert len(result) == 3
    for r in result:
        assert r.rule_key in [rule_person.key, rule_property.key]
        if r.rule_key == rule_property.key:
            assert r.context_model_uuid in [p.uuid for p in [property_1]]


def test_rule_model_values(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    add_real_estate_property_to_case(
        case=case,
        property_to_create=schemas.RealEstatePropertyCreate(
            has_reconstructions=True, has_use_restrictions=True, address_canton="GE"
        ),
    )
    rule_property_reconstruction = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="rule_property_reconstruction",
            entity=Entity.RealEstateProperty,
            field="has_reconstructions",
            values="True",
        ),
    )
    rule_property_reconstruction.business_case_types.add(business_case_type)
    add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="rule_property_use_restr",
            entity=Entity.RealEstateProperty,
            field="has_use_restrictions",
            values="True",
        ),
    )
    rule_property_gustavo = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="rule_property_gustavo",
            entity=Entity.RealEstateProperty,
            field="gustavo",
            values="True",
        ),
    )
    rule_property_gustavo.business_case_types.add(business_case_type)
    result = check_document_requirements(case)
    assert len(result) == 2
    for r in result:
        assert r.rule_key in ["rule_property_reconstruction", "rule_property_gustavo"]
        assert r.rule_key not in [
            "rule_property_use_restr"
        ]  # as this rule has no matching business case type


def test_rule_doc_requirements(db):
    doc_check = create_doc_check()
    business_case_type = BusinessCaseType.objects.create(
        key="test-business-case", doc_check=doc_check
    )
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    add_real_estate_property_to_case(
        case=case, property_to_create=schemas.RealEstatePropertyCreate()
    )
    rule_property = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=schemas.CompletenessRule(
            key="test_rule_property", entity=Entity.RealEstateProperty
        ),
    )
    rule_property.business_case_types.add(business_case_type)
    doc_cat_1 = DocumentCategory.objects.create(doc_check=doc_check, name="test_cat_1")
    doc_cat_1_age = 12
    doc_option_1 = DocumentOption.objects.create(
        completeness_rule=rule_property,
        document_category=doc_cat_1,
        max_doc_age_in_months=doc_cat_1_age,
    )
    doc_cat_2 = DocumentCategory.objects.create(doc_check=doc_check, name="test_cat_2")
    doc_cat_2_age = 24
    doc_option_2 = DocumentOption.objects.create(
        completeness_rule=rule_property,
        document_category=doc_cat_2,
        max_doc_age_in_months=doc_cat_2_age,
    )
    result = check_document_requirements(case)
    assert len(result) == 1
    result_doc_options = result[0].document_options
    assert sorted([option.uuid for option in result_doc_options]) == sorted(
        [doc_option_1.uuid, doc_option_2.uuid]
    )
    result_doc_option_1 = next(
        (option for option in result_doc_options if option.uuid == doc_option_1.uuid),
        None,
    )
    assert result_doc_option_1.document_category == doc_cat_1.name
    assert result_doc_option_1.max_doc_age_in_months == doc_cat_1_age
