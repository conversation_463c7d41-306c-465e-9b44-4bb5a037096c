# Generated by Django 3.2.16 on 2022-11-01 07:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('statemgmt', '0002_load_intial_data'),
        ('dossier', '0029_account_valid_ui_languages'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='active_dossier_state_machine',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='statemgmt.statemachine'),
        ),
        migrations.AddField(
            model_name='documentcategory',
            name='account',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='dossier.account'),
        ),
    ]
