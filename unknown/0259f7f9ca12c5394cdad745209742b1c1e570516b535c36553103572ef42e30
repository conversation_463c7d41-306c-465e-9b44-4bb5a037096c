import json
import os
import uuid
from typing import List

import pytest
from django.conf import settings
from django.test.client import Client, AsyncClient
from faker import Faker
import jwt
from jwcrypto import jwk

from dossier.models import Account, DocumentCategory, JWK
from dossier.services import create_expiration_date
from projectconfig.jwk import load_jwk_from_env
from swissfex.tests.data import DATA_PATH
from swissfex.tests.factories import SwissFexAccountFactoryFaker


@pytest.fixture(scope="session")
def mock_jwks_public_private():
    # JWKS with both public and private keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()


@pytest.fixture(scope="session")
def mock_jwks_pem_public_private(mock_jwks_public_private):
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][0]))
    return jwk_key.export_to_pem(private_key=True, password=None)


@pytest.fixture(scope="session")
def mock_jwks_public():
    # JWKS with only public keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    ).model_dump(exclude_unset=True)


@pytest.fixture(scope="session")
def faker():
    return Faker(locale="de_CH")


@pytest.fixture
def swissfex_account():
    account, _ = Account.objects.update_or_create(
        key="swissfexd",
        defaults=dict(
            name="swissfex development account",
            default_bucket_name="dms-default-bucket",
        ),
        dmf_endpoint="https://www.localhost",
    )
    return account


@pytest.fixture
def random_other_account():
    # Use this account to test separation of accounts (e.g. DocumentCategories in multiple accounts)
    account, _ = Account.objects.update_or_create(
        key="randomotheraccount",
        defaults=dict(
            name="random other account",
            default_bucket_name="dms-default-bucket",
        ),
        dmf_endpoint="https://www.localhost",
    )
    return account


@pytest.fixture
def set_swissfex_JWK(swissfex_account, mock_jwks_public):
    # Set public key used for zkb authentication
    # We save public keys in a model called JWK which has an FK to account
    # This way only accounts with associated public keys are allowed to authenticate in
    return JWK.objects.create(jwk=mock_jwks_public["keys"][0], account=swissfex_account)


@pytest.fixture
def swissfex_account_factory(swissfex_account):
    return SwissFexAccountFactoryFaker(account=swissfex_account)


@pytest.fixture
def document_categories(swissfex_account_factory) -> List[DocumentCategory]:
    return swissfex_account_factory.load_initial_document_categories()


@pytest.fixture(scope="session")
def token_data(faker):
    # Differs from DossierCreateJWT, with
    # given_name, family_name, preferred_username
    # Decided what we want to standardise on
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-swissfex-first service-swissfex-last",
        "given_name": "service-swissfex-first",
        "family_name": "service-swissfex-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        "user_roles": ["api_role"],
        "account_key": "swissfexd",
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


@pytest.fixture(scope="session")
def mock_token(mock_jwks_pem_public_private, token_data):
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=mock_jwks_pem_public_private,
        algorithm="RS256",
    )


class AuthenticatedClient(Client):
    def __init__(
        self, token, enforce_csrf_checks=False, raise_request_exception=True, **defaults
    ):
        self.token = token
        super().__init__(enforce_csrf_checks, raise_request_exception, **defaults)

    def request(self, **request):
        request.update({"HTTP_AUTHORIZATION": f"Bearer {self.token}"})
        return super().request(**request)


class AsyncAuthenticatedClient(AsyncClient):
    def __init__(
        self, token, enforce_csrf_checks=False, raise_request_exception=True, **defaults
    ):
        self.token = token
        super().__init__(enforce_csrf_checks, raise_request_exception, **defaults)

    async def request(self, **request):
        request.update({"HTTP_AUTHORIZATION": f"Bearer {self.token}"})
        return super().request(**request)


@pytest.fixture(scope="session")
def authenticated_client(mock_token):
    return AuthenticatedClient(token=mock_token)


@pytest.fixture(scope="session")
def async_authenticated_client(mock_token):
    return AsyncAuthenticatedClient(token=mock_token)
