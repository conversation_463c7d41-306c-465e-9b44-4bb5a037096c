# Create your models here.
from django.db.models import CASCADE

from core.behaviors import Timestampable
from dossier.models import DossierFile
from django.db import models

from semantic_document.models import SemanticDocument


class SemanticDocumentExport(Timestampable):
    file = models.ForeignKey(DossierFile, on_delete=CASCADE, blank=True, null=True)
    done = models.DateTimeField(blank=True, null=True)
    semantic_document = models.ForeignKey(
        SemanticDocument, on_delete=CASCADE, related_name="exports"
    )
    error_message = models.TextField(blank=True, null=True)
