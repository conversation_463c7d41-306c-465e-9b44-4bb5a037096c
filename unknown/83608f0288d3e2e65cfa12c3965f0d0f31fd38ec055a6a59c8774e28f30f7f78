# Generated by Django 4.2.20 on 2025-03-16 21:57

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("semantic_document", "0021_semanticdocument_custom_semantic_document_date"),
    ]

    operations = [
        migrations.CreateModel(
            name="SemanticPageUserAnnotations",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("annotation_group_uuid", models.UUIDField()),
                (
                    "annotation_type",
                    models.CharField(
                        choices=[("highlight", "highlight"), ("comment", "comment")]
                    ),
                ),
                ("bbox_top", models.FloatField()),
                ("bbox_left", models.FloatField()),
                ("bbox_width", models.FloatField()),
                ("bbox_height", models.FloatField()),
                ("text", models.TextField(blank=True, null=True)),
                (
                    "semantic_page",
                    models.<PERSON>Key(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_annotations",
                        to="semantic_document.semanticpage",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
