# Generated by Django 4.2.13 on 2024-07-01 16:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0094_account_enable_dossier_assignment_to_someone_else_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="fileexception",
            name="exception_type",
            field=models.IntegerField(
                choices=[
                    (1, "Unknown Exception"),
                    (2, "Not Readable"),
                    (3, "Password Protected"),
                    (4, "Unsupported Filetype"),
                    (5, "Too Small File"),
                    (6, "Xlsm File Cannot Be Converted"),
                    (7, "Ocr Filetype Processing"),
                    (8, "Virus Detected"),
                    (999, "Unmapped Exception"),
                ],
                default=1,
            ),
        ),
    ]
