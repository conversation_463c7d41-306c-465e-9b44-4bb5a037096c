import os

import aiohttp
from dotenv import load_dotenv
from miniopy_async import Minio, S3Error
from sanic import Sanic, Request, exceptions

load_dotenv()

app = Sanic("hystatic")

ENDPOINT = os.getenv("HYSTATIC_S3_ENDPOINT", "sos-ch-dk-2.exo.io")
ACCESS_KEY = os.getenv("HYSTATIC_S3_ACCESS_KEY")
SECRET_KEY = os.getenv("HYSTATIC_S3_SECRET_KEY")
REGION = os.getenv("HYSTATIC_S3_REGION", "ch-dk-2")
BUCKET = os.getenv("HYSTATIC_S3_BUCKET", "dev-hystatic")


client = Minio(
    ENDPOINT,
    access_key=ACCESS_KEY,
    secret_key=SECRET_KEY,
    region=REGION,
    secure=True,  # http for False, https for True
)


@app.get("/<objectpath:path>", stream=True)
async def get_file(request: Request, objectpath: str):
    if objectpath == "":
        object_sub_path = "index.html"
    else:
        object_sub_path = objectpath
    async with aiohttp.ClientSession() as session:
        try:
            try:
                object = await client.get_object(
                    BUCKET, f"{request.host}/{object_sub_path}", session=session
                )
            except S3Error:
                object = await client.get_object(
                    BUCKET, f"{request.host}/index.html", session=session
                )
        except S3Error:
            raise exceptions.NotFound()
        response = await request.respond(content_type=object.content_type)
        async for data in object.content.iter_chunked(1024):
            await response.send(data)
