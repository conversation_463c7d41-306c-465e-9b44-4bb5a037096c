import json

from django.http import HttpResponse
from django.utils.deprecation import MiddlewareMixin

from dossier.exceptions import HttpError


class HttpExceptionMiddleware(MiddlewareMixin):
    def process_exception(self, request, exception):
        if isinstance(exception, HttpError):
            return HttpResponse(
                json.dumps({"detail": exception.detail}), status=exception.status
            )
