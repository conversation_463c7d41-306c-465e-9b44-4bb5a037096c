from pathlib import Path
from uuid import UUID

from PyPDF2 import PdfWriter
from pydantic import BaseModel

from dossier.schemas import (
    SemanticDocumentFullApiData,
    SemanticDossier,
    ProcessedFileFullApiData,
    SemanticPageFullApiData,
)
from semantic_document.schemas_page_annotations import UserAnnotationsSchema
from semantic_document.services_pdf import (
    create_semantic_document_pdf,
)

from semantic_document.services import create_semantic_document_pdf_filename


class SemanticDocumentExport(BaseModel):
    semantic_document_uuid: UUID
    filename: str
    pages: int
    path: Path


def worker_generate_semantic_document_pdf(
    semantic_dossier: SemanticDossier,
    semantic_document_uuid: UUID,
    dest_path: Path,
    add_metadata: bool = True,
    add_uuid_suffix: bool = False,
) -> SemanticDocumentExport:
    """
    Create a single pdf for a single semantic document
    Uses the SemanticDossier schema passed in by worker

    Note: this function is unable to handle semantic documents with no pages
    Make sure you filter for them before calling this function

    @param semantic_dossier:
    @param dest_path:
    @return:
    """
    dest_path.mkdir(parents=True, exist_ok=True)

    processed_files: dict[str, ProcessedFileFullApiData] = (
        semantic_dossier.processed_files
    )

    # Pythonic in style?
    # Filter to get semantic document we want
    semantic_document: SemanticDocumentFullApiData = next(
        (
            semantic_doc
            for semantic_doc in semantic_dossier.semantic_documents
            if semantic_doc.uuid == semantic_document_uuid
        ),
        None,
    )

    def get_pdf_url(page: SemanticPageFullApiData):
        pages = processed_files[page.source_file_uuid].pages
        processed_page = pages[str(page.source_page_number)]
        dossier_file_uuid = processed_page.searchable_pdf_dossier_file_uuid
        return semantic_dossier.dossier_files[str(dossier_file_uuid)].url

    def get_annotations(page):
        if hasattr(page, "annotations"):
            return [
                UserAnnotationsSchema(
                    annotation_group_uuid=ann.annotation_group_uuid,
                    annotation_type=ann.annotation_type,
                    bbox_top=ann.bbox_top,
                    bbox_left=ann.bbox_left,
                    bbox_width=ann.bbox_width,
                    bbox_height=ann.bbox_height,
                    text=ann.text,
                    hexcolor=ann.hexcolor,
                )
                for ann in page.annotations
            ]
        return None

    # Initialize PdfWriter and collect all pages into a single PDF
    writer: PdfWriter = create_semantic_document_pdf(
        semantic_document=semantic_document,
        pages=semantic_document.semantic_pages,
        get_pdf_url_func=get_pdf_url,
        get_annotations_func=get_annotations,
        add_metadata=add_metadata,
    )

    # Write the single PDF file
    # First create the filename
    filename = create_semantic_document_pdf_filename(
        semantic_document.formatted_title, semantic_document.uuid, add_uuid_suffix
    )
    # Then create the full path
    document_file_path: Path = dest_path / filename
    writer.write(document_file_path)

    # Publish Semantic Document export

    return SemanticDocumentExport(
        semantic_document_uuid=semantic_document.uuid,
        filename=semantic_document.filename,
        pages=len(semantic_document.semantic_pages),
        path=document_file_path,
    )
