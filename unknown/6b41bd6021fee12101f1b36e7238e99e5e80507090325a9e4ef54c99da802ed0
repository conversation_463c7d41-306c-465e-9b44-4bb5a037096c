# Generated by Django 4.1.2 on 2022-10-20 11:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0026_account_valid_dossier_languages'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='show_document_category_external',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='documentcategory',
            name='additional_search_terms',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='documentcategory',
            name='de_external',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='documentcategory',
            name='en_external',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='documentcategory',
            name='fr_external',
            field=models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='documentcategory',
            name='it_external',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
    ]
