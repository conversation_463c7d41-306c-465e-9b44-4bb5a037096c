import djclick as click
import structlog
from django.contrib.auth import get_user_model
from faker import Faker

from pentest.factories import PentestAccountFactoryFaker

User = get_user_model()
logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
def load_account(
    account_key: str,
):
    """
    Create or update a pentest account with close to production config. This loads
    document categories.

    Example:

    python manage.py reset-db
    python manage.py load_pentest_data load-account pentest

    """
    Faker.seed(234777)
    bfac = PentestAccountFactoryFaker(
        account_key=account_key,
    )
    bfac.account.save()
