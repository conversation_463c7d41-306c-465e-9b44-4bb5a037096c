from pathlib import Path

import httpx
import miniopy_async
import pytest
from django.conf import settings
from miniopy_async import S3Error
from pydantic import HttpUrl

from tempfilestore.filestore import TemporaryFileStore, FileStore


@pytest.mark.skip(
    reason="Miniopy does not work on CI, not currently configured or mocked"
)
async def test_simple_filestore():
    # we store the files in s3 (different backend for the temporary store are possible)
    minio_client = miniopy_async.Minio(
        settings.TEMPFILESTORE_S3_ENDPOINT,
        settings.TEMPFILESTORE_S3_ACCESS_KEY,
        settings.TEMPFILESTORE_S3_SECRET_KEY,
        secure=settings.TEMPFILESTORE_S3_SECURE,
        region=settings.TEMPFILESTORE_S3_REGION,
    )

    # temporary filestore with minio as backend
    filestore = TemporaryFileStore(
        FileStore(
            minio_client,
        ),
        bucket_name=settings.TEMPFILESTORE_S3_BUCKET,
    )

    # upload a sample file
    testfile = Path(__file__).parent / "data/testfile.txt"
    res = await filestore.put(testfile)
    assert isinstance(res, HttpUrl)

    # check if we can download the file
    async with httpx.AsyncClient() as client:
        res = await client.get(res)
        assert res.status_code == 200

    # we should not be able to list the objects in the bucket (it is a exoscale sos s3 policy)
    # current policy ALLOW: resources.bucket == 'dev-tempfilestore' && operation in ['get-object', 'put-object', 'get-sos-presigned-url']

    with pytest.raises(S3Error):
        await minio_client.list_objects(settings.TEMPFILESTORE_S3_BUCKET)
