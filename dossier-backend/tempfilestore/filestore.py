"""
A temporary file store to put files and get a presigned url to download them.
Only to the owner of the url is allowed to download the file.
"""

from django.utils import timezone

from datetime import timedelta
from pathlib import Path
from time import time
from uuid import uuid4

import miniopy_async
import structlog
from pydantic import HttpUrl, TypeAdapter

from django.conf import settings

logger = structlog.getLogger(__name__)


class FileStore:
    def __init__(self, client: miniopy_async.Minio):
        self.client = client

    async def fput_object(self, bucket_name: str, object_name: str, file_path: Path):
        await self.client.fput_object(bucket_name, object_name, file_path)

    async def presigned_get_object(
        self,
        bucket_name: str,
        object_name: str,
        expires=timedelta(days=7),
        request_date=None,
    ) -> HttpUrl:
        return TypeAdapter(HttpUrl).validate_python(
            await self.client.presigned_get_object(
                bucket_name, object_name, expires=expires, request_date=request_date
            ),
        )


class TemporaryFileStore:
    def __init__(self, file_store: FileStore, bucket_name: str):
        self.file_store = file_store
        self.bucket_name = bucket_name

    async def put(self, file: Path, expires=timedelta(days=7)):
        assert file.exists()
        logger.debug(f"starting uploading file {file}")
        local_timedelta = expires
        if expires > timedelta(days=7):
            local_timedelta = timedelta(days=7)
        expire_date = timezone.now() + local_timedelta
        day = expire_date.strftime("%Y%m%d")
        hour = expire_date.strftime("%H")
        time_slice = expire_date.strftime("%H%M%S")

        uuid = uuid4()
        filename = file.name

        object_name = f"{day}/{hour}/{time_slice}/{uuid}/{filename}"

        start = time()

        await self.file_store.fput_object(self.bucket_name, object_name, file)
        logger.debug(
            f"finished uploading file {file}", duration=round(time() - start, 2)
        )

        return await self.file_store.presigned_get_object(
            self.bucket_name, object_name, expires
        )


async def upload_file_and_get_presigned_url(temp_path: Path) -> HttpUrl:
    # Note we are always using sos-ch-dk-2.exo.io even for tests
    # We should be using s3 store in docker compose for testing
    # and local development
    minio_client = miniopy_async.Minio(
        settings.TEMPFILESTORE_S3_ENDPOINT,
        settings.TEMPFILESTORE_S3_ACCESS_KEY,
        settings.TEMPFILESTORE_S3_SECRET_KEY,
        secure=settings.TEMPFILESTORE_S3_SECURE,
        region=settings.TEMPFILESTORE_S3_REGION,
    )
    filestore = TemporaryFileStore(
        FileStore(
            minio_client,
        ),
        bucket_name=settings.TEMPFILESTORE_S3_BUCKET,
    )

    res: HttpUrl = await filestore.put(temp_path)

    return res
