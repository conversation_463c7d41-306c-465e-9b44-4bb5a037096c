version: '3.8'
services:
  db:
    image: postgres:12
    environment:
      POSTGRES_DB: test_dms
      POSTGRES_USER: dms
      POSTGRES_PASSWORD: dms
    healthcheck:
      test: pg_isready -U dms -d test_dms
      interval: 10s
      timeout: 3s
      retries: 3

  minio:
    image: quay.io/minio/minio:RELEASE.2022-06-11T19-55-32Z
    entrypoint: "/usr/bin/sh"
    command: -c "mkdir -p /data/dossier && mkdir -p /data/dms-default-bucket && mkdir -p /data/production-v2-test-clientistest && minio server /data --console-address ':9006'"
    hostname: minio
    environment:
      MINIO_ROOT_USER: S3_ACCESS_KEY
      MINIO_ROOT_PASSWORD: S3_SECRET_KEY
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9000/minio/health/live" ]
      interval: 30s
      timeout: 20s
      retries: 3

  dms-test:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: pytest
    working_dir: /app
    secrets:
      - source: DMS_TEST_ENV_V04
        target: /app/.env
    volumes:
      - ./output:/output
    depends_on:
      - db
      - minio


secrets:
  DMS_TEST_ENV_V04:
    file: ./.env-test