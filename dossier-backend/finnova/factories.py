from typing import Optional

import structlog

from assets import ASSETS_PATH
from dossier.dossier_access_external import (
    get_access_check_provider_default_dossier_access_grant,
)
from finnova import schemas
from dossier.factories import (
    set_new_account_defaults,
    DefaultAccountFactoryFaker,
)
from dossier.fakes import load_initial_document_categories
from dossier.models import (
    Account,
    NavigationStrategy,
    DocumentCategory,
    DossierAccessCheckErrorComponent,
    DossierCloseStrategy,
)
from projectconfig.settings import (
    DEFAULT_VALID_UI_LANGUAGES,
    DEFAULT_VALID_DOSSIER_LANGUAGES,
)
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)

logger = structlog.get_logger()

FRONTEND_THEME_FINNOVA: str = "FINNOVA"


def update_or_create_finnova_account(
    account_key: str, default_bucket_name: str = None
) -> Account:
    assert is_valid_account_key(
        account_key
    ), f"Invalid account key: '{account_key}'. Valid keys are {[a.value for a in schemas.AccountName]}"

    account, _ = Account.objects.update_or_create(key=account_key)

    if account_key == schemas.AccountName.finnovadev.value:
        # Used for development HD internally and shared dev environment
        account.name = "finnova Development"
        account.default_bucket_name = "production-v2-test-finnovadev"
        account.dmf_endpoint = "https://finnovadev.test.hypodossier.ch"
    elif account_key == schemas.AccountName.finnovatest.value:
        account.name = "finnova Test"
        account.default_bucket_name = "production-v2-test-finnovatest"
        account.dmf_endpoint = "https://finnovatest.test.hypodossier.ch"
    elif account_key == schemas.AccountName.finnovaprod.value:
        account.name = "finnova Production"
        account.default_bucket_name = "production-v2-finnova-dms"
        account.dmf_endpoint = "https://finnova.hypodossier.ch"

        account.navigation_strategy = NavigationStrategy.NO_DOSSIER_LIST_BRANDED
        account.enable_button_create = False
        account.enable_feedback_form = False
        account.enable_uploading_files = False
        account.enable_document_upload = False
        account.allow_dossier_listing = False

    else:
        raise ValueError(f"Invalid account key '{account_key}'")

    if default_bucket_name:
        account.default_bucket_name = default_bucket_name

    set_new_account_defaults(account)

    # Standard config for finnova
    account.max_dossier_expiry_duration_days = 500

    # To allow 20 days for final archiving process
    account.default_dossier_expiry_duration_days = 520
    account.valid_dossier_languages = DEFAULT_VALID_DOSSIER_LANGUAGES
    account.valid_ui_languages = DEFAULT_VALID_UI_LANGUAGES
    account.show_document_category_external = False
    account.show_business_case_type = False
    account.enable_virus_scan = False
    account.enable_rendering_hurdles_tab = False
    account.enable_dossier_permission = False
    account.enable_dossier_assignment = False

    # Download will most likely happen via API. That will provide the document_category.
    # No need to put it into the attachment for now
    account.enable_download_metadata_json = False
    account.enable_semantic_document_export = True

    # Ensure that a state machine is created
    created_objects = create_semantic_document_state_machine()

    state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = state_machine

    # This is needed to customize the "Übermittlung an Archiv" button
    account.frontend_theme = FRONTEND_THEME_FINNOVA
    account.enable_semantic_document_export_unknown_documents = False
    account.dossier_close_strategy = (
        DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE
    )
    account.dossier_close_expiry_days = (
        7  # 7 is currently default but we want to make it explicit
    )

    account.dossier_access_check_provider = (
        get_access_check_provider_default_dossier_access_grant()
    )
    account.dossier_access_check_error_component = (
        DossierAccessCheckErrorComponent.FINNOVA_ACCESS_CHECK_INSTRUCTIONS
    )

    account.save()

    return account


# Function to validate if the string is part of the enum
def is_valid_account_key(account_key: str) -> bool:
    try:
        # Attempt to match the account_key with an enum member
        schemas.AccountName(account_key)
        return True
    except ValueError:
        # If account_key is not found in the enum, return False
        return False


def load_finnova_document_categories(account_key):

    # This is the latest doc cat definition that is verified for finnova
    document_categories_json_path = (
        ASSETS_PATH / "document_category/default/DocumentCategory-2025-03-10.json"
    )
    account = Account.objects.get(key=account_key)
    load_initial_document_categories(
        account, document_categories_json_path=document_categories_json_path
    )

    # document_categories_json_path_custom = (
    #     ASSETS_PATH / "document_category/finnova/DocumentCategory-2024-09-25.json"
    # )
    # document_categories_custom, _, _, _ = load_initial_document_categories(
    #     account, document_categories_json_path_custom
    # )

    d = DocumentCategory.objects.get(name="TAX_ATTACHMENTS", account__key=account_key)
    d.exclude_for_recommendation = True
    d.save()


class FinnovaAccountFactoryFaker(DefaultAccountFactoryFaker):
    def __init__(
        self,
        account: Optional[Account] = None,
        account_key: Optional[str] = None,
        default_bucket_name: Optional[str] = None,
    ):
        self.document_categories = None

        if account is None:
            if account_key is None:
                account_key = schemas.AccountName.finnovadev.value
        else:
            account_key = account.key

        account = update_or_create_finnova_account(account_key, default_bucket_name)

        super().__init__(account, "de_CH")

    def load_initial_document_categories(self):
        load_finnova_document_categories(self.account.key)

        self.document_categories = list(
            DocumentCategory.objects.filter(account=self.account).all()
        )

        assert (
            len(self.document_categories) == 264
        ), f"Instead of 264, {len(self.document_categories)} have been found "

        return self.document_categories
