from datetime import <PERSON><PERSON><PERSON>
from typing import Optional

import structlog
from django.conf import settings
from django.db import transaction
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.utils import timezone
from ninja import NinjaAP<PERSON>, Query, Router
from ninja.errors import ValidationError
from ninja.security import HttpBearer
from pydantic import HttpUrl

from clientis.schemas.schemas import api_validate_dossier_external_id_format
from core.schema import Message, Error
from dossier.helpers_api import handle_api_validation_error
from dossier.models import <PERSON><PERSON>r<PERSON><PERSON>, DossierAccessGrant, Dossier, Account
from dossier.services_external import (
    get_dossier_with_access_check_api,
    create_dossier_api,
    check_dossier_ready_for_closing,
    perform_dossier_close,
    create_dossier_close_ready_response,
)
from finnova import schemas
from finnova.models import FinnovaDossierProperties
from finnova.schemas import <PERSON>ssier<PERSON>loseReadyResponse, DossierCloseResponse
from projectconfig.authentication import (
    authenticate_from_account,
    get_user_or_create,
)

logger = structlog.get_logger()


class FinnovaJWTAuth(HttpBearer):
    def authenticate(self, request, token, *args, **kw):
        jwt = authenticate_from_account(token)
        if settings.API_ROLE not in jwt.user_roles:
            return
        return jwt


api = NinjaAPI(
    title="Hypodossier - Finnova API",
    csrf=False,
    auth=FinnovaJWTAuth(),
    urls_namespace="finnova-api",
    version="0.1.0",
    servers=[],
)


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


def create_shared_router():
    router = Router()

    @router.get("/ping", response={200: Message}, url_name="ping", exclude_none=True)
    def ping(request):
        return Message(detail="pong")

    @router.post(
        "/dossier",
        response={201: schemas.Dossier, 409: Error},
        url_name="create-dossier",
        exclude_none=True,
    )
    def create_dossier(request, dossier_create: schemas.CreateDossier):

        # Validate external id matches required schema
        api_validate_dossier_external_id_format(dossier_create.external_dossier_id)

        return create_dossier_api(request, dossier_create)

    @router.get(
        "/dossier/{external_dossier_id}/show",
        response={302: HttpUrl},
        auth=None,
        url_name="show-dossier",
    )
    def show_dossier(
        request,
        external_dossier_id: str,
        lang: Optional[str] = Query(None, description="Language code (de, fr, it, en)"),
    ) -> HttpResponse:
        """
        Redirects to the dossier for the user to view it if it exists or returns a 404
        """
        dossier: Dossier = get_object_or_404(Dossier, external_id=external_dossier_id)
        account: Account = dossier.account
        if lang:
            response_url = (
                f"{account.dmf_endpoint}/dossier/{dossier.uuid}/view/page?lang={lang}"
            )
        else:
            response_url = f"{account.dmf_endpoint}/dossier/{dossier.uuid}/view/page?lang={dossier.lang}"
        return HttpResponseRedirect(redirect_to=response_url)

    @router.patch(
        "/dossier/{external_dossier_id}",
        response={200: schemas.ChangeDossierResponse, 409: Error, 404: Message},
        url_name="update-dossier",
        exclude_none=True,
    )
    def update_dossier(
        request, external_dossier_id: str, dossier_change: schemas.ChangeDossier
    ):
        """
        Updates a new Dossier based on the provided parameters (prpoerties of the dossier which are not available during the creation of the dossier. Partial updates should be possible.
        Propably save the additional properties inside an additional model part of the finnova app
        """

        dossier = get_dossier_with_access_check_api(
            dossier_user=request.auth.get_user_or_create(),
            is_manager=True,
            external_dossier_id=external_dossier_id,
        )

        with transaction.atomic():

            # Update fields if they are provided in the request
            dossier_changed = False
            for field in ["name", "lang"]:
                if (
                    hasattr(dossier_change, field)
                    and getattr(dossier_change, field) is not None
                ):
                    setattr(dossier, field, getattr(dossier_change, field))
                    dossier_changed = True
            if dossier_changed:
                dossier.save()

            finnova_dossier_properties, created = (
                FinnovaDossierProperties.objects.get_or_create(
                    dossier=dossier, defaults={"account": dossier.account}
                )
            )

            # Update fields if they are provided in the request
            for field in [
                "sequence_number",
                "financing_number",
                "client_id",
                "client_key",
            ]:
                if (
                    hasattr(dossier_change, field)
                    and getattr(dossier_change, field) is not None
                ):
                    setattr(
                        finnova_dossier_properties,
                        field,
                        getattr(dossier_change, field),
                    )

            try:
                finnova_dossier_properties.full_clean()
                finnova_dossier_properties.save()
            except ValidationError as e:
                return 409, Error(message=str(e))

            # Refresh the dossier to get the updated data
            dossier.refresh_from_db()

            # Add the Finnova properties to the schema
            response = schemas.ChangeDossierResponse(
                external_dossier_id=dossier.external_id,
                name=dossier.name,
                lang=dossier.lang,
                sequence_number=dossier.finnova_properties.sequence_number,
                financing_number=dossier.finnova_properties.financing_number,
                client_id=dossier.finnova_properties.client_id,
                client_key=dossier.finnova_properties.client_key,
            )

            return 200, response

    @router.delete(
        "/dossier/{external_dossier_id}",
        response={202: Message, 404: Message},
        url_name="dossier-delete",
        exclude_none=True,
    )
    def delete_dossier(request, external_dossier_id):
        """Do we need to cover this use case? When do we delete the dossier?"""
        dossier_user = request.auth.get_user_or_create()

        dossier = get_dossier_with_access_check_api(
            dossier_user=dossier_user,
            is_manager=True,  # Fix this to do proper ownership
            external_dossier_id=external_dossier_id,
        )

        dossier.expiry_date = timezone.now() - timedelta(days=1)
        dossier.save()
        return 202, {"detail": "Dossier Scheduled for deletion"}

    @router.post(
        "/dossier-access-grant",
        response=Message,
        url_name="set-dossier-user-grant",
        exclude_none=True,
        description="Grants temporary access to a dossier for a user. If a datetime in the past is set, then access is revoked",
    )
    @transaction.atomic
    def set_dossier_access_grant(
        request,
        dossier_access_grant: schemas.DossierAccessGrant,
    ):

        api_user = request.auth.get_user_or_create()

        dossier = get_dossier_with_access_check_api(
            dossier_user=api_user,
            is_manager=True,
            external_dossier_id=dossier_access_grant.external_dossier_id,
        )

        # At bcge and finnova the only identifier we get is the username (which is the emailaddress)
        # via access grant schema.
        # Firstname, lastname, email and username is provided via sso.
        dossier_user: DossierUser = get_user_or_create(
            account=dossier.account,
            username=dossier_access_grant.username,
        )

        dossier_access_grant, _ = DossierAccessGrant.objects.update_or_create(
            user=dossier_user.user,
            dossier=dossier,
            defaults={
                "expires_at": dossier_access_grant.expires_at,
                "has_access": True,
                "scope": dossier_access_grant.scope,
            },
        )

        if not dossier_access_grant.has_access:
            # PG: an alternative would be to delete the grant
            return 200, {"detail": "Access revoked"}

        if dossier_access_grant.expires_at < timezone.now():
            return 200, {"detail": "Access expired"}

        return 200, {
            "detail": f"Access granted for {dossier_access_grant.user}, expires at {dossier_access_grant.expires_at}"
        }

    @router.get(
        "/dossier/{external_dossier_id}/check-dossier-close-ready",
        response={200: DossierCloseReadyResponse},
        url_name="check-dossier-close-ready",
        exclude_none=True,
    )
    def check_dossier_close_ready(request, external_dossier_id):
        """
        Check if a dossier is ready to be closed. If not return instructions what the user needs to do
        """
        dossier_user = request.auth.get_user_or_create()

        dossier = get_dossier_with_access_check_api(
            dossier_user=dossier_user,
            is_manager=True,  # Fix this to do proper ownership
            external_dossier_id=external_dossier_id,
        )

        # Validate external id matches required schema
        api_validate_dossier_external_id_format(external_dossier_id)

        stats, evaluation = check_dossier_ready_for_closing(dossier)
        res1 = create_dossier_close_ready_response(stats, evaluation)

        # We need to convert to Client specific pydantic schema
        res2: DossierCloseReadyResponse = DossierCloseReadyResponse.model_validate(
            res1.model_dump()
        )
        return 200, res2

    @router.get(
        "/dossier/{external_dossier_id}/close-dossier",
        response={200: DossierCloseResponse},
        url_name="close-dossier",
        exclude_none=True,
    )
    def close_dossier(request, external_dossier_id: str):
        """
        Check if a dossier can be closed and close it if all requirements are fulfilled.
        Return success (or not). In case it was not successful, instructions for user are returned.
        """
        dossier_user = request.auth.get_user_or_create()

        dossier: Dossier = get_dossier_with_access_check_api(
            dossier_user=dossier_user,
            is_manager=True,
            external_dossier_id=external_dossier_id,
        )

        # Validate external id matches required schema
        api_validate_dossier_external_id_format(external_dossier_id)

        with transaction.atomic():
            success, evaluation = perform_dossier_close(dossier)

            # FINOVA does not use a account.active_work_status_state_machine or Dossier.work_status
            # so we need to set the access_mode to READ_ONLY manually here
            # We will likely migrate the dossier default finite state machine here, as part of a clientis refactor
            if success:
                dossier.access_mode = Dossier.DossierAccessMode.READ_ONLY
                dossier.save()
            ret = DossierCloseResponse(
                success=success,
                msg_nok_de=evaluation.msg_nok_de,
                msg_nok_en=evaluation.msg_nok_en,
                msg_nok_fr=evaluation.msg_nok_fr,
                msg_nok_it=evaluation.msg_nok_it,
            )
            return 200, ret

    return router


api.add_router("", create_shared_router())
