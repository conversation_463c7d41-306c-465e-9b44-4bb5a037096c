import json
import os
import uuid
from typing import List

import pytest
from django.conf import settings

from faker import Faker

from finnova.schemas import Account<PERSON>ame
from core.authentication import AuthenticatedClient, create_token

from dossier.models import (
    Account,
    DocumentCate<PERSON>y,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>rU<PERSON>,
)
from dossier.services import create_expiration_date
from projectconfig.authentication import get_user_or_create
from projectconfig.jwk import load_jwk_from_env
from finnova.factories import FinnovaAccountFactoryFaker
import jwt
from jwcrypto import jwk
from finnova.tests.data import DATA_PATH
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)


@pytest.fixture(scope="session")
def faker():
    return Faker(locale="de_CH")


@pytest.fixture
def finnova_account():
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="finnova development account",
            default_bucket_name="dms-default-bucket",
            dmf_endpoint="https://www.localhost",
        ),
        key=AccountName.finnovadev.value,
    )

    # Ensure that a state machine is created
    created_objects = create_semantic_document_state_machine()

    state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = state_machine
    account.save()

    account.refresh_from_db()
    return account


@pytest.fixture(scope="session")
def mock_jwks_public_private():
    # JWKS with both public and private keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()


@pytest.fixture
def mock_jwks_public():
    # JWKS with only public keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    ).model_dump(exclude_unset=True)


@pytest.fixture
def set_finnova_JWK(finnova_account, mock_jwks_public):
    # Set public key used for finnova authentication
    return JWK.objects.create(jwk=mock_jwks_public["keys"][1], account=finnova_account)


@pytest.fixture(scope="session")
def token_data(faker):
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-finnova-first service-finnova-last",
        "given_name": "service-finnova-first",
        "family_name": "service-finnova-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        # "external_dossier_id": schemas.DossierName(
        #     faker.sentence()
        # )
        # during dossier creation via API parameter
        "user_roles": ["api_role"],
        "account_key": AccountName.finnovadev.value,
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


@pytest.fixture(scope="session")
def mock_token(mock_jwks_public_private, token_data):
    # need a PEM-formatted key
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,
        algorithm="RS256",
    )


@pytest.fixture
def finnova_account_factory(finnova_account):
    return FinnovaAccountFactoryFaker(
        account=finnova_account, default_bucket_name="dms-default-bucket"
    )


@pytest.fixture
def document_categories(finnova_account_factory) -> List[DocumentCategory]:
    doc_cats = finnova_account_factory.load_initial_document_categories()
    return doc_cats


@pytest.fixture(scope="session")
def finnova_authenticated_client(mock_token):
    return AuthenticatedClient(mock_token)


def finnova_get_or_create_default_user(finnova_account):
    return get_user_or_create(
        account=finnova_account.account,
        username="<EMAIL>",
    )


@pytest.fixture
def finnovauser1_client():
    token = create_token(
        "finnova user 1",
        "finlast",
        "<EMAIL>",
        account_key=AccountName.finnovadev.value,
    )

    return AuthenticatedClient(token)


@pytest.fixture
def finnovauser1_user() -> DossierUser:
    return get_user_or_create(
        account=Account.objects.get(key=AccountName.finnovadev.value),
        username="<EMAIL>",
        email="<EMAIL>",
        fname="finnova user 1",
        lname="finlast",
    )


@pytest.fixture
def finnovauser2_user() -> DossierUser:
    return get_user_or_create(
        account=Account.objects.get(key=AccountName.finnovadev.value),
        username="<EMAIL>",
        email="<EMAIL>",
        fname="finnova user 2",
        lname="finlast",
    )


@pytest.fixture(scope="session")
def finnova_miss_signed_authenticated_client(mock_jwks_public_private, token_data):
    """Test auth failure using key signed by finnova"""
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][0]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    wrong_token = jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,  # use wrong key, key [0] from finnova
        algorithm="RS256",
    )
    return AuthenticatedClient(wrong_token)
