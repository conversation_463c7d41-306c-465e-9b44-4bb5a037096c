from django.contrib import admin

from dossier.models import Dossie<PERSON>
from finnova.models import FinnovaDossierProperties


@admin.register(FinnovaDossierProperties)
class FinnovaDossierPropertiesAdmin(admin.ModelAdmin):
    list_display = (
        "account",
        "dossier",
        "sequence_number",
        "financing_number",
        "client_id",
        "client_key",
        "created_at",
        "updated_at",
    )
    list_filter = ("account", "dossier")
    search_fields = (
        "dossier__name",
        "dossier__uuid",
        "uuid",
        "sequence_number",
        "financing_number",
        "client_id",
        "client_key",
    )
    readonly_fields = ("created_at", "updated_at")

    raw_id_fields = ["dossier"]

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "dossier":
            kwargs["queryset"] = Dossier.objects.all().order_by("name")
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("account", "dossier")
