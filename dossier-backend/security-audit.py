import json
import logging
import structlog
import subprocess
from typing import List

logger = structlog.get_logger()


def analyse_issues(exclude_dependencies: List[str] = []):
    version_result = subprocess.run(
        ["pip-audit", "-V"],
        capture_output=True,
        text=True,
    )
    print(f"This shows the version of pip-audit: {version_result.stdout}")
    audit_result = subprocess.run(
        ["pip-audit", "-f", "json", "--progress-spinner", "off"],
        capture_output=True,
        text=True,
    )

    vulns_count = 0
    try:
        feedback = json.loads(audit_result.stdout)
        if feedback.get("dependencies"):
            for dependency in feedback.get("dependencies"):
                name = dependency.get("name")
                vulns = dependency.get("vulns")
                if name in exclude_dependencies:
                    logger.warning(
                        f"exclude dependency {name} because it is in the list of excluded dependencies"
                    )
                    continue
                if vulns:
                    vulns_count += 1
                    logger.warning(dependency)

            if vulns_count > 0:
                logger.warning(f"found {vulns_count} issues")
                exit(1)
        exit(0)
    except Exception:
        logger.error(
            f"Unexpected feedback from pip-audit: stdout={audit_result.stdout}, stderr={audit_result}",
            exc_info=True,
        )
        exit(1)

    exit(1)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    exclude_dependencies = [
        "actorizer",
    ]
    analyse_issues(exclude_dependencies)
