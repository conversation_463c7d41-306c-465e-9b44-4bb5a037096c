import base64
import os

import httpx
import structlog
from dotenv import load_dotenv
from minio.helpers import md5sum_hash
from sanic import Sanic, Request, HTTPResponse

logger = structlog.get_logger()

load_dotenv()

app = Sanic("hys3proxy")

S3_ENDPOINT = os.getenv("S3_ENDPOINT")
S3_ENDPOINT_PROXY = os.getenv("S3_ENDPOINT_PROXY")
S3_SSE_C_B64 = os.getenv("S3_SSE_C_B64")
S3_SSE_C_MD5_B64 = md5sum_hash(base64.b64decode(S3_SSE_C_B64))
ALLOWED_ORIGINS = os.getenv("DJANGO_CORS_ORIGIN_WHITELIST").split(" ")


@app.get("/<objectpath:path>", stream=True)
async def handle_get(request: Request, objectpath: str):
    upstream_url = request.url.replace(S3_ENDPOINT_PROXY, S3_ENDPOINT).replace(
        "http", "https"
    )

    await logger.ainfo("trying to upload", upstream_url=upstream_url)

    cors_headers = {}
    origin = request.headers.get("origin")
    if origin in ALLOWED_ORIGINS:
        cors_headers = {
            "Access-Control-Allow-Origin": request.headers.get("origin"),
            "Access-Control-Allow-Methods": "GET,OPTIONS",
        }
    async with httpx.AsyncClient(http2=True) as client:
        async with client.stream(
            "GET",
            url=upstream_url,
            headers={
                "x-amz-server-side-encryption-customer-algorithm": "AES256",
                "x-amz-server-side-encryption-customer-key": S3_SSE_C_B64,
                "x-amz-server-side-encryption-customer-key-MD5": S3_SSE_C_MD5_B64,
            },
        ) as up_res:
            if up_res.is_success:
                down_res = await request.respond(
                    content_type=up_res.headers.get("Content-Type"),
                    headers={
                        **cors_headers,
                        "last-modified": up_res.headers.get("last-modified"),
                        "etag": up_res.headers.get("etag"),
                        "Cache-Control": "max-age=86400",
                    },
                )
                async for chunk in up_res.aiter_raw():
                    await down_res.send(chunk)
                await down_res.eof()
            else:
                logger.warning(
                    "Could not download file from upstream",
                    upstream_url=upstream_url,
                    request_url=request.url,
                    status=up_res.status_code,
                    content=await up_res.aread(),
                )
                down_res = await request.respond(status=up_res.status_code)
                await down_res.eof()


@app.put("/<objectpath:path>", stream=True)
async def handle_put(request: Request, objectpath: str):
    upstream_url = request.url.replace(S3_ENDPOINT_PROXY, S3_ENDPOINT).replace(
        "http", "https"
    )

    async with httpx.AsyncClient() as client:
        try:
            up_res = await client.put(
                upstream_url,
                headers={
                    "x-amz-server-side-encryption-customer-algorithm": "AES256",
                    "x-amz-server-side-encryption-customer-key": S3_SSE_C_B64,
                    "x-amz-server-side-encryption-customer-key-MD5": S3_SSE_C_MD5_B64,
                    "Content-Length": request.headers.get("Content-Length"),
                },
                content=request.stream,
            )
            up_res.raise_for_status()
        except:
            await logger.aexception(
                "Issue during upstream upload",
                upstream_url=upstream_url,
                status_code=up_res.status_code,
                content=up_res.content,
            )
    return HTTPResponse(
        status=up_res.status_code,
        headers=up_res.headers,
        content_type=up_res.headers.get("Content-Type"),
    )


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8888, access_log=False, fast=True)
