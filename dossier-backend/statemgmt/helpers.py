from functools import cmp_to_key

from statemgmt.models import Status, TransitionPrecondition, StateCondition


def to_state_order_sort(a, b):
    a_to_state_order = a["to_state"]["order"]
    b_to_state_order = b["to_state"]["order"]

    if a_to_state_order > b_to_state_order:
        return 1
    elif a_to_state_order == b_to_state_order:
        return 0
    else:
        return -1


def prepare_for_api_state_transitions(state_transitions):
    data = []

    for state_transition in state_transitions:
        from_state = Status.objects.get(uuid=state_transition.from_state_id)
        to_state = Status.objects.get(uuid=state_transition.to_state_id)

        transition_preconditions = TransitionPrecondition.objects.filter(
            transition=state_transition
        )

        checked_conditions = []

        for transition_precondition in transition_preconditions:
            condition = StateCondition.objects.get(
                uuid=transition_precondition.condition_id
            )
            checked_conditions.append(
                {
                    "uuid": transition_precondition.uuid,
                    "show_anyway": transition_precondition.show_anyway,
                    "overrideable": transition_precondition.overrideable,
                    "warning_de": transition_precondition.warning_de,
                    "warning_en": transition_precondition.warning_en,
                    "warning_fr": transition_precondition.warning_fr,
                    "warning_it": transition_precondition.warning_it,
                    "condition": condition.name,
                }
            )
        data.append(
            {
                "uuid": state_transition.uuid,
                "from_state": {
                    "uuid": from_state.uuid,
                    "key": from_state.key,
                    "name_de": from_state.name_de,
                    "name_en": from_state.name_en,
                    "name_fr": from_state.name_fr,
                    "name_it": from_state.name_it,
                    "color": from_state.color,
                    "order": from_state.order,
                },
                "to_state": {
                    "uuid": to_state.uuid,
                    "key": to_state.key,
                    "name_de": to_state.name_de,
                    "name_en": to_state.name_en,
                    "name_fr": to_state.name_fr,
                    "name_it": to_state.name_it,
                    "color": to_state.color,
                    "order": to_state.order,
                },
                "checked_conditions": checked_conditions,
            }
        )

    key_for_sorting = cmp_to_key(to_state_order_sort)
    data_sorted = sorted(data, key=key_for_sorting)

    return data_sorted
