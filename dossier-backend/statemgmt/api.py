from typing import List, Any
from uuid import UUID

from deprecation import deprecated
from django.shortcuts import get_object_or_404
from ninja import Router

from statemgmt import helpers as statemgmt_helpers
from statemgmt import schemas
from statemgmt.models import Status, StateTransition, StateMachine
from statemgmt.services import map_status

statemgmt = Router()


@statemgmt.get("/{state_machine_uuid}/statuses", response=List[schemas.Status])
def get_all_statuses_by_state_machine(request, state_machine_uuid: UUID):
    state_machine = get_object_or_404(StateMachine, uuid=state_machine_uuid)
    statuses = Status.objects.filter(state_machine=state_machine)
    return [map_status(status) for status in statuses]


@statemgmt.get("/statuses", response=List[schemas.Status])
@deprecated(details="use /{state_machine_uuid}/statuses endpoint")
def get_all_statuses(request):
    work_status_id = request.auth.account.active_work_status_state_machine

    statuses = Status.objects.filter(state_machine=work_status_id)

    return [map_status(status) for status in statuses]


@statemgmt.get("/state_transitions/{status_uuid}", response=Any)
def get_state_transitions(request, status_uuid: UUID):
    state_transitions = StateTransition.objects.filter(from_state_id=status_uuid)

    return statemgmt_helpers.prepare_for_api_state_transitions(state_transitions)
