from adminsortable.fields import Sortable<PERSON><PERSON><PERSON><PERSON><PERSON>
from adminsortable.models import SortableMixin
from colorfield.fields import <PERSON>Field
from django.db import models
from django.db.models import CASCADE, SET_NULL
from django.utils.html import format_html
from plantuml import PlantUML

from core.behaviors import Timestampable


class StateMachine(Timestampable):
    name = models.CharField(max_length=255, unique=True)

    start_status = models.ForeignKey(
        "Status", on_delete=SET_NULL, blank=True, null=True
    )

    def plantuml(self):
        states = Status.objects.filter(state_machine_id=self.uuid).all()
        transitions = StateTransition.objects.filter(state_machine_id=self.uuid).all()

        state_string = "".join(
            [
                f'state "{state.name_en}" as {state.key} {state.color} \n'
                for state in states
            ]
        )
        transition_string = "".join(
            [
                f"{transition.from_state.key} --> {transition.to_state.key} : [{' & '.join([condition.condition.name for condition in transition.preconditions.all()])}]\n"
                for transition in transitions
            ]
        )
        content = f"""
        @startuml
        {state_string}

        {transition_string}
        @enduml
        """
        url = PlantUML(url="http://www.plantuml.com/plantuml/img/").get_url(content)
        return format_html(f'<a href="{url}" >PlantUML</a>')

    def __str__(self):
        return self.name


class StateCondition(Timestampable, SortableMixin):
    class Meta:
        ordering = ["order"]

    order = models.PositiveIntegerField(default=0, db_index=True, editable=False)

    name = models.CharField(max_length=255, unique=True)

    # CONDITION_LEVEL = ("WARNING", "ERROR")

    # level = models.TextChoices
    # resolution_hint_de = models.TextField()

    def __str__(self):
        return self.name


class Status(Timestampable, SortableMixin):
    class Meta:
        ordering = ["order"]

        constraints = [
            models.UniqueConstraint(
                fields=["state_machine", "key"], name="unique_statemgmt_status"
            ),
        ]
        verbose_name_plural = "Status"

    state_machine = SortableForeignKey(StateMachine, on_delete=CASCADE)

    key = models.CharField(max_length=255)

    name_de = models.CharField(max_length=255, blank=True, null=True)
    description_de = models.TextField(blank=True, null=True)
    name_fr = models.CharField(max_length=255, blank=True, null=True)
    description_fr = models.TextField(blank=True, null=True)
    name_it = models.CharField(max_length=255, blank=True, null=True)
    description_it = models.TextField(blank=True, null=True)
    name_en = models.CharField(max_length=255, blank=True, null=True)
    description_en = models.TextField(blank=True, null=True)

    color = ColorField(default="#FF0000")

    order = models.PositiveIntegerField(default=0, editable=False, db_index=True)

    def __str__(self):
        return f"{self.state_machine}/{self.key}"


class StateTransition(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["state_machine", "from_state", "to_state"],
                name="unique_state_transitions",
            ),
        ]

    state_machine = models.ForeignKey(
        StateMachine, on_delete=CASCADE, related_name="transitions"
    )
    from_state = models.ForeignKey(
        Status, on_delete=CASCADE, related_name="from_states", blank=True, null=True
    )
    to_state = models.ForeignKey(
        Status, on_delete=CASCADE, related_name="to_states", blank=True, null=True
    )


class TransitionPrecondition(Timestampable):
    class Meta:
        ordering = ("overrideable",)

    transition = models.ForeignKey(
        StateTransition, on_delete=CASCADE, related_name="preconditions"
    )
    condition = models.ForeignKey(
        StateCondition, on_delete=CASCADE, related_name="preconditions"
    )

    show_anyway = models.BooleanField(default=False)
    overrideable = models.BooleanField(default=False)

    warning_title_de = models.TextField(blank=True, null=True)
    warning_title_en = models.TextField(blank=True, null=True)
    warning_title_fr = models.TextField(blank=True, null=True)
    warning_title_it = models.TextField(blank=True, null=True)

    warning_de = models.TextField(blank=True, null=True)
    warning_en = models.TextField(blank=True, null=True)
    warning_fr = models.TextField(blank=True, null=True)
    warning_it = models.TextField(blank=True, null=True)
