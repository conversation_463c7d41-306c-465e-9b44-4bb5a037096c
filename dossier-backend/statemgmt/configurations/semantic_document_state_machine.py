from enum import Enum
from typing import Dict, TypedDict, List

from django.db import transaction

from statemgmt.models import (
    StateMachine,
    Status,
    StateCondition,
    StateTransition,
    TransitionPrecondition,
)


class CreatedStateMgmtObjects(TypedDict):
    machine: StateMachine
    states: Dict[str, Status]
    conditions: Dict[str, StateCondition]
    transitions: Dict[str, StateTransition]
    preconditions: List[TransitionPrecondition]


class SemanticDocumentState(str, Enum):
    IN_FRONT_OFFICE = "IN_FRONT_OFFICE"
    READY_FOR_EXPORT = "READY_FOR_EXPORT"
    EXPORT_AVAILABLE = "EXPORT_AVAILABLE"
    EXPORT_ERROR = "EXPORT_ERROR"
    EXPORT_DONE = "EXPORT_DONE"
    EXPORT_IN_PROGRESS = "EXPORT_IN_PROGRESS"


STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS = "SEMANTIC_DOCUMENT_WORK_STATUS"


def create_context_for_semantic_document_state_transition(is_system: bool = True):
    """
    Helper function for creating the context. Use this for all transitions of semantic documents.
    If new context parameters are needed, add them here
    @return:
    """
    return dict(
        is_user=not is_system,
        is_system=is_system,
        is_true=True,
        is_false=False,
    )


@transaction.atomic
def create_semantic_document_state_machine() -> CreatedStateMgmtObjects:
    # Create a dictionary to return all created objects
    created_objects = {
        "machine": None,
        "states": {},
        "conditions": {},
        "transitions": {},
        "preconditions": [],
    }

    # Create StateMachine
    created_objects["machine"], _ = StateMachine.objects.get_or_create(
        name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS
    )
    # Create States
    state_keys = [state.value for state in SemanticDocumentState]
    state_details = {
        SemanticDocumentState.IN_FRONT_OFFICE.value: {
            "name_de": "Dokument im Frontoffice",
            "description_de": "Dokument steht im Frontoffice zur Verfügung.",
            "name_fr": "Document dans le front office",
            "description_fr": "Le document est disponible dans le front office.",
            "name_it": "Documento in Front Office",
            "description_it": "Il documento è disponibile nel Front Office.",
            "name_en": "Document in Front Office",
            "description_en": "Document is available in the front office.",
        },
        SemanticDocumentState.READY_FOR_EXPORT.value: {
            "name_de": "Bereit für den Export",
            "description_de": "Dokument ist bereit für den Export.",
            "name_fr": "Prêt pour l'exportation",
            "description_fr": "Le document est prêt à être exporté.",
            "name_it": "Pronto per l'esportazione",
            "description_it": "Il documento è pronto per essere esportato.",
            "name_en": "Ready for Export",
            "description_en": "Document is ready for export.",
        },
        SemanticDocumentState.EXPORT_AVAILABLE.value: {
            "name_de": "Export verfügbar",
            "description_de": "Der Export des Dokuments ist verfügbar.",
            "name_fr": "Exportation disponible",
            "description_fr": "L'exportation du document est disponible.",
            "name_it": "Esportazione disponibile",
            "description_it": "L'esportazione del documento è disponibile.",
            "name_en": "Export Available",
            "description_en": "The document export is available.",
        },
        SemanticDocumentState.EXPORT_IN_PROGRESS.value: {
            "name_de": "Export in Bearbeitung",
            "description_de": "Der Export des Dokuments wird durchgeführt.",
            "name_fr": "Exportation en cours",
            "description_fr": "L'exportation du document est en cours.",
            "name_it": "Esportazione in corso",
            "description_it": "L'esportazione del documento è in corso.",
            "name_en": "Export in Progress",
            "description_en": "The document export is in progress.",
        },
        SemanticDocumentState.EXPORT_ERROR.value: {
            "name_de": "Fehler beim Export",
            "description_de": "Ein Fehler ist beim Export aufgetreten.",
            "name_fr": "Erreur d'exportation",
            "description_fr": "Une erreur s'est produite lors de l'exportation.",
            "name_it": "Errore di esportazione",
            "description_it": "Si è verificato un errore durante l'esportazione.",
            "name_en": "Export Error",
            "description_en": "An error occurred during export.",
        },
        SemanticDocumentState.EXPORT_DONE.value: {
            "name_de": "Export abgeschlossen",
            "description_de": "Der Export des Dokuments wurde abgeschlossen.",
            "name_fr": "Exportation terminée",
            "description_fr": "L'exportation du document est terminée.",
            "name_it": "Esportazione completata",
            "description_it": "L'esportazione del documento è completata.",
            "name_en": "Export Completed",
            "description_en": "The document export has been completed.",
        },
    }

    # Loop through state keys and create or update Status objects
    for key in state_keys:
        # Get or create the Status object
        status_obj, created = Status.objects.get_or_create(
            key=key, state_machine=created_objects["machine"]
        )

        # Update the fields with the corresponding values from state_details
        status_obj.name_de = state_details[key]["name_de"]
        status_obj.description_de = state_details[key]["description_de"]
        status_obj.name_fr = state_details[key]["name_fr"]
        status_obj.description_fr = state_details[key]["description_fr"]
        status_obj.name_it = state_details[key]["name_it"]
        status_obj.description_it = state_details[key]["description_it"]
        status_obj.name_en = state_details[key]["name_en"]
        status_obj.description_en = state_details[key]["description_en"]

        # Save the updated Status object
        status_obj.save()

        # Store the object in the created_objects dictionary
        created_objects["states"][key] = status_obj

    # Create Conditions
    # condition_names = [
    #     "IN_FRONT_OFFICE",
    #     "READY_FOR_EXPORT",
    #     "EXPORT_AVAILABLE",
    #     "EXPORT_DONE",
    #     "EXPORT_ERROR",
    # ]
    # for name in condition_names:
    #     created_objects["conditions"][name], _ = StateCondition.objects.get_or_create(
    #         name=name
    #     )

    # Create Transitions
    transitions = [
        (
            SemanticDocumentState.IN_FRONT_OFFICE.value,
            SemanticDocumentState.READY_FOR_EXPORT.value,
        ),
        (
            SemanticDocumentState.READY_FOR_EXPORT.value,
            SemanticDocumentState.EXPORT_AVAILABLE.value,
        ),
        # Keep EXPORT_AVAILABLE -> Done, so we don't explicitly need to set to IN_PROGRESS
        (
            SemanticDocumentState.EXPORT_AVAILABLE.value,
            SemanticDocumentState.EXPORT_DONE.value,
        ),
        # Also offer route EXPORT_AVAILABLE -> IN_PROGRESS -> DONE
        (
            SemanticDocumentState.EXPORT_AVAILABLE.value,
            SemanticDocumentState.EXPORT_IN_PROGRESS.value,
        ),
        (
            SemanticDocumentState.EXPORT_IN_PROGRESS.value,
            SemanticDocumentState.EXPORT_DONE.value,
        ),
        (
            SemanticDocumentState.EXPORT_IN_PROGRESS.value,
            SemanticDocumentState.EXPORT_ERROR.value,
        ),
        (
            SemanticDocumentState.EXPORT_AVAILABLE.value,
            SemanticDocumentState.EXPORT_ERROR.value,
        ),
        # On error, allow for setting to export done
        (
            SemanticDocumentState.EXPORT_ERROR.value,
            SemanticDocumentState.EXPORT_DONE.value,
        ),
    ]
    for from_state, to_state in transitions:
        key = f"{from_state}_to_{to_state}"
        created_objects["transitions"][key], _ = StateTransition.objects.get_or_create(
            from_state=created_objects["states"][from_state],
            to_state=created_objects["states"][to_state],
            state_machine=created_objects["machine"],
        )

    # Finally set initial state of state machine
    created_objects["machine"].start_status = created_objects["states"][
        SemanticDocumentState.IN_FRONT_OFFICE.value
    ]

    created_objects["machine"].save()

    return created_objects
