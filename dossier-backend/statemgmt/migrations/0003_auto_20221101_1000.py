# Generated by Django 3.2.16 on 2022-11-01 09:00

import adminsortable.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('statemgmt', '0002_load_intial_data'),
    ]

    operations = [
        migrations.AlterField(
            model_name='statetransition',
            name='state_machine',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transitions', to='statemgmt.statemachine'),
        ),
        migrations.AlterField(
            model_name='status',
            name='state_machine',
            field=adminsortable.fields.SortableForeignKey(on_delete=django.db.models.deletion.CASCADE, to='statemgmt.statemachine'),
        ),
        migrations.AddConstraint(
            model_name='statetransition',
            constraint=models.UniqueConstraint(fields=('state_machine', 'from_state', 'to_state'), name='unique_state_transitions'),
        ),
    ]
