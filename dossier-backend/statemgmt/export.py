import structlog
from pathlib import Path
from typing import List, Optional
from uuid import UUID

from django.db import transaction
from pydantic import BaseModel

from statemgmt.models import (
    StateMachine,
    StateCondition,
    Status,
    StateTransition,
    TransitionPrecondition,
)

logger = structlog.get_logger()


class StateMachineExport(BaseModel):
    uuid: UUID
    name: str
    start_status_id: Optional[UUID] = None


class StateConditionExport(BaseModel):
    uuid: UUID
    name: str


class StatusExport(BaseModel):
    uuid: UUID
    state_machine_id: Optional[UUID] = None
    key: str
    name_de: Optional[str] = None
    description_de: Optional[str] = None
    name_en: Optional[str] = None
    description_en: Optional[str] = None
    name_fr: Optional[str] = None
    description_fr: Optional[str] = None
    name_it: Optional[str] = None
    description_it: Optional[str] = None
    color: Optional[str] = None
    order: Optional[int] = None


class StateTransitionExport(BaseModel):
    uuid: UUID
    state_machine_id: UUID
    from_state_id: Optional[UUID] = None
    to_state_id: Optional[UUID] = None


class TransitionPreconditionExport(BaseModel):
    uuid: UUID
    transition_id: UUID
    condition_id: UUID
    show_anyway: bool
    overrideable: bool
    warning_title_de: Optional[str] = None
    warning_title_en: Optional[str] = None
    warning_title_fr: Optional[str] = None
    warning_title_it: Optional[str] = None
    warning_de: Optional[str] = None
    warning_en: Optional[str] = None
    warning_fr: Optional[str] = None
    warning_it: Optional[str] = None


class StateMachineDetailExport(BaseModel):
    state_machine: StateMachineExport
    state_condition: List[StateConditionExport]
    status: List[StatusExport]
    state_transition: List[StateTransitionExport]
    transition_precondition: List[TransitionPreconditionExport]


def dump_state_machine(state_machine_name: str, dest_path: Path):
    state_machine_detail_export = create_export(state_machine_name)
    dest_path.write_text(state_machine_detail_export.model_dump_json())


def create_export(state_machine_name):
    state_machine = StateMachine.objects.get(name=state_machine_name)
    state_machine_export = StateMachineExport(
        uuid=state_machine.uuid,
        name=state_machine_name,
        start_status_id=(
            state_machine.start_status.uuid if state_machine.start_status else None
        ),
    )
    state_conditions = StateCondition.objects.filter(
        preconditions__transition__state_machine=state_machine
    ).distinct()
    state_conditions_export = [
        StateConditionExport(uuid=state_condition.uuid, name=state_condition.name)
        for state_condition in state_conditions
    ]
    status = Status.objects.filter(state_machine__name=state_machine_name)
    status_export = [
        StatusExport(
            uuid=status.uuid,
            state_machine_id=status.state_machine.uuid,
            key=status.key,
            name_de=status.name_de,
            description_de=status.description_de,
            name_en=status.name_en,
            description_en=status.description_en,
            name_fr=status.name_fr,
            description_fr=status.description_fr,
            name_it=status.name_it,
            description_it=status.description_it,
            color=status.color,
            order=status.order,
        )
        for status in status
    ]
    state_transitions = StateTransition.objects.filter(
        state_machine__name=state_machine_name
    )
    state_transitions_export = [
        StateTransitionExport(
            uuid=state_transition.uuid,
            state_machine_id=state_transition.state_machine.uuid,
            from_state_id=state_transition.from_state.uuid,
            to_state_id=state_transition.to_state.uuid,
        )
        for state_transition in state_transitions
    ]
    transition_preconditions = TransitionPrecondition.objects.filter(
        transition__in=state_transitions
    )
    transition_preconditions_export = [
        TransitionPreconditionExport(
            uuid=transition_precondition.uuid,
            transition_id=transition_precondition.transition.uuid,
            condition_id=transition_precondition.condition.uuid,
            show_anyway=transition_precondition.show_anyway,
            overrideable=transition_precondition.overrideable,
            warning_title_de=transition_precondition.warning_title_de,
            warning_title_en=transition_precondition.warning_title_en,
            warning_title_fr=transition_precondition.warning_title_fr,
            warning_title_it=transition_precondition.warning_title_it,
            warning_de=transition_precondition.warning_de,
            warning_en=transition_precondition.warning_en,
            warning_fr=transition_precondition.warning_fr,
            warning_it=transition_precondition.warning_it,
        )
        for transition_precondition in sorted(
            transition_preconditions, key=lambda x: x.uuid
        )
    ]
    state_machine_detail_export = StateMachineDetailExport(
        state_machine=state_machine_export,
        state_condition=state_conditions_export,
        status=status_export,
        state_transition=state_transitions_export,
        transition_precondition=transition_preconditions_export,
    )
    return state_machine_detail_export


def update_state_machine(
    state_machine_json_path: Path, state_machine_name: str = None
) -> StateMachine:
    with transaction.atomic():
        state_machine_json_path = Path(state_machine_json_path)
        if not state_machine_json_path.exists():
            raise Exception(
                f"path does not exist: {state_machine_json_path}. Current path: {Path().absolute()}"
            )
        if state_machine_name:
            logger.info(
                f"updating state machine from {state_machine_json_path} using state machine name {state_machine_name}"
            )
        else:
            logger.info(
                f"updating state machine from {state_machine_json_path} using state machine provided in this file"
            )

        export = StateMachineDetailExport.model_validate_json(
            state_machine_json_path.read_text()
        )

        logger.info(
            f"Found in export: {len(export.status)} status, {len(export.state_transition)} state transitions, {len(export.state_condition)} state conditions, {len(export.transition_precondition)} transition preconditions..."
        )

        state_machine, created = StateMachine.objects.get_or_create(
            name=state_machine_name or export.state_machine.name
        )
        logger.info(
            f"Working on state_machine '{state_machine.name}'. Created={created}..."
        )

        conditions = {}
        for state_condition in export.state_condition:
            conditions[state_condition.uuid], created = (
                StateCondition.objects.get_or_create(name=state_condition.name)
            )
            if created:
                logger.info(f"Created state condition {state_condition.name}")

        states = {}
        for status in export.status:
            status_defaults = status.model_dump()
            del status_defaults["state_machine_id"]
            del status_defaults["uuid"]
            del status_defaults["key"]

            states[status.uuid], created = Status.objects.update_or_create(
                status_defaults, state_machine=state_machine, key=status.key
            )
            if created:
                logger.info(f"Created status {states[status.uuid].key}")

        state_machine.start_status = (
            states[export.state_machine.start_status_id]
            if export.state_machine.start_status_id
            else None
        )
        logger.info(f"Start status is '{state_machine.start_status}'")
        state_machine.save()

        state_transitions = {}
        for state_transition in export.state_transition:
            (
                state_transitions[state_transition.uuid],
                created,
            ) = StateTransition.objects.get_or_create(
                state_machine=state_machine,
                from_state=states[state_transition.from_state_id],
                to_state=states[state_transition.to_state_id],
            )
            if created:
                logger.info(f"Created state transition '{state_transition}'")

        preconditions = []
        for transition_precondition in export.transition_precondition:
            defaults = transition_precondition.model_dump()
            del defaults["transition_id"]
            del defaults["condition_id"]
            del defaults["uuid"]

            precondition, created = TransitionPrecondition.objects.update_or_create(
                defaults,
                transition=state_transitions[transition_precondition.transition_id],
                condition=conditions[transition_precondition.condition_id],
            )
            preconditions.append(precondition)
            if created:
                logger.info(f"Created state precondition '{precondition}'")

    return state_machine
