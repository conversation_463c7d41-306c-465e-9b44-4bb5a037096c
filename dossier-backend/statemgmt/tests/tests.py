import structlog

import pytest
from django.urls import reverse
from pytest_django.asserts import assertContains

from statemgmt import schemas
from statemgmt.models import (
    Status,
    StateMachine,
    StateTransition,
    StateCondition,
    TransitionPrecondition,
)
from statemgmt.services import (
    calculate_next_possible_states,
    map_condition,
    validate_state_transition,
    map_status,
    StateTransitionError,
)

logger = structlog.get_logger()

app = "statemgmt"


def test_admin_interface_works(admin_client):
    change_lists = ["statemachine", "statecondition", "status", "statetransition"]

    for model in change_lists:
        print(model)
        url = reverse(f"admin:{app}_{model}_changelist")
        assert admin_client.get(url).status_code == 200

        url = reverse(f"admin:{app}_{model}_add")
        assert admin_client.get(url).status_code == 200

        # url = reverse(f"admin:{app}_{model}_change", kwargs={'object_id': '1699513f-a125-4d4c-9804-9354426b277c'})
        # assert admin_client.get(url).status_code == 200


def test_plantuml_url_is_in_admin(admin_client):
    url = reverse(f"admin:{app}_statemachine_changelist")
    response = admin_client.get(url)
    assertContains(response, "http://www.plantuml.com/plantuml")


def test_simple_state_machine(db):
    machine = StateMachine.objects.create(name="test machine")
    assert machine.uuid

    state1 = Status.objects.create(
        key="State1", name_de="Name de", state_machine=machine
    )
    state2 = Status.objects.create(
        key="State2", name_de="Name de", state_machine=machine
    )

    transition = StateTransition.objects.create(
        state_machine=machine, from_state=state1, to_state=state2
    )
    condition, _ = StateCondition.objects.get_or_create(name="123")
    assert condition.uuid

    TransitionPrecondition.objects.create(transition=transition, condition=condition)


def test_simple_transition_without_condition(db):
    machine = StateMachine.objects.create(name="My test state machine")
    state1 = Status.objects.create(key="State 1", state_machine=machine)
    state2 = Status.objects.create(key="State 2", state_machine=machine)

    transition = StateTransition.objects.create(
        from_state=state1, to_state=state2, state_machine=machine
    )

    assert calculate_next_possible_states({}, state1) == [
        schemas.PossibleTransition(
            uuid=transition.uuid,
            from_state=map_status(state1),
            to_state=map_status(state2),
            checked_conditions=[],
        )
    ]


def test_transition_with_conditions(db):
    machine = StateMachine.objects.create(name="My test state machine")
    state1 = Status.objects.create(key="State 1", state_machine=machine)
    state2 = Status.objects.create(key="State 2", state_machine=machine)
    state3 = Status.objects.create(key="State 3", state_machine=machine)
    state4 = Status.objects.create(key="State 4", state_machine=machine)

    # fulfilled
    condition1, _ = StateCondition.objects.get_or_create(name="is_user")
    transition1 = StateTransition.objects.create(
        from_state=state1, to_state=state2, state_machine=machine
    )
    precondition1 = TransitionPrecondition.objects.create(
        transition=transition1, condition=condition1
    )

    # not fulfilled
    condition2, _ = StateCondition.objects.get_or_create(name="is_system")
    transition2 = StateTransition.objects.create(
        from_state=state1, to_state=state3, state_machine=machine
    )
    TransitionPrecondition.objects.create(transition=transition2, condition=condition2)

    # not fulfilled, but show anyway
    transition3 = StateTransition.objects.create(
        from_state=state1, to_state=state4, state_machine=machine
    )
    precondition3 = TransitionPrecondition.objects.create(
        transition=transition3, condition=condition2, show_anyway=True
    )

    assert (
        schemas.PossibleTransitions(
            transitions=calculate_next_possible_states(
                dict(is_user=True, is_system=False), state1
            )
        ).model_dump()
        == schemas.PossibleTransitions(
            transitions=[
                schemas.PossibleTransition(
                    uuid=transition1.uuid,
                    from_state=map_status(state1),
                    to_state=map_status(state2),
                    checked_conditions=[map_condition(precondition1, True)],
                ),
                schemas.PossibleTransition(
                    uuid=transition3.uuid,
                    from_state=map_status(state1),
                    to_state=map_status(state4),
                    checked_conditions=[map_condition(precondition3, False)],
                ),
            ]
        ).model_dump()
    )


def test_validate_state_transition(db):
    machine = StateMachine.objects.create(name="My test state machine")
    state1 = Status.objects.create(key="State 1", state_machine=machine)
    state2 = Status.objects.create(key="State 2", state_machine=machine)
    state3 = Status.objects.create(key="State 3", state_machine=machine)
    state4 = Status.objects.create(key="State 4", state_machine=machine)
    state5 = Status.objects.create(key="State 5", state_machine=machine)

    # fulfilled
    condition1, _ = StateCondition.objects.get_or_create(name="is_user")
    transition1 = StateTransition.objects.create(
        from_state=state1, to_state=state2, state_machine=machine
    )
    TransitionPrecondition.objects.create(transition=transition1, condition=condition1)

    # not fulfilled
    condition2, _ = StateCondition.objects.get_or_create(name="is_system")
    transition2 = StateTransition.objects.create(
        from_state=state1, to_state=state3, state_machine=machine
    )
    TransitionPrecondition.objects.create(transition=transition2, condition=condition2)

    # not fulfilled, but show anyway
    transition3 = StateTransition.objects.create(
        from_state=state1, to_state=state4, state_machine=machine
    )
    TransitionPrecondition.objects.create(
        transition=transition3, condition=condition2, show_anyway=True
    )

    # not fulfilled, but show anyway, overridable
    transition4 = StateTransition.objects.create(
        from_state=state1, to_state=state5, state_machine=machine
    )
    TransitionPrecondition.objects.create(
        transition=transition4,
        condition=condition2,
        show_anyway=True,
        overrideable=True,
    )

    context = dict(is_user=True, is_system=False)
    validate_state_transition(context, state1, state2)

    # there is no need to change anything
    with pytest.raises(StateTransitionError):
        validate_state_transition(context, state1, state1)

    # there is no transition
    with pytest.raises(StateTransitionError):
        validate_state_transition(context, state2, state3)

    with pytest.raises(StateTransitionError):
        validate_state_transition(context, state1, state4)

    validate_state_transition(context, state1, state5)
