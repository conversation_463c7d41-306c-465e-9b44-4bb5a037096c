import json
import structlog
from pathlib import Path

import pytest
from django.core.management import call_command

from core.temporary_path import temporary_path
from statemgmt.export import (
    dump_state_machine,
    update_state_machine,
    create_export,
)
from statemgmt.models import StateMachine

logger = structlog.get_logger()


@pytest.fixture
def load_statemgmt(django_db_blocker):
    # StateMachine.objects.all().delete()
    # StateCondition.objects.all().delete()
    with django_db_blocker.unblock():
        call_command("loaddata", "initial.json", app="statemgmt")


def test_dump_bekb_state_machine(db):
    with temporary_path() as path:
        # dest_path = Path(__file__).parent / f"data/bekb_state_machine.json"
        dest_path = path / "dump.json"
        dump_state_machine("Dossier Status BEKB Initial", dest_path)
        expected_file_path = Path(__file__).parent / "data/bekb_state_machine.json"

        actual_dict = json.loads(dest_path.read_text())
        expected_dict = json.loads(expected_file_path.read_text())

        # Sort them because state_condition is a dict and the order of entries can be random
        # on loading and this would result in a flaky test
        actual_dict["state_condition"] = sorted(
            actual_dict["state_condition"], key=lambda x: x["name"]
        )
        expected_dict["state_condition"] = sorted(
            expected_dict["state_condition"], key=lambda x: x["name"]
        )

        for key in expected_dict:
            assert (
                actual_dict[key] == expected_dict[key]
            ), f"found a difference at key '{key}' with actual='{actual_dict[key]}' and expected='{expected_dict[key]}'"


def test_load_bekb_state_machine(db):
    bekb_state_machine_json_path = (
        Path(__file__).parent / "data/bekb_state_machine.json"
    )
    assert StateMachine.objects.count() == 1
    update_state_machine(bekb_state_machine_json_path, "New test state machine")
    assert StateMachine.objects.count() == 2

    create_export("New test state machine")
