from typing import List

from pydantic import TypeAdapter

from core.authentication import Authenticated<PERSON>lient
from statemgmt import schemas

expected_Status = TypeAdapter(List[schemas.Status]).validate_json(
    """[{"uuid": "94d2a2e6-39d2-42e5-aee3-ff450f998b0b", "key": "IN_FRONT_OFFICE", "name_de": "Dossier bei FICO", "name_en": "In front office", "name_fr": "Dossier chez le FICO", "name_it": "Dossier presso FICO", "description_de": "FICO erstellt und komplettiert das Dossier.", "description_en": "FICO prepares and completes the dossier.", "description_fr": "FICO pr\\u00e9pare et compl\\u00e8te le dossier.", "description_it": "FICO prepara e completa il dossier.", "color": "#D1F1FB", "order": 5}, {"uuid": "3542e73e-1add-4084-bcf4-4fa2e1d31d73", "key": "READY_FOR_CREDIT_OFFICE", "name_de": "Kreditpr\\u00fcfung beauftragt", "name_en": "Ready for credit office", "name_fr": "Demande tranmise au contr\\u00f4le d\'entr\\u00e9e", "name_it": "Controllo del credito commissionato", "description_de": "FICO reicht Dossier weiter an Kreditpr\\u00fcfung (Inbox Kreditpr\\u00fcfung).", "description_en": "FICO submits dossier to inbox credit check.", "description_fr": "FICO soumet le dossier \\u00e0 la v\\u00e9rification de cr\\u00e9dit de la bo\\u00eete de r\\u00e9ception.", "description_it": "FICO sottopone il dossier al controllo del credito della casella di posta.", "color": "#FEE0C2", "order": 6}, {"uuid": "4644cd48-6c78-4aed-9687-c16bc0aa2686", "key": "IN_CREDIT_OFFICE", "name_de": "Dossier bei Kreditpr\\u00fcfung", "name_en": "In credit office", "name_fr": "Dossier au contr\\u00f4le d\'entr\\u00e9e", "name_it": "Dossier con controllo del credito", "description_de": "KOFF pr\\u00fcft das Dossier und gibt es anschliessend an FICO zur\\u00fcck.", "description_en": "KOFF reviews the dossier and then returns it to FICO.", "description_fr": "KOFF examine le dossier et le renvoie ensuite \\u00e0 FICO.", "description_it": "KOFF esamina il dossier e poi lo restituisce a FICO.", "color": "#FEF3CA", "order": 7}, {"uuid": "728ee254-fadb-4f2f-ba17-534f47a81038", "key": "READY_FOR_BACK_OFFICE", "name_de": "Verarbeitung beauftragt", "name_en": "Ready for back office", "name_fr": "Demande transmise pour traitement", "name_it": "Elaborazione commissionata", "description_de": "FICO reicht Dossier weiter an Verarbeitung (Inbox Verarbeitung).", "description_en": "FICO passes dossier to processing (inbox processing).", "description_fr": "FICO transmet le dossier au traitement (traitement de la bo\\u00eete de r\\u00e9ception).", "description_it": "FICO passa il dossier all\'elaborazione (elaborazione della posta in arrivo).", "color": "#D9D8B5", "order": 8}, {"uuid": "ccd15b9b-693f-4632-8cba-920f65430803", "key": "IN_BACK_OFFICE", "name_de": "Dossier bei Verarbeitung", "name_en": "In back office", "name_fr": "Dossier en cours de traitement", "name_it": "Dossier per l\'elaborazione", "description_de": "Verarbeitung pr\\u00fcft Kreditauftrag und wickelt ab (evtl. Zur\\u00fcckweisung an FICO).", "description_en": "Processing checks and processes credit order (possible rejection to FICO).", "description_fr": "Traitement des contr\\u00f4les et des processus de commande de cr\\u00e9dit (rejet possible \\u00e0 FICO).", "description_it": "Elaborazione controlli ed elaborazioni ordine di credito (eventuale rifiuto a FICO).", "color": "#EBEED6", "order": 9}, {"uuid": "08f9b11a-c972-403f-8528-f5964252685b", "key": "READY_FOR_EXPORT_DEAL", "name_de": "Archivierung beauftragt", "name_en": "Ready for export", "name_fr": "Demande transmise pour archivage", "name_it": "Archiviazione commissionata", "description_de": "Verarbeitung schliesst Gesch\\u00e4ftsfall ab und beauftragt Archivierung (und anschliessende L\\u00f6schung aus HypoDossier).", "description_en": "Processing completes business case and commissions archiving (and subsequent deletion from HypoDossier).", "description_fr": "Le traitement compl\\u00e8te l\'analyse de rentabilisation et commande l\'archivage (et la suppression ult\\u00e9rieure d\'HypoDossier).", "description_it": "L\'elaborazione completa l\'archiviazione del business case e delle commissioni (e la successiva cancellazione da HypoDossier).", "color": "#ECCFF5", "order": 10}, {"uuid": "eac52394-f819-4435-8c53-3578a0290051", "key": "READY_FOR_EXPORT_NO_DEAL", "name_de": "Nullabschluss beauftragt", "name_en": "Ready for export no deal", "name_fr": "Demande transmise pour archivage en tant que \\"Dossier sans suite\\"", "name_it": "Chiusura zero commissionata", "description_de": "FICO entscheidet, dass Gesch\\u00e4ft nicht abgeschlossen wird und Daten archiviert werden sollen.", "description_en": "FICO decides that business will not be closed and data should be archived.", "description_fr": "FICO d\\u00e9cide que les affaires ne seront pas ferm\\u00e9es et que les donn\\u00e9es doivent \\u00eatre archiv\\u00e9es.", "description_it": "FICO decide che l\'attivit\\u00e0 non sar\\u00e0 chiusa e i dati dovrebbero essere archiviati.", "color": "#D7D7D7", "order": 12}, {"uuid": "1f90818b-c86b-4ce8-934d-c40265a13670", "key": "EXPORT_ARCHIVE_AVAILABLE", "name_de": "Dossier wird archiviert", "name_en": "Export archive available", "name_fr": "Dossier en cours d\'archivage", "name_it": "Fascicolo archiviato", "description_de": "Export steht zur Verf\\u00fcgung und die Archivierung wird demn\\u00e4chst durchgef\\u00fchrt.", "description_en": "Export is available and archiving will take place soon.", "description_fr": "L\'exportation est disponible et l\'archivage aura lieu bient\\u00f4t.", "description_it": "L\'esportazione \\u00e8 disponibile e l\'archiviazione avverr\\u00e0 a breve.", "color": "#F9E3F3", "order": 13}, {"uuid": "ed27300b-53ff-46d5-9ccf-029d2e693da5", "key": "EXPORT_ERROR", "name_de": "Archivierung fehlerhaft", "name_en": "Export error", "name_fr": "Archivage incorrect", "name_it": "Archiviazione non corretta", "description_de": "W\\u00e4hrend der Archivierung ist ein Fehler aufgetreten.", "description_en": "An error occurred during archiving.", "description_fr": "Une erreur s\'est produite lors de l\'archivage.", "description_it": "Si \\u00e8 verificato un errore durante l\'archiviazione.", "color": "#F2D2D2", "order": 17}, {"uuid": "8f755980-32bf-4590-88f4-2b328d6ac299", "key": "EXPORT_DONE", "name_de": "Archivierung erfolgreich", "name_en": "Export done", "name_fr": "Archivage r\\u00e9ussi", "name_it": "Archiviazione riuscita", "description_de": "", "description_en": "", "description_fr": "", "description_it": "", "color": "#DDF7F5", "order": 18}]""",
)


def test_dossier_get_status_account_independent(
    db, bekbuser1_client: AuthenticatedClient
):
    state_machine_uuid = "1699513f-a125-4d4c-9804-9354426b277c"
    result = bekbuser1_client.get(f"/api/statemgmt/{state_machine_uuid}/statuses")
    assert result.status_code == 200
    print(result.content)
    assert (
        TypeAdapter(List[schemas.Status]).validate_json(result.content)
        == expected_Status
    )


def test_dossier_get_statuses(db, bekbuser1_client: AuthenticatedClient):
    """deprecated, should not be used anymore"""
    result = bekbuser1_client.get("/api/statemgmt/statuses")
    assert result.status_code == 200
    assert (
        TypeAdapter(List[schemas.Status]).validate_json(result.content)
        == expected_Status
    )
