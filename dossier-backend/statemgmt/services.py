import structlog
from typing import Dict, List

from events.models import Event
from statemgmt import schemas
from statemgmt.models import StateTransition, Status

logger = structlog.get_logger()


def calculate_next_possible_states(
    context: Dict[str, bool], current_state
) -> List[schemas.PossibleTransition]:
    next_transitions = StateTransition.objects.prefetch_related(
        "preconditions__condition"
    ).filter(from_state=current_state)
    possible_transitions = []
    transition: StateTransition
    for transition in next_transitions:
        checked_conditions = []
        for condition in transition.preconditions.all():
            condition_checker_name = condition.condition.name
            result = False
            try:
                result = context[condition_checker_name]
            except LookupError:
                logger.warning(
                    f"Could not find condition_check {condition_checker_name}"
                )

            checked_conditions.append(map_condition(condition, result))
        possible_transitions.append(
            schemas.PossibleTransition(
                uuid=transition.uuid,
                from_state=map_status(transition.from_state),
                to_state=map_status(transition.to_state),
                checked_conditions=checked_conditions,
            )
        )

    def check_not_fulfilled_and_not_show_anyways(
        transition: schemas.PossibleTransition,
    ):
        return not any(
            [
                not condition.fulfilled and not condition.show_anyway
                for condition in transition.checked_conditions
            ]
        )

    possible_transitions = list(
        filter(check_not_fulfilled_and_not_show_anyways, possible_transitions)
    )
    possible_transitions.sort(key=lambda pt: pt.to_state.order)
    return possible_transitions


def map_condition(condition, fulfilled):
    return schemas.Condition(
        uuid=condition.uuid,
        name=condition.condition.name,
        show_anyway=condition.show_anyway,
        overridable=condition.overrideable,
        warning_title_de=condition.warning_title_de,
        warning_de=condition.warning_de,
        warning_title_en=condition.warning_title_en,
        warning_en=condition.warning_en,
        warning_title_fr=condition.warning_title_fr,
        warning_fr=condition.warning_fr,
        warning_title_it=condition.warning_title_it,
        warning_it=condition.warning_it,
        fulfilled=fulfilled,
    )


class StateTransitionError(Exception):
    def __init__(self, message: str, context: Dict[str, bool] = None):
        self.message = message
        self.context = context
        super().__init__(message)


def validate_state_transition(
    context: Dict[str, bool], current_status: Status, next_status: Status
):
    """
    Check if the transition from current_status to next_status is valid.
    If yes, do nothing, else raise a StateTransitionError.

    Raises:
        StateTransitionError: if the transition is invalid.

    @param context:
    @param current_status:
    @param next_status:
    @return: None

    """
    if current_status is None:
        # We assume that a transition from None to any state is always valid.
        return

    possible_transitions = calculate_next_possible_states(context, current_status)

    valid_next_state_transition = [
        transition
        for transition in possible_transitions
        if transition.to_state.uuid == next_status.uuid
        and all(
            [
                condition.fulfilled or condition.overridable
                for condition in transition.checked_conditions
            ]
        )
    ]

    if len(valid_next_state_transition) == 0:
        possible_next_states = [
            transition.to_state.key for transition in possible_transitions
        ]
        error_message = f"Transition not allowed from {current_status} to {next_status} or conditions not fulfilled. Only the following next states are allowed: {possible_next_states}."
        raise StateTransitionError(message=error_message, context=context)


def map_status(status):
    return schemas.Status(
        uuid=status.uuid,
        key=status.key,
        name_de=status.name_de,
        name_en=status.name_en,
        name_fr=status.name_fr,
        name_it=status.name_it,
        description_de=status.description_de,
        description_en=status.description_en,
        description_fr=status.description_fr,
        description_it=status.description_it,
        color=status.color,
        order=status.order,
    )


def get_work_status_transition_events(dossier_uuid, order="created_at"):
    events = list(
        Event.objects.filter(
            type="dossier.schemas.DossierWorkStatusChangedEvent",
            details__dossier_uuid=str(dossier_uuid),
        )
        .order_by(order)
        .all()
    )
    return events


def log_work_status_transitions(dossier_uuid, context: str):
    events = get_work_status_transition_events(dossier_uuid)
    logger.info(context, dossier_uuid=dossier_uuid, num_events=len(events))
    for e in events:
        logger.info(
            "WorkStatusChangedEvent",
            event_uuid=e.uuid,
            dossier_uuid=e.details["dossier_uuid"],
            from_state_key=e.details["from_state_key"],
            to_state_key=e.details["to_state_key"],
            username=e.details["username"],
            created_at=e.created_at.strftime("%Y-%m-%d %H:%M:%S"),
        )

    list_of_transitions, events, e = check_work_status_transition_consistency(
        dossier_uuid
    )
    logger.info("transition of states", transitions=list_of_transitions)


def check_work_status_transition_consistency(dossier_uuid):
    events = get_work_status_transition_events(dossier_uuid)
    list_of_transitions = []
    last_key = None
    inconsistent_event = None
    for e in events:
        from_key = e.details["from_state_key"]
        to_key = e.details["to_state_key"]
        username = e.details["username"]
        list_of_transitions.append((from_key, to_key, username))
        if (not last_key == from_key) and (not inconsistent_event):
            # Found a transition which does not start with the correct from_key
            # That should be the same as the to_key of the previous transition
            # (or None for the first transition)
            inconsistent_event = e

        last_key = to_key
    return list_of_transitions, events, inconsistent_event
