import io
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import List
from zipfile import ZipFile

import docx
from docx import Document
from docx.shared import Inches

from assets import ASSETS_PATH


def get_photo_album_docx_template_path(
    photo_album_docx_template_name: str, add_photo_album_docx: bool
):
    if not add_photo_album_docx:
        return None
    if not photo_album_docx_template_name:
        return None

    path_album = (
        ASSETS_PATH
        / "account"
        / "photo_album_docx_template"
        / photo_album_docx_template_name
    )
    assert path_album.exists()
    return path_album


def create_photo_album_doc(photo_album_path: Path):
    photo_album_doc = None
    if photo_album_path:
        photo_album_doc: docx.document.Document = Document(photo_album_path)
    return photo_album_doc


def add_photo_to_album(photo_album_doc: Document, bytes_image: io.BytesIO):
    if photo_album_doc:
        # bytes_image_2 = get_image_bytes(page_object, file_extension)
        bytes_image.seek(0)  # Reset the stream, because it might have been used before
        photo_album_doc.add_picture(bytes_image, width=Inches(5))
        photo_album_doc.add_paragraph(" ")
        photo_album_doc.add_paragraph(" ")


def create_photo_album_from_image_streams(
    photo_album_path: Path, image_streams: List[io.BytesIO]
):
    photo_album_doc = create_photo_album_doc(photo_album_path)
    if photo_album_doc:
        for bytes_image in image_streams:
            add_photo_to_album(photo_album_doc, bytes_image)
    return photo_album_doc


def add_photo_album_to_zip(photo_album_doc: Document, zip_file: ZipFile):
    if photo_album_doc:
        with TemporaryDirectory() as temp_dir:
            photo_album_result_path = Path(temp_dir) / "Fotos.docx"
            photo_album_doc.save(photo_album_result_path)

            zip_file.write(str(photo_album_result_path), photo_album_result_path.name)
