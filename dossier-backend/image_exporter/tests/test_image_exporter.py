from io import Bytes<PERSON>
from pathlib import Path
from tempfile import TemporaryDirectory
from urllib.parse import urlparse
from zipfile import ZipFile

import pytest

from assets import ASSETS_PATH
from dossier.models import Do<PERSON><PERSON>
from image_exporter.helpers import get_page_objects
from image_exporter.management.commands import image_exporter_worker
from image_exporter.photo_album_docx import get_photo_album_docx_template_path
from image_exporter.schemas import ExportPageImageRequestBody


def get_byte_stream_from_local_assets_folder(page_object, file_extension):
    """
    Take a zip file from inside the local 'assets' folder and return a single jpg file
    from inside the zip as a byte stream.
    """
    url = page_object["url"]
    parts = urlparse(url)

    dossier_id: str = parts.path.split("/")[2]
    assets_dossier_file = ASSETS_PATH / f"sample_dossiers/{dossier_id}.zip"
    assert assets_dossier_file.exists()

    file_path = parts.path.split("/")[2:]
    path_in_zip = Path(*file_path)

    with ZipFile(assets_dossier_file, "r") as zip:
        return BytesIO(zip.read(name=str(path_in_zip)))


def export_images_and_test_result(
    page_object_filters,
    page_objects_data,
    photo_album_path: Path,
    num_page_objects_expected: int,
    photo_album_expected: bool,
):
    with TemporaryDirectory() as temp_dir:
        dest_path = Path(temp_dir) / "out.zip"
        (
            count_page_objects,
            has_photo_album,
        ) = image_exporter_worker.write_page_objects_to_file(
            page_object_filters.model_dump(),
            page_objects_data,
            str(dest_path),
            photo_album_path,
        )

        assert dest_path.exists()
        assert count_page_objects == num_page_objects_expected
        assert has_photo_album == photo_album_expected


page_object_filters_all = ExportPageImageRequestBody(
    export_photos=True,
    export_plans=True,
    export_misc_images=True,
    export_identity_documents=True,
    skip_small_images=True,
)

page_object_filters_photos_only = ExportPageImageRequestBody(
    export_photos=True,
    export_plans=False,
    export_misc_images=False,
    export_identity_documents=False,
    skip_small_images=True,
)

photo_album_docx_template: str = "photo-album-docx-template-default-v01.docx"


@pytest.mark.django_db
def test_basic_image_export_all(monkeypatch):
    """
    Export all images (photos + plans) without photo album. Should be 25 images (no photo album)
    """
    dossier = Dossier.objects.get(name="image export samples")

    with monkeypatch.context() as m:
        m.setattr(
            image_exporter_worker,
            "get_image_bytes",
            get_byte_stream_from_local_assets_folder,
        )
        page_object_filters = page_object_filters_all
        add_photo_album_docx = False
        photo_album_path = get_photo_album_docx_template_path(
            photo_album_docx_template, add_photo_album_docx
        )

        num_page_objects_expected = 25

        page_objects_data = get_page_objects(
            dossier.uuid, page_object_filters.model_dump()
        )

        export_images_and_test_result(
            page_object_filters,
            page_objects_data,
            photo_album_path,
            num_page_objects_expected,
            False,
        )


@pytest.mark.django_db
def test_basic_image_export_photos_only(monkeypatch):
    """
    Export all property photos only (no plans) with photo album. Should be 13 images + photo_album
    """

    dossier = Dossier.objects.get(name="image export samples")

    with monkeypatch.context() as m:
        m.setattr(
            image_exporter_worker,
            "get_image_bytes",
            get_byte_stream_from_local_assets_folder,
        )
        page_object_filters = page_object_filters_photos_only
        add_photo_album_docx = True
        photo_album_path = get_photo_album_docx_template_path(
            photo_album_docx_template, add_photo_album_docx
        )

        num_page_objects_expected = 13

        page_objects_data = get_page_objects(
            dossier.uuid, page_object_filters.model_dump()
        )

        export_images_and_test_result(
            page_object_filters,
            page_objects_data,
            photo_album_path,
            num_page_objects_expected,
            True,
        )
