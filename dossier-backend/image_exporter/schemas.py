from typing import Optional

from pydantic import BaseModel


class ExportPageImageRequestBody(BaseModel):
    export_photos: bool
    export_plans: bool
    export_misc_images: bool
    export_identity_documents: bool
    skip_small_images: bool


class ImageExportRequestV1(BaseModel):
    dossier_uuid: str
    put_upload_url: str
    name_of_archive: str
    page_object_filters: ExportPageImageRequestBody
    add_photo_album_docx: Optional[bool] = False


class ImageExportResponsePayload(BaseModel):
    count_page_objects: int


class ImageExportResponseV1(BaseModel):
    http_status_code: int
    message: str
    payload: Optional[ImageExportResponsePayload] = None


class ExportPageObjectsResponse(ImageExportResponsePayload):
    location: str
    name_of_zip_archive: str
