# Setup Dossier Manager Service (dms)

## Setup local development environment

Generate a gitlab personal access token, with read api perpmissions (read_api
Grants read access to the API, including all groups and projects, the container registry, and the package registry.)
https://gitlab.com/-/profile/personal_access_tokens

Then run

```shell
poetry config http-basic.hypodossier <username> <password>
poetry install
```

If your using a docker container, copy the auth-example.toml to auth.toml and add the gitlab token and username to the file.

Copy the .env file
```shell
cp .env-sample .env
```

Install git lfs
```shell
brew install git-lfs
```

or

```shell
sudo apt install git-lfs
```

```shell
git-lfs install
git-lfs pull
```

For Macs, make sure the postgreqsl path is set in .env .e.g
```shell
POSTGRES_PATH=/opt/homebrew/bin/
```

## Create Access to rabbitmq

1. Log in to RabbitMQ (https://rabbitmq-mgmt.hypo.duckdns.org/) with admin, admin.
2. Under Admin, Virtual Hosts, create a new virtual host named dossier-processor (as given in the .env-sample file at the end of the Rabbit-URL).
3. Under Admin, Users, create a second user with username and password 'dossier-manager' (according
   to the .env-sample file)
4. Give this user permissions on the virtual host 'dossier-processor' as ".*,.*,.*".
   ![docs/rabbitmq-permissions.png](docs/rabbitmq-permissions.png)

## Create Database on pgcluster1.hypo.duckdns.org

```bash
# use admin as password
sudo apt install postgresql-client-12
psql -U admin -h pgcluster1.hypo.duckdns.org postgres -c "create user dms with password 'dms'";
psql -U admin -h pgcluster1.hypo.duckdns.org postgres -c "create database dms with owner 'dms'";
psql -U admin -h pgcluster1.hypo.duckdns.org postgres -c "alter user dms createdb";

```


## Run on docker swarm

```
TAG=v1.47.0 docker stack deploy -c docker-compose.stack.yml dms --with-registry-auth --prune 
```

TAG=c19c1d4dcd348774c4e2d6b952377ff194240815 docker stack deploy -c docker-compose.stack.yml dms --with-registry-auth --prune


## S3 bucket

create the default bucket dms-default-bucket on the s3 server in the UI or via CLI with

```bash
# if not already done, set the alias local
mcli alias set local https://minio.hypo.duckdns.org S3_ACCESS_KEY S3_SECRET_KEY
mcli mb local/dms-default-bucket
mcli mb local/dossier
```

## load initial data

Loads the database dump db_latest.tar from the assets folder which contains 3 dossier and 2 users:
- a superuser admin with password admin (for the admin management interface https://dms.hypo.duckdns.org/admin) 
-  <EMAIL> (owner of the 3 dossiers)


You want to restore the file dossier-backend/assets/db_latest.tar. First, you need to pull it from GitLab using git LFS.
```
# install Git LFS if not already done
sudo apt -y install git-lfs
git lfs install

# pull from repo
git lfs pull

# pg_restore, password: dms
pg_restore -h pgcluster1.hypo.duckdns.org -c -U dms  -d dms  assets/db_latest.tar
```

Copy .env-sample to .env to be able to execute commands in the local config.
The following command loads the files of the 3 dossiers into the s3 store
```
python manage.py s3_assets load dms-default-bucket 
 ```

use the following command to create the asset zip files
```
python manage.py s3_assets store dms-default-bucket 
 ```

Give dms user right to create dbs needed for pytest
```
# pw: admin
psql -h localhost -U admin postgres -c 'alter user dms createdb;'

```

Execute defined pytest tests (should run successfully)
```
pytest
```


# Some database helpers
## reset the database
```
SELECT pg_terminate_backend(pg_stat_activity.pid)
FROM pg_stat_activity
WHERE pg_stat_activity.datname = 'dms' 
  AND pid <> pg_backend_pid();
drop database dms;
create database dms with owner 'dms';
"
```

## backup database (used for the db_latest.tar in the assets folder)
```bash
pg_dump -h localhost -U admin  -F t dms > dms_dev_backup_$(date "+%Y%m%d_%H%M%S").tar
```

## restore database
```bash
pg_restore -h localhost -c -U dms  -d dms  <replace with backup .tar file>
```

## backup production database to local directory
- in separate terminal do the port forward from 5432 to 55432 on localhost: ssh db1
- use pw from 'ssh db1' (take pw from there):
  cat hypsql/docker-compose.yml | grep POSTGRES_PASSWORD

- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t prod_dms_bekb > dms_bekb_backup_$(date "+%Y%m%d_%H%M%S").tar
- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t test_dms_bekbs > dms_bekbs_backup_$(date "+%Y%m%d_%H%M%S").tar
- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t test_dms_bekbz > dms_bekbz_backup_$(date "+%Y%m%d_%H%M%S").tar

- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t test_dms_bekbe > dms_bekbe_backup_$(date "+%Y%m%d_%H%M%S").tar

- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t prod_dms_demo > dms_demo_backup_$(date "+%Y%m%d_%H%M%S").tar
- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t prod_dms_hbl > dms_hbl_backup_$(date "+%Y%m%d_%H%M%S").tar
- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t prod_dms_feyn > dms_feyn_backup_$(date "+%Y%m%d_%H%M%S").tar
- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t prod_dms_hypoteq > dms_hypoteq_backup_$(date "+%Y%m%d_%H%M%S").tar

- in new terminal: pg_dump -h localhost -U postgres  -p 55432 -F t test_dms_swissfext > dms_swissfext_backup_$(date "+%Y%m%d_%H%M%S").tar


## Restore backup to a new database locally:
# create new db bekb_prod. open console in local db, create db but with existing db user 'dms'
- create database bekb_prod with owner dms
# restore data in console (run twice, ignore warnings - second run will have fewer warnings)


# Password for all these commands is 'dms'
# Shutdown any dms running locally before the next command
dropdb -h localhost --username=dms dms
createdb -h localhost --owner=dms --username=dms dms 
pg_restore -h localhost -c -U dms  -d dms dms_bekb_backup_2024XXXXXXX.tar
# In case there are db migrations run this:
python manage.py migrate
# to access the dossiers the account.key needs to be matched between db (in prod it is bekbp) and keycloak user 
# -> change account.key in admin to "default" (or change in keycloak)


## create the bekbe account

```bash
python manage.py reset-db

# import doccheck for bekbe
python manage.py export_import_doccheck import-doccheck-from-json doccheck/configurations/bekb_doccheck_240522.json bekbe

# create some fake dossiers for the account bekbe
python manage.py create_fake_dossier


# Find out on which docker node the dms_dms runs, e.g. swarm2w5
ssh swarm21
docker stack ps swissfext-dms | grep Run | grep dms_dms
scp assets/document_category/DocumentCategory-fixed-2023-05-23.json ubuntu@swarm2w5:/home/<USER>
ssh swarm2w5
docker ps | grep dms_dms
docker cp ./DocumentCategory-fixed-2023-05-23.json hypoteq-dms_dms.1.r7ox2rq4s6lgfndsfpnhq70ab:/app
docker exec -it hypoteq-dms_dms.1.r7ox2rq4s6lgfndsfpnhq70ab bash
python manage.py update_dossier_categories hypoteq DocumentCategory-fixed-2023-05-23.json




```

## Configure BEKB Completeness Check on a new instance (e.g. bekbz)

```
ssh swarm21
docker stack ps bekbz-dms | grep Run | grep dms_dms

# Now log in to the node where the dms is running
docker ps | grep bekbz

# Now log into the docker container
docker exec -it bekbz-dms_dms.1.x590b9hbw0b8kznlte81oyuzc bash

# Create a new dockcheck with standard bekb config
# This creates a new doccheck here: https://dms.bekbz.test.hypodossier.ch/admin/doccheck/doccheck/
python manage.py export_import_doccheck import-doccheck-from-json doccheck/configurations/bekb_doccheck.json bekbz

# Import business case type for the completeness check
# This creates the business case types here: https://dms.bekbz.test.hypodossier.ch/admin/doccheck/businesscasetype/
# they have to correspond to the business case type for the dossiers found here: https://dms.bekbz.test.hypodossier.ch/admin/dossier/businesscasetype/
python manage.py export_import_businesscase_type import-businesscase-type dossier/configurations/bekb_businesscase_types.json  bekbz 

# Now select the created doccheck in the account and save the account: 
https://dms.bekbz.test.hypodossier.ch/admin/dossier/account/5652c837-5d93-4187-8bca-7a252005e7bc/change/

# There are already dossiers in the account which all do not have a compleness check 'case' (case list is still empty)
# So assign an empty case to the existing dossiers
# Note: case is only added to non-expired dossiers
python manage.py doccheck add-case-to-existing-dossiers bekbz

```

## Initializing CDP Data

Apply the latest migrations : 
```bash
python manage.py migrate
```

For developers, add "hd_internal" to the `cdp_field_set` property for the "default" account in the admin settings 


To initialize the CDP data, you can use the provided Django management command `cdp_init`. 
This command imports all necessary CDP data, including page object types, document categories, and Field Set configurations.
The asset files for CDP data are stored in `dossier-backend/cdp/management/assets` directory.

Usage : 
```bash
python manage.py cdp_init
```

The command will execute the following sub-commands in the following order:

1. **Import Page Object Keys:**
   
    Executes the `import_cdp_page_object_keys` command to import page object keys from the latest JSON file in the
   `dossier-backend/cdp/management/assets/page_object_keys` directory. Optionally a `--file ` parameter with the json file path 
   can be passed to this command when running it independently.

2. **Import Document Categories:**  
   Executes the `import_cdp_document_categories` command to import document categories from the latest JSON file in 
   the `dossier-backend/cdp/management/assets/document_categories` directory. Optionally a `--file ` parameter with the json file path 
   can be passed to this command when running it independently.
  
3. Import Field Sets:  
   Executes the `import_all_field_sets` command to import all Field Set data from the latest JSON file in s
    the `dossier-backend/cdp/management/assets/field_set_data` directory. Optionally a `--file ` parameter with the json file path
    can be passed to this command when running it independently.


#### Exporting CDP Data

To export the CDP data, you can use the provided Django management command `export_all_field_sets`.
This command exports all Field Set configurations to a JSON file in the `dossier-backend/cdp/management/assets/field_set_data` directory 
as `field_set_export_{%Y%m%d}.json`


#### Source of CDP Data

- Page object keys, and document categories as json files are generated by `hyextract` as the source. 
- In `hyextract` navigate to file : `hyextract/asyncizer/scripts/script_create_hypodossier_spec_data_fields.py` and run the script to generate the json files.
- In this script, the methods used to generate the page object keys and the document categories are contained in the snippet : 

```python 
p_page_object_keys = Path(
    f"{BASE_DIR}/doc/internal/cdp/{timestamp_version}_Hypodossier_Page_Object_Keys.json"
)
num_unique_page_objects  = create_page_object_key_union_set_for_all_doc_cat(d, p_page_object_keys, [DocumentCat.FINANCIAL_STATEMENT_COMPANY])
print(f"Exported {num_unique_page_objects} unique page object keys to {p_page_object_keys}")

p_document_categories = Path(f"{BASE_DIR}/doc/internal/cdp/{timestamp_version}_Hypodossier_Document_Categories.json")
num_document_categories = create_hypodossier_document_cat(d, p_document_categories)
print(f"Exported {num_document_categories} document categories to {p_document_categories}")
```
- In `hyextract` the generated JSON files should be stored under : `hyextract/doc/internal/cdp/` directory.
- The user can then copy these files to the `dossier-backend/cdp/management/assets` directory in their respective directories. 


#### Steps to configure a field in the dms - admin
1. Navigate to `FieldSets` ; Add another `assigned_field` ; 
2. Create a new `field_definition` if needed ; create a new `return_type` if it does not exist already
3. Next we want to configure the priority mapping for the `relevant_page_objects`  related to the `field_definition`. Filter the `relevant_page_objects` by the `field_definition`; click on the `relevant_page_object` and add priorities for each document category for the `relevant_page_object`
4. Navigate to `priority_mappings`; filter by the `field_definition` to verify / view the priority mapping for the selected `field_definition`

# Loading bcgeevo data

python manage.py load_bcge_data load-account bcgeevo

python manage.py load_bcge_data update-document-categories bcgeevo

In the django admin,

http://localhost:8000/admin/dossier/account/

under bcgeevo, set Default bucket name, to dms-default-bucket

Then create documents using

python manage.py create_dossier_test_archiving_possible_document_category bcgeevo False


You should be able to <NAME_EMAIL> (both usernaname and password)


# Loading finnovadev data

python manage.py load_finnova_data load-account finnovadev

python manage.py load_finnova_data update-document-categories finnovadev


In the django admin,

http://localhost:8000/admin/dossier/account/

under finnovadev, set Default bucket name, to dms-default-bucket

python manage.py create_dossier_test_archiving_possible_document_category finnovadev False

You should be able to <NAME_EMAIL> (both usernaname and password)