SECRET_KEY="some very long secret"
RABBIT_URL=amqp://dossier-manager:<EMAIL>:5672/dossier-processor

DJANGO_ALLOWED_HOSTS="localhost localhost:8000 dms.hypo-staging-du.duckdns.org dms.hypo.duckdns.org"
DJANGO_CORS_ORIGIN_WHITELIST=^http://localhost:[0-9]*$ ^https://.*\.hypo\.duckdns\.org$ ^https://.*\.dmf\.hypo-staging-du\.duckdns\.org$ ^https://.*\.hypo-staging-du\.duckdns\.org$

POSTGRES_HOST=postgresql.staging.hypodossier.ch
POSTGRES_DB=staging_dms
POSTGRES_USER=staging_dms
POSTGRES_PASSWORD=staging_dms_pw

DEBUG=1

# Enable this for Silk Django SQL profiling
DEBUG_ENABLE_SILK=False

S3_ENDPOINT=minio.staging.hypodossier.ch
ADMIN_S3_ENDPOINT_URL=https://minio.staging.hypodossier.ch
S3_ACCESS_KEY=EX1D91NXN2LM0IV0C3X7
S3_SECRET_KEY=wFnrvYl544A+d+4PSnn1UC6JFscG06dGA+u5x8GS
S3_SECURE=true
S3_REGION=ch-dk-2

ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY=DossierZipper.staging.DossierZipRequestV1

SENTRY_ENVIRONMENT=staging

KEYCLOAK_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB
# Used for API used to Manage/create Accounts and JWKs
MANAGEMENT_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB

DOSSIER_EVENT_CONSUMER_PREFETCH=20
DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD=10
DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE=80
DOSSIER_EVENT_CONSUMER_FINALIZE_PROCESS_BACKOFF_MAX_TRIES=6

PRETTY_PRINT_LOG=True

HYSTATIC_S3_ACCESS_KEY=EXO1693d68f35809c97147f2349
HYSTATIC_S3_SECRET_KEY=1e9FTP3X5lr2jQrJvv3qd3xhMngK7-I1-eNKG97WT_0

# If this is True then the document category ZKB_VBV will be handled in a special way
# Semantic document is set to read-only directly after processing
# Download triggers download of extracted file, not of the processed file
# This only applies if the extracted file is a PDF
ZKB_VBV_USE_EXTRACTED_FILE=True

TEMPFILESTORE_S3_ACCESS_KEY=EXO2705ead2ef01134267c1ce59
TEMPFILESTORE_S3_SECRET_KEY=jVkq3PqbzBpl7elGCGvSdg_lCOeCVdnd5jnA7oCTBjk

# If this is True and processing delivers a processed_page, semantic_document and/or semantic_page with a
# document_category which is not configured then the document category is added automatically.
# If this is False then the document_category will be mapped to DocumentCategory.UNKNOWN
ADD_NEW_DOCUMENT_CATEGORIES_FROM_PROCESSING=False

# Namespace, e.g. BEKB, SWISSFEX, etc
# Inserted into RabbitMQ routing keys, as rabbitmq is currently shared between instances
DEPLOYMENT_NAMESPACE=staging

ADMIN_KEYCLOAK_CLIENT_ID=django-admin
ADMIN_KEYCLOAK_SECRET_KEY=wqtvWuYecdJ6vfnM4g6PGM668k3D2ZeY
KEYCLOAK_REALM_ENDPOINT=https://auth.hypo.duckdns.org/realms/dev-hypodossier/

# If this is True, documents of certain document categories for BEKB will be grouped (= combined into one
# semantic document) directly before the BEKB archive export happens. Structure of semantic documents is
# permanently changed. Documents with different collaterals will NOT be merged into one document.
ENABLE_BEKB_ARCHIVE_GROUPING_BY_DOCUMENT_CATEGORY=True

# Whether to enable to output of pageobjects in data v2
# we are slowly depreciating them
# https://gitlab.com/hypodossier/document-universe/-/issues/512
ENABLE_PAGE_OBJECTS_IN_DATA_V2=True

INSTANCE_SIGNING_KEY={"d":"HMxExd13eBEogE6H-xF_vJWZVH4Zavzk4LVJsQ9qQmzXQyh6nIjXhg7OvOACq3U7A2QhIbmqU-gpH9s7Vba9xxNwY9LMDjxGz-ovaj0FPeOsTj5OTWaJBK4Gv_v1zUIpO_8Nvn_bVLLeoZF9jXgWmrxN9DHz9pRxT8zriU6bCgDKBgR3JAFC1QAyHjY0i4dW12NOH06Cv53PWelaoiZ5I49NLizVvSX1NQ4JYTiwKwfiXWEBUCudaXWt2k06lIPE3UAUv_tbteHrJtNoZ-Hhtnx9fbyII9k_gzjVV2cBVT-DodQxHRBR7m9StE2UT10ZyOUpS8e-pioN-UwfTA5kgQ","dp":"xnX34zYjq65DJU7tfPB7g_irMTS_RF9gCecPSHdWJ1p1mG4QacmAaJeIZAX58q2pqv2o2Y5kJCdEX-iabVGKvFEUcFdqDZ4mAj744tNUV-tgAsr-pMT9OgE7AnIyq8cYSUIc9KkmHBiBHwM3VIeJljb19MkX9hlEs54SiQHiPkE","dq":"w42P13rDwpGPFnDT9pXhsOqcpVqptTt5-5jeOG4KHX8ZO_Y2QDIrTJoYngvxFjD6d5TFhkPNu8gwH8PNawRgnLs3gcjOXVWTBufqjct2sZG1FXcSfUxj-IoMKktoVYCwCIxImRKa7JhT9iqWOoKpTqqrLFSTySZfQ24_dfkcwW0","e":"AQAB","kid":"567558dd-571a-47dd-9f28-408c08719f3d","kty":"RSA","n":"7YT62XnCtRUWqfK8VJYYS26RTcEsTUR9cScwd16BDtsBGGmZ97wWY04ObFbX-AHBQqmcDoYGwHueky5NezO5kRUNkm819x5h8oA0NLjIW4bu24yt1gPxq484cgIelNfv9fUaJWyAwkcdVP5cXb3DZK05K32WuwDVi5LTaxhulwmYZRz4C9HnNEqnwRBAzorZ42Jo3pMfQVaRyEL75IuR2SMyJZWI0bHzigNlSEqo3gGaAZR9MqWySQCWm20GQALTywuKwTqlmXjaV7mQq-O_hdraQeKksU6-D136X-sflTmTedrv2_E6sExbq_ZVWT0yZdEHnyFfWSQtjS9C8J3RSw","p":"-ljS3FNTyQTzgwLSifBBEGjF2ws_Klb2HzIyWyaYQFXrYM1_eNcLS00APoLh5YckPVVaLZW7wfh0Kkg_amwv4lX_UTGNX4tB4bsNEQpGR_-Q6Cf6euh8e1YCKp5QbOy76hg258HtjszUY8rGiPUZ4K8VHiQw1jYoDffZzEigcCE","q":"8uIBIXCy_fWCV-QuzjPMEAMD_ORBAtRSbx2dA5nlS2PjctW72zUab3BDL47lJGMrioJ-hnlAXAPmdf2jXgrnlRbQPGTz9_we1tL5dWi1tRzNZMm_xDkFs0bYu5SeFtipMPlDBtY_Pd-w5F4ubO7BqcvnE8idVhtsXVU3zeU2g-s","qi":"JbZZm8hGJgqEkPhI2eSLz2X5DGaiE4ZuqDLX1HcsxBCj2WjD_OAmtJSlo3eA_BoPe9c5Uul09U44oDNz-lV5LtYeJYNG8i7oljxhWd9Fk3c6P1vFA17ewklpbGq-53KBxMhu1jVUw0GV8Cg3SaO9r99s01i9Ykvr_6HjEn9oO9I"}


HYPYFREP_PDFA_CONVERT_REQUEST_ROUTING_KEY=hyfrep.FREPPDFAConversionRequest

CDP_CONFIDENCE_THRESHOLD=0.7