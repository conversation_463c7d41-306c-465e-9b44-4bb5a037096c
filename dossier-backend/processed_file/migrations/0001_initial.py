# Generated by Django 3.2.3 on 2021-06-22 12:23

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dossier', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PageObjectTitle',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(blank=True, max_length=255, null=True)),
                ('de', models.CharField(max_length=255)),
                ('en', models.CharField(max_length=255)),
                ('fr', models.CharField(max_length=255)),
                ('it', models.Char<PERSON>ield(max_length=255)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PageObjectType',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProcessedFile',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='processed_files', to='dossier.dossier')),
                ('extracted_file', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='processed_files', to='dossier.extractedfile')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProcessedPage',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('confidence_value', models.FloatField(default=0)),
                ('number', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('lang', models.CharField(choices=[('DE', 'German'), ('EN', 'English'), ('FR', 'French'), ('IT', 'Italian')], default='DE', max_length=2)),
                ('document_category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='dossier.documentcategory')),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='processed_pages', to='dossier.dossier')),
                ('image', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dossier.dossierfile')),
                ('page_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.pagecategory')),
                ('processed_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='processed_pages', to='processed_file.processedfile')),
                ('searchable_pdf', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dossier.dossierfile')),
                ('searchable_txt', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dossier.dossierfile')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PageObject',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('ref_height', models.IntegerField()),
                ('ref_width', models.IntegerField()),
                ('top', models.IntegerField()),
                ('left', models.IntegerField()),
                ('right', models.IntegerField()),
                ('bottom', models.IntegerField()),
                ('value', models.TextField(blank=True, null=True)),
                ('confidence_value', models.FloatField()),
                ('confidence_formatted', models.CharField(blank=True, max_length=16, null=True)),
                ('confidence_level', models.CharField(choices=[('low', 'low'), ('medium', 'medium'), ('high', 'high')], max_length=16)),
                ('key', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='processed_file.pageobjecttitle')),
                ('processed_page', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='page_objects', to='processed_file.processedpage')),
                ('type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='processed_file.pageobjecttype')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
