import logging
import structlog
import zipfile
from pathlib import Path
from urllib.parse import urlparse
from uuid import uuid4, UUID
from zipfile import ZipFile
import uuid
import PyPDF2
import pytest
import requests_mock
from channels.db import database_sync_to_async

from assets import ASSETS_PATH
from core.temporary_path import temporary_path
from dossier.helpers import create_dossier_file_without_saving
from dossier.helpers_v2 import prepare_semantic_dossier
from dossier.models import Dossier, DocumentCategory
from dossier.schemas import SemanticDossier
from dossier_zipper.workers import (
    process_dossier_zip_request,
)
from dossier_zipper.packer import (
    package_documents,
    SemanticDocumentExport,
    package_offline_version,
    package_document,
)
from dossier_zipper.schemas import DossierZipResponseV1, DossierZipRequestV1
from projectconfig.settings import ZKB_VBV_DOCUMENT_CATEGORY_KEY
from semantic_document.models import SemanticDocument

from semantic_document.models import SemanticPageUserAnnotations

logging.basicConfig(level=logging.INFO)

logger = structlog.get_logger()


def prepare_data(dossier_name: str):
    logger.info(f"Now prepare data for dossier '{dossier_name}'")
    dossier = Dossier.objects.get(name=dossier_name)
    semantic_dossier = prepare_semantic_dossier(dossier)

    export_request_uuid = uuid4()
    dossier_file = create_dossier_file_without_saving(dossier, "sample.zip")

    data = semantic_dossier.model_dump()
    data["uuid"] = str(data["uuid"])
    return export_request_uuid, SemanticDossier(**data), dossier_file


async def async_prepare_data(dossier_name: str):
    return await database_sync_to_async(prepare_data)(dossier_name)


def content_callback(request, context):
    parts = urlparse(request.path_url)
    assets_dossier_file = (
        ASSETS_PATH / f'sample_dossiers/{parts.path.split("/")[2]}.zip'
    )
    assert assets_dossier_file.exists()

    file_path = parts.path.split("/")[2:]
    path_in_zip = Path(*file_path)

    with ZipFile(assets_dossier_file, "r") as zip:
        return zip.read(name=str(path_in_zip))


def test_dossier_zipper_request_response_without_rabbitmq(db):
    export_request_uuid, request_dossier, dossier_file = prepare_data(
        "sales pitch mix with errors dossier"
    )

    with requests_mock.Mocker() as m:
        request_calls = m.get(requests_mock.ANY, content=content_callback)
        put_call = m.put(requests_mock.ANY)

        request = DossierZipRequestV1(
            zip_request_uuid=export_request_uuid,
            semantic_dossier=request_dossier,
            put_upload_url=dossier_file.put_url,
        )

        raw_response = process_dossier_zip_request(
            dossier_zip_request=request.model_dump_json()
        )

        assert request_calls.call_count == 31
        assert put_call.call_count == 1

    response = DossierZipResponseV1.model_validate_json(raw_response)
    assert response.zip_request_uuid == export_request_uuid


exported_document_Exports = {
    UUID("34cedd8c-caf6-49fb-a240-d829d56b47f0"): SemanticDocumentExport(
        semantic_document_uuid=UUID("34cedd8c-caf6-49fb-a240-d829d56b47f0"),
        filename="410 PK Ausweis Thiemann Manuel AXA Basis Kader 2017-03-01.pdf",
        pages=2,
    ),
    UUID("3e492479-7c67-453b-ab1b-f3c30dcc1ab5"): SemanticDocumentExport(
        semantic_document_uuid=UUID("3e492479-7c67-453b-ab1b-f3c30dcc1ab5"),
        filename="240 Betreibungsauskunft Thiemann Manuel Antonius Keine Betreibungen 2014-04-11.pdf",
        pages=1,
    ),
    UUID("3e9f7a53-5eb7-45c9-8760-847ad15bdc32"): SemanticDocumentExport(
        semantic_document_uuid=UUID("3e9f7a53-5eb7-45c9-8760-847ad15bdc32"),
        filename="410 PK Ausweis Thiemann Manuel AXA Zusatz GL 2017-03-01.pdf",
        pages=2,
    ),
    UUID("4b2299ee-6f31-465c-b2f5-68631bca45de"): SemanticDocumentExport(
        semantic_document_uuid=UUID("4b2299ee-6f31-465c-b2f5-68631bca45de"),
        filename="210 Pass CH Andreas Dominik».pdf",
        pages=1,
    ),
    UUID("624a4c2e-66d4-4e24-b6e0-ab2cedbfe69e"): SemanticDocumentExport(
        semantic_document_uuid=UUID("624a4c2e-66d4-4e24-b6e0-ab2cedbfe69e"),
        filename="617 Gebäudeversicherungsausweis ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18.pdf",
        pages=1,
    ),
    UUID("7666c840-8f4e-4937-b8ab-29cdfedacbb9"): SemanticDocumentExport(
        semantic_document_uuid=UUID("7666c840-8f4e-4937-b8ab-29cdfedacbb9"),
        filename="617 Gebäudeversicherungsausweis ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20.pdf",
        pages=1,
    ),
    UUID("7bf53b35-0fff-4c5f-8988-e23896175836"): SemanticDocumentExport(
        semantic_document_uuid=UUID("7bf53b35-0fff-4c5f-8988-e23896175836"),
        filename="310 Steuererklärung Mustermann Max ZH 2019.pdf",
        pages=13,
    ),
    UUID("9fd92c2f-10d3-43c1-b4bd-440680d4b624"): SemanticDocumentExport(
        semantic_document_uuid=UUID("9fd92c2f-10d3-43c1-b4bd-440680d4b624"),
        filename="240 Betreibungsauskunft Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11.pdf",
        pages=1,
    ),
    UUID("a7e7b2da-a760-4ff9-851f-52e9c75ecdd5"): SemanticDocumentExport(
        semantic_document_uuid=UUID("a7e7b2da-a760-4ff9-851f-52e9c75ecdd5"),
        filename="245 Strafregisterauszug Thiemann Manuel Antonius 2020-09-01.pdf",
        pages=1,
    ),
    UUID("b0855f5e-a46f-4edb-8894-2c605dd158d5"): SemanticDocumentExport(
        semantic_document_uuid=UUID("b0855f5e-a46f-4edb-8894-2c605dd158d5"),
        filename="424 Freizügigkeitskonto Manuel Thiemann ZKB 2019-12-31.pdf",
        pages=3,
    ),
    UUID("b9537fc0-76c5-4842-a29d-07ceb4a0fe81"): SemanticDocumentExport(
        semantic_document_uuid=UUID("b9537fc0-76c5-4842-a29d-07ceb4a0fe81"),
        filename="430 Vorsorgekonto Säule 3 Angelica Thiemann Postfinance Saldo CHF 12'902.pdf",
        pages=2,
    ),
    UUID("e29e8c5f-2afb-49dd-b2dc-f29d6b5865dc"): SemanticDocumentExport(
        semantic_document_uuid=UUID("e29e8c5f-2afb-49dd-b2dc-f29d6b5865dc"),
        filename="330 Lohnausweis Manuel Thiemann 2016 brutto CHF 10'000.pdf",
        pages=1,
    ),
}


def test_package_documents(db):
    logger.info(f"root={ASSETS_PATH.parent}")

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    print(dossier.uuid)
    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        with requests_mock.Mocker() as m:
            request_calls = m.get(requests_mock.ANY, content=content_callback)
            export = package_documents(semantic_dossier, temp_path, add_metadata=True)

            assert request_calls.call_count == 31
            assert export.semantic_document_exports == exported_document_Exports

        num_files = len(export.files)
        assert num_files == 14


@pytest.mark.parametrize(
    "settings_vbv,change_file_to_vbv",
    [[True, True], [True, False], [False, True], [False, False]],
)
def test_package_documents_with_zkb_vbv(
    db, settings_vbv: bool, change_file_to_vbv: bool
):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    DocumentCategory.objects.create(
        id="600-VBV",
        account=dossier.account,
        name=ZKB_VBV_DOCUMENT_CATEGORY_KEY,
        de="VBV",
    )

    vbv_cat = DocumentCategory.objects.get(
        account=dossier.account, name=ZKB_VBV_DOCUMENT_CATEGORY_KEY
    )
    other_cat = DocumentCategory.objects.get(
        account=dossier.account, name="VESTED_BENEFITS_ACCOUNT"
    )

    sem_docs = SemanticDocument.objects.filter(
        dossier=dossier, document_category=other_cat
    )
    modified = False
    if change_file_to_vbv:
        for sem_doc in sem_docs:
            if sem_doc.document_category == other_cat:
                sem_doc.document_category = vbv_cat
                # sem_doc.document_category_key = vbv_cat.name
                sem_doc.title_suffix = "test_vbv"
                # sem_doc.title = f"{vbv_cat.id} {vbv_cat.de} {sem_doc.title_suffix}"
                sem_doc.title_custom = None
                sem_doc.save()
                modified = True
        assert modified
    else:
        assert not modified

    # Reload dossier with modified vbv doc
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        with requests_mock.Mocker() as m:
            request_calls = m.get(requests_mock.ANY, content=content_callback)
            export = package_documents(
                semantic_dossier,
                temp_path,
                add_metadata=True,
                zkb_vbv_use_extracted_file=settings_vbv,
            )

            for p in export.files:
                if p.name.startswith(vbv_cat.id):
                    filesize_bytes_vbv = p.stat().st_size
                    if settings_vbv and change_file_to_vbv:
                        assert request_calls.call_count == 29
                        assert (
                            filesize_bytes_vbv == 1813100
                        ), "ZKB_VBV_USE_EXTRACTED_FILE is enabled, we expect the original file for the vbv"
                    else:
                        assert request_calls.call_count == 31
                        assert (
                            filesize_bytes_vbv == 626806
                        ), "ZKB_VBV_USE_EXTRACTED_FILE is disabled we expect the processed file for the vbv"
        num_files = len(export.files)
        assert num_files == 14


def test_package_documents_add_uuid_suffix(db):
    logger.info(f"root={ASSETS_PATH.parent}")

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    print(dossier.uuid)
    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        with requests_mock.Mocker() as m:
            request_calls = m.get(requests_mock.ANY, content=content_callback)
            export = package_documents(
                semantic_dossier, temp_path, add_metadata=True, add_uuid_suffix=True
            )

            assert request_calls.call_count == 31
            assert export.semantic_document_exports == exported_document_Exports

        num_files = len(export.files)
        assert num_files == 14

        semantic_documents_uuids = [x.uuid for x in semantic_dossier.semantic_documents]

        for doc_uuid in semantic_documents_uuids:
            assert str(doc_uuid) in str(export.files)


def test_package_documents_with_annotations(db):
    """Test that annotations are properly included in the dossier zipper."""
    # Get test dossier
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    # Add an annotation to the first page of the first document
    semantic_document = dossier.semantic_documents.first()
    semantic_page = semantic_document.semantic_pages.first()

    # Add test annotation
    annotation_group_uuid = uuid.uuid4()
    SemanticPageUserAnnotations.objects.create(
        semantic_page=semantic_page,
        annotation_group_uuid=annotation_group_uuid,
        annotation_type="highlight",
        text="Test annotation",
        bbox_top=0.2,  # 20% from top
        bbox_left=0.3,  # 30% from left
        bbox_width=0.1,  # 10% of page width
        bbox_height=0.05,  # 5% of page height
        hexcolor="#FFFF00",  # Yellow
    )

    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        export = package_documents(
            semantic_dossier, temp_path, add_metadata=True, add_uuid_suffix=True
        )

        # Find the exported PDF file for our annotated document based on the uuid_suffix
        # that has been added to the export filename due to add_uuid_suffix=True9
        pdf_file = next(
            p
            for p in export.files
            if p.name.startswith(semantic_document.document_category.id)
            and p.stem.endswith(str(semantic_document.uuid))
            and p.suffix == ".pdf"
        )

        assert pdf_file.exists(), f"Could not find exported file {pdf_file}"

        # Read the PDF and verify annotations
        with open(pdf_file, "rb") as f:
            pdf_reader = PyPDF2.PdfReader(f)

            # Check all pages for the annotation since we can't guarantee page ordering
            annotation_found = False
            for pdf_page in pdf_reader.pages:
                if not pdf_page.annotations:
                    continue

                if len(pdf_page.annotations) != 3:
                    continue

                # Try to find our annotation in this page
                for annot_ref in pdf_page.annotations:
                    annotation = annot_ref.get_object()
                    if annotation["/Subtype"] == "/Highlight" and annotation["/C"] == [
                        1,
                        1,
                        0,
                    ]:  # Yellow in RGB
                        annotation_found = True

                        # Verify the annotation position (with some tolerance for rounding)
                        page_height = float(pdf_page.mediabox.height)
                        page_width = float(pdf_page.mediabox.width)

                        expected_left = 0.3 * page_width
                        expected_width = 0.1 * page_width
                        expected_top = page_height - (0.2 * page_height)
                        expected_height = 0.05 * page_height

                        rect = annotation["/Rect"]
                        rect_left = float(rect[0])
                        rect_right = float(rect[2])
                        rect_bottom = float(rect[1])
                        rect_top = float(rect[3])

                        # Allow for small rounding differences
                        assert abs(rect_left - expected_left) < 1  # Left
                        assert (
                            abs(rect_right - (expected_left + expected_width)) < 1
                        )  # Right
                        assert (
                            abs(rect_bottom - (expected_top - expected_height)) < 1
                        )  # Bottom
                        assert abs(rect_top - expected_top) < 1  # Top
                        break

                if annotation_found:
                    break

            # Verify that we found the annotation
            assert (
                annotation_found
            ), "Could not find the expected annotation in any page of the PDF"


# Testing package_offline_version with 000 Hypodossier Datenextraktion.xlsx
def test_package_offline_version_with_xlsx(db):
    files, file_exist = check_excel_export_file_exist(enable_download=True)
    assert len(files) == 18
    assert file_exist


# Testing package_offline_version without 000 Hypodossier Datenextraktion.xlsx
def test_package_offline_version_with_no_xlsx(db):
    files, file_exist = check_excel_export_file_exist(enable_download=False)
    assert len(files) == 17
    assert not file_exist


def test_package_offline_version_merged_pdf(db):
    # Check that if account document_export_strategy is set to SINGLEPDF
    # then we have only one pdf file in the zip
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    account = dossier.account
    account.enable_download_extraction_excel = False
    account.document_export_strategy = "SINGLEPDF"
    account.save()
    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        # this mocking is necessary because in package_documents() it calls
        # requests.get() to download the PDFs which is routed to http://minio:9000
        # (which works locally but not on staging)
        with requests_mock.Mocker() as m:
            request_calls = m.get(requests_mock.ANY, content=content_callback)
            package_offline_version(semantic_dossier, temp_path, False)

            assert request_calls.call_count == 29

            file = list(temp_path.iterdir())[0]
            files = zipfile.ZipFile(file).namelist()
            assert len(files) == 1


def test_package_offline_version_merged_pdf_add_uuid_suffix(db):
    # Check that uuid is added as suffix
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    account = dossier.account
    account.enable_download_extraction_excel = False
    account.document_export_strategy = "SINGLEPDF"
    account.save()
    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        # this mocking is necessary because in package_documents() it calls
        # requests.get() to download the PDFs which is routed to http://minio:9000
        # (which works locally but not on staging)
        with requests_mock.Mocker() as m:
            request_calls = m.get(requests_mock.ANY, content=content_callback)
            package_offline_version(
                semantic_dossier, temp_path, False, add_uuid_suffix=True
            )

            assert request_calls.call_count == 29

            file = list(temp_path.iterdir())[0]
            files = zipfile.ZipFile(file).namelist()
            assert len(files) == 1

            assert str(semantic_dossier.uuid) in files[0]


def test_package_offline_version_with_xlsx_wrong_file(db):
    files, file_exist = check_excel_export_file_exist(
        enable_download=True, xlsx_file_name="messedupFile"
    )
    assert len(files) == 18
    assert not file_exist


def check_excel_export_file_exist(
    enable_download: bool, xlsx_file_name="000 Hypodossier Datenextraktion.xlsx"
):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    dossier.account.enable_download_extraction_excel = enable_download
    dossier.account.save()
    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        # this mocking is necessary because in package_documents() it calls
        # requests.get() to download the PDFs which is routed to http://minio:9000
        # (which works locally but not on staging)
        with requests_mock.Mocker() as m:
            request_calls = m.get(requests_mock.ANY, content=content_callback)
            package_offline_version(semantic_dossier, temp_path, False)

            assert request_calls.call_count == 31

            file = list(temp_path.iterdir())[0]
            files = zipfile.ZipFile(file).namelist()
            file_exist = xlsx_file_name in files

    return files, file_exist


def test_package_document(db):
    logger.info(f"root={ASSETS_PATH.parent}")

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_dossier = prepare_semantic_dossier(dossier)

    with temporary_path() as temp_path:
        with requests_mock.Mocker() as m:
            request_calls = m.get(requests_mock.ANY, content=content_callback)
            document_package = package_document(semantic_dossier, temp_path)
            assert request_calls.call_count == 29
            assert document_package.files[0].exists()
            assert len(document_package.files) == 1
