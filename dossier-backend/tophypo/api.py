from datetime import timed<PERSON><PERSON>

import structlog
from django.conf import settings
from django.db import transaction
from django.http import Http404, HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.utils import timezone
from ninja import NinjaAP<PERSON>, UploadedFile, File, Form
from ninja.errors import ValidationError
from ninja.security import Http<PERSON>earer

from core.generics import AnyHttpUrlStr
from core.publisher import publish
from core.schema import Message, Error
from dossier import schemas as dossier_schema
from dossier import services_external as dossier_services_external
from dossier.helpers_api import handle_api_validation_error
from dossier.services_external import (
    create_dossier_api,
    update_dossier_api,
    get_dossier_with_access_check_api,
)
from dossier.models import Dossier
from dossier.services import get_grant_token_for_dossier

# from dossier.models import (
#     OriginalFile,
# )
# from dossier.services_external import (
#     serialize_semantic_document,
# )
from projectconfig.authentication import (
    authenticate_from_account,
)
from projectconfig.settings import (
    ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
)

# from semantic_document.models import SemanticDocument
from semantic_document.services import (
    export_semantic_document_wrapper_with_access_check,
)

# from semantic_document import (
#     helpers as semantic_document_helpers,
#     models as semantic_document_models,
#     schemas as semantic_document_schemas,
# )
from workers.models import SemanticDocumentExport
from tophypo.schemas.schemas import (
    SemanticDocumentPDFExportRequest,
)
import tophypo.schemas.schemas as tophypo_schemas


logger = structlog.get_logger()


class TophypoJWTAuth(HttpBearer):
    def authenticate(self, request, token, *args, **kw):
        jwt = authenticate_from_account(token)
        if settings.API_ROLE not in jwt.user_roles:
            return
        return jwt


api = NinjaAPI(
    title="Hypodossier - Tophypo API",
    csrf=False,
    auth=TophypoJWTAuth(),
    urls_namespace="tophypo-api",
    version="0.1.0",
    servers=[],
)


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


def map_dossier(dossier):
    # Same as SWISSFEX, but keep duplicate code, as dossier schema might diverge
    return tophypo_schemas.Dossier(
        uuid=dossier.uuid,
        external_dossier_id=str(dossier.external_id),
        updated_at=dossier.updated_at,
        created_at=dossier.created_at,
    )


@api.get("/ping", response={200: Message}, url_name="ping", exclude_none=True)
def ping(request):
    return Message(detail="pong")


@api.post(
    "/dossier",
    response={201: tophypo_schemas.Dossier, 409: Error},
    url_name="create-dossier",
    exclude_none=True,
)
def create_dossier(request, dossier_create: tophypo_schemas.CreateDossier):
    return create_dossier_api(request=request, dossier_create=dossier_create)


@api.patch(
    "/dossier/{external_dossier_id}",
    response={201: tophypo_schemas.Dossier, 409: Error, 404: Message},
    url_name="update-dossier",
    exclude_none=True,
)
def update_dossier(
    request, external_dossier_id: str, dossier_change: tophypo_schemas.ChangeDossier
):
    """Updates a new Dossier based on the provided parameters

    We provide a external_dossier_id as part of the URL and allow the client to change it
    as part of ChangeDossier
    """

    return update_dossier_api(
        request=request,
        external_dossier_id=external_dossier_id,
        dossier_change=dossier_change,
    )


@api.post(
    "/dossier/{external_dossier_id}/original-files",
    response={201: dossier_schema.CreatedObjectReference, 409: Message, 404: Message},
    url_name="add-original-file",
    exclude_none=True,
)
@transaction.atomic
def add_original_file(
    request,
    external_dossier_id: str,
    file: UploadedFile = File(...),
    allow_duplicate_and_rename: bool = Form(False),
):
    """Add an original file to a dossier"""
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    response_code, original_file = dossier_services_external.add_original_file(
        dossier=dossier,
        file=file,
        allow_duplicate_and_rename=allow_duplicate_and_rename,
    )

    return response_code, original_file


@api.post(
    "/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}",
    response={200: tophypo_schemas.SemanticDocumentPDFExportRequest, 404: Message},
    url_name="export-dossier-semantic-document-pdf",
)
def export_semantic_document_pdf(
    request,
    external_dossier_id,
    semantic_document_uuid,
):
    request = export_semantic_document_wrapper_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        external_dossier_id=external_dossier_id,
        semantic_document_uuid=semantic_document_uuid,
    )

    publish(
        message=request.model_dump_json().encode(),
        routing_key=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    )

    return SemanticDocumentPDFExportRequest(
        uuid=request.semantic_document_pdf_request_uuid,
    )


@api.get(
    "/export/{semantic_document_export_request_uuid}/status",
    response=tophypo_schemas.ExportStatus,
    url_name="dossier-semantic-document-export-status",
    exclude_none=True,
)
def get_available_export(request, semantic_document_export_request_uuid: str):
    # Check the status of an individual document

    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account

    export_semantic_document: SemanticDocumentExport = get_object_or_404(
        SemanticDocumentExport, uuid=semantic_document_export_request_uuid
    )

    # Check that user has access to an associated account
    if export_semantic_document.semantic_document.dossier.account != account:
        raise Http404("No permission to access this dossier")

    status = tophypo_schemas.ExportProcessingStatus.PROCESSING
    dossier_url = None
    dossier_file_uuid = None
    if export_semantic_document.done:
        status = tophypo_schemas.ExportProcessingStatus.PROCESSED
        dossier_url = export_semantic_document.file.get_fast_url()
        dossier_file_uuid = export_semantic_document.file.uuid

    return tophypo_schemas.ExportStatus(
        semantic_document_export_request_uuid=semantic_document_export_request_uuid,
        status=status,
        dossier_url=dossier_url,
        dossier_file_uuid=dossier_file_uuid,
        updated_at=export_semantic_document.done,
    )


# @api.delete(
#     "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}",
#     response={200: semantic_document_schemas.SavingResultWithMessage},
#     url_name="semantic-document-soft-delete",
# )
# def soft_delete_semantic_document(
#     request, external_dossier_id: str, semantic_document_uuid: UUID
# ):
#     dossier = get_dossier_with_access_check_api(
#         dossier_user=request.auth.get_user_or_create(),
#         is_manager=True,  # Fix this to do proper ownership
#         external_dossier_id=external_dossier_id,
#     )

#     semantic_document_helpers.semantic_restore_or_delete(
#         dossier,
#         semantic_document_models.SemanticDocument.all_objects,
#         semantic_document_uuid,
#         True,
#     )

#     return semantic_document_schemas.SavingResultWithMessage(message="deleted")


@api.delete(
    "/dossier/{external_dossier_id}",
    response={202: Message, 404: Message},
    url_name="dossier-delete",
    exclude_none=True,
)
def delete_dossier(request, external_dossier_id):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    dossier.expiry_date = timezone.now() - timedelta(days=1)
    dossier.save()

    return 202, {"detail": "Dossier Scheduled for deletion"}


# @api.get(
#     "/dossiers-unchanged/{num_days_documents_unchanged}",
#     response={200: List[tophypo_schemas.Dossier], 404: Message},
#     url_name="dossiers-unchanged",
#     exclude_none=True,
#     description="Show dossiers which have remained unchanged for >= num_days_documents_unchanged."
#     "Does not show expired Dossiers",
# )
# def get_unchanged_dossiers(request, num_days_documents_unchanged: float):
#     dossier_user = request.auth.get_user_or_create()
#
#     dossiers = get_dossiers_last_change(
#         # Note: we use Dossier.objects, which hides expired dossiers
#         # use Dossier._base_manager if we want all of them
#         dossiers_qs=Dossier.objects.filter(account=dossier_user.account),
#         time_delta=timedelta(days=num_days_documents_unchanged),
#     )
#     return [map_dossier(dossier) for dossier in dossiers]


@api.get(
    path="/{external_dossier_id}/token",
    response={200: str, 401: Message},
    url_name="cdp-dossier-access-token",
)
def get_dossier_grant_token(request, external_dossier_id: str):

    account_key = request.auth.account_key
    dossier = Dossier.objects.filter(
        account__key=account_key, external_id=external_dossier_id
    )
    if dossier.exists() is False:
        return 404, {
            "detail": f"Dossier with external id {external_dossier_id} does not exist"
        }
    return get_grant_token_for_dossier(dossier.first())


@api.get(
    "/dossier/{external_dossier_id}/show",
    response={302: AnyHttpUrlStr},
    auth=None,
    url_name="show-dossier",
)
def show_dossier(
    request,
    external_dossier_id: str,
) -> HttpResponse:
    """
    Redirects to the dossier for the user to view it if it exists or returns a 404
    """
    dossier = get_object_or_404(Dossier, external_id=external_dossier_id)
    account = dossier.account
    response_url = f"{account.dmf_endpoint}/dossier/{dossier.uuid}/view/page"
    return HttpResponseRedirect(redirect_to=response_url)
