import json
import os
import uuid
from typing import List

import pytest
from django.conf import settings

from faker import Faker

from tophypo.schemas.schemas import AccountName
from core.authentication import AuthenticatedClient

from dossier.models import (
    Account,
    DocumentCategory,
    JWK,
)
from dossier.services import create_expiration_date
from projectconfig.jwk import load_jwk_from_env
from tophypo.factories import TophypoAccountFactoryFaker
import jwt
from jwcrypto import jwk
from tophypo.tests.data import DATA_PATH


@pytest.fixture(scope="session")
def faker():
    return Faker(locale="de_CH")


@pytest.fixture
def tophypo_account():
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="tophypo development account",
            default_bucket_name="dms-default-bucket",
            dmf_endpoint="https://www.localhost",
        ),
        key=AccountName.tophypot.value,
    )
    return account


@pytest.fixture(scope="session")
def mock_jwks_public_private():
    # JWKS with both public and private keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()


@pytest.fixture
def mock_jwks_public():
    # JWKS with only public keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    ).model_dump(exclude_unset=True)


@pytest.fixture
def set_tophypo_JWK(tophypo_account, mock_jwks_public):
    # Set public key used for tophypo authentication
    return JWK.objects.create(jwk=mock_jwks_public["keys"][1], account=tophypo_account)


@pytest.fixture(scope="session")
def token_data(faker):
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-tophypo-first service-tophypo-last",
        "given_name": "service-tophypo-first",
        "family_name": "service-tophypo-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        "external_dossier_id": str(faker.uuid4()),
        # during dossier creation via API parameter
        "user_roles": ["api_role"],
        "account_key": AccountName.tophypot.value,
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


@pytest.fixture(scope="session")
def mock_token(mock_jwks_public_private, token_data):
    # need a PEM-formatted key
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,
        algorithm="RS256",
    )


@pytest.fixture
def tophypo_account_factory(tophypo_account):
    return TophypoAccountFactoryFaker(account=tophypo_account)


@pytest.fixture
def document_categories(tophypo_account_factory) -> List[DocumentCategory]:
    return tophypo_account_factory.load_initial_document_categories()


@pytest.fixture(scope="session")
def tophypo_authenticated_client(mock_token):
    return AuthenticatedClient(mock_token)


@pytest.fixture(scope="session")
def tophypo_miss_signed_authenticated_client(mock_jwks_public_private, token_data):
    """Test auth failure using key signed by tophypo"""
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][0]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    wrong_token = jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,  # use wrong key, key [0] from tophypo
        algorithm="RS256",
    )
    return AuthenticatedClient(wrong_token)
