from typing import Optional, List

import structlog
from django.shortcuts import get_object_or_404
from pydantic import StringConstraints, <PERSON>, BaseModel

from dossier.models import Account, DossierUser
from projectconfig import authentication
from typing_extensions import Annotated

logger = structlog.get_logger()


ExternalDossierID = Annotated[
    str, StringConstraints(max_length=255, pattern="[A-Za-z0-9-]{1,255}")
]
PrincipalID = Annotated[str, StringConstraints(max_length=255)]
DossierName = Annotated[str, StringConstraints(max_length=255)]


class DossierCreateJWT(BaseModel):
    exp: int = Field(
        description="Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, "
        "ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, "
        "2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day "
        "is accounted for by exactly 86400 seconds, other than that non-integer values can be "
        "represented. See RFC 3339 [RFC3339] for details regarding date/times in general "
        "and UTC in particular."
    )

    account_key: str  # "swissfex"

    first_name: str  # "service-swissfex-first"
    last_name: str  # "service-swissfex-last"
    email: str  # "<EMAIL>"
    username: str  # "service-account-swissfex"

    external_dossier_id: Optional[str] = None

    user_roles: List[str] = []

    def get_user_or_create(self) -> DossierUser:
        try:
            account = get_object_or_404(Account, key=self.account_key)

            # Not 100% sure on this - do we need this for swissfex?
            # validate_whitelistaccess(account, {'account_name': self.account_name})

            return authentication.get_user_or_create(
                account=account,
                username=self.username,
                email=self.email,
                fname=self.first_name,
                lname=self.last_name,
            )
        except Exception as e:
            logger.warning("Could not get or create user from JWT", error=e)
            raise e
