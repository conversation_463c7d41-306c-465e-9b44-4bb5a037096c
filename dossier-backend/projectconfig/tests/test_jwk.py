import json
import os

import pytest

from projectconfig.jwk import (
    load_and_validate_jwk_from_file,
    load_jwk_from_env,
    JWKPairList,
    convert_jwk_to_pem,
)
import projectconfig

from jwcrypto import jwk as jwcrypto_jwk
import jwt

from projectconfig.tests.data import DATA_PATH


def test_load_and_validate_jwk_valid_file():
    jwk_data = load_and_validate_jwk_from_file(DATA_PATH / "jwks-example.json")
    assert isinstance(jwk_data, JWKPairList)
    assert len(jwk_data.keys) > 0


def test_load_and_validate_jwk_invalid_file_structure():
    with pytest.raises(Exception):
        load_and_validate_jwk_from_file(DATA_PATH / "jwks-invalid.json")


def test_load_and_validate_jwk_nonexistent_file():
    with pytest.raises(FileNotFoundError):
        load_and_validate_jwk_from_file(DATA_PATH / "nonexistent_file.json")


def test_load_jwk_from_env_none_path(mocker):
    mocker.patch("projectconfig.jwk.logger.warning")
    jwk_data = load_jwk_from_env(None)
    assert jwk_data is None
    projectconfig.jwk.logger.warning.assert_called_once_with("JWK path not set")


def test_load_jwk_from_env_valid_path(mocker):
    mocker.patch(
        "projectconfig.jwk.load_and_validate_jwk_from_file",
        return_value=JWKPairList(keys=[]),
    )
    jwk_data = load_jwk_from_env("valid_jwk.json")
    assert isinstance(jwk_data, JWKPairList)


def test_load_jwk_from_env_invalid_file_structure(mocker):
    mocker.patch(
        "projectconfig.jwk.load_and_validate_jwk_from_file",
        side_effect=Exception("Invalid Structure"),
    )
    mocker.patch("projectconfig.jwk.logger.warning")
    jwk_data = load_jwk_from_env("invalid_structure_jwk.json")
    assert jwk_data is None
    projectconfig.jwk.logger.warning.assert_called_once()


def test_load_jwk_from_env_nonexistent_file(mocker):
    mocker.patch(
        "projectconfig.jwk.load_and_validate_jwk_from_file",
        side_effect=FileNotFoundError,
    )
    mocker.patch("projectconfig.jwk.logger.warning")
    jwk_data = load_jwk_from_env("nonexistent_file.json")
    assert jwk_data is None
    projectconfig.jwk.logger.warning.assert_called_once()


def test_encrypt_decrypt_with_jwk_success():
    # Test that we can encrypt and decrypt with the same JWKS
    # Load the JWK
    jwk_data = load_jwk_from_env(jwk_path=os.path.join(DATA_PATH, "jwks-example.json"))

    assert jwk_data is not None

    key_data = jwk_data.model_dump()["keys"][0]

    # Construct the key object
    private_key = convert_jwk_to_pem(key_data)

    # Encrypt some data
    payload = {"claim": "value", "aud": "account"}

    token = jwt.encode(payload, private_key, algorithm="RS256")

    decoded_payload = jwt.decode(
        jwt=token,
        key=convert_jwk_to_pem(key_data, private_key=False),
        audience="account",
        algorithms=["RS256"],
    )

    # Assert that the decoded payload match the original
    assert payload == decoded_payload


def test_encrypt_decrypt_with_jwk_public_key_only_success():
    # Test that we can encrypt with a JWKS containing public/private key pairs
    # and decrypt with the public key only

    # Load the JWKS with public/private key pairs
    jwks_data = load_jwk_from_env(jwk_path=os.path.join(DATA_PATH, "jwks-example.json"))
    # Encrypt some data
    payload = {"claim": "value"}

    token = jwt.encode(
        payload,
        convert_jwk_to_pem(jwks_data.model_dump()["keys"][0]),
        algorithm="RS256",
    )

    jwks_public_data = load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    )

    assert payload == jwt.decode(
        token,
        key=convert_jwk_to_pem(
            jwks_public_data.model_dump(exclude_unset=True)["keys"][0]
        ),
        algorithms=["RS256"],
    )


def test_encrypt_decrypt_with_jwk_failure():
    # Generate a key
    generated_key = jwcrypto_jwk.JWK.generate(
        kty="RSA", kid="swissfexd", size=2048, use="sig", alg="RS256"
    )

    generated_public_key = convert_jwk_to_pem(generated_key, private_key=False)

    # Load the JWK
    jwk_data = load_jwk_from_env(jwk_path=os.path.join(DATA_PATH, "jwks-example.json"))

    assert jwk_data is not None

    key_data = jwk_data.model_dump()["keys"][0]

    # Encrypt some data
    payload = {"claim": "value"}

    token = jwt.encode(payload, convert_jwk_to_pem(key_data), algorithm="RS256")

    # Attempt to decode the token with the wrong key
    with pytest.raises(jwt.exceptions.InvalidSignatureError):
        jwt.decode(token, key=generated_public_key, algorithms=["RS256"])

    # Correctly decode the token
    decoded_payload = jwt.decode(
        token, key=convert_jwk_to_pem(key_data, private_key=False), algorithms=["RS256"]
    )

    # Assert that the decoded payload match the original
    assert payload == decoded_payload

    # Encode and decode using the generated key

    private_key = json.loads(generated_key.export())

    token = jwt.encode(payload, convert_jwk_to_pem(private_key), algorithm="RS256")

    assert (
        jwt.decode(
            token,
            key=convert_jwk_to_pem(private_key, private_key=False),
            algorithms=["RS256"],
        )
        == payload
    )
