from projectconfig.settings import *

DATABASES["default"]["NAME"] = "test_dms"

KEYCLOAK_PRIVATE_KEY = """**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
PUBLIC_KEY = """-----BEGIN RSA PUBLIC KEY-----
MIICCgKCAgEA3PVy/h5M9qFbXTyARzNzZ0xlEZknpZaaYzZ2gnlVmphB1aQvknBi
/4lFrPgQehlZEqn+F1Dc0harv8Z4X72Gf6EeoXUEzdOvQ5LPLtRS/RfDqpMNN/sY
sF/bql6DBCpjOm52dD9avps5CB5499uJH7peQhzyYMC+aW7RsKQaSiScVs2V71VF
IYF6FNW6D2eU9l/uHH3/rlmxuVSf8cWwmiHeMDmnHAhT/ZZgyzZeDZwFds6wShgL
JjSVuNGKxqKrYb5od0VQY5rhnzdOENrpe7z89ONgVSD9fCCGV+8G6cs/YAm3k9rA
w+JINvE/XiMxS4SAqGD9nYruaQfgbaMksm1x3pkq7L+za2/wysI7aLCq9bLIwirz
ytktOhWUsMwxfsFUI73zjZL7e5qDQ6fEb/xuyYZcjxJ16CXll9SwPTLnaw0PW2ei
7skFC/WWrz8reDo5AMIH6ZZBQDB84KcS5DLasU5jUgufIqmW0gip1OHbSlulbyxD
2H5IjHRQFZXnavlWjThYMoWvepvyU9NJ+w3lVBYyGIn4bDre2AIk8dRCsUEVjBu3
jobF+YY+RbwmVcFwK1ufTFEHowTskvCFZYqHa3n47xEoouLDXHnfHnDF41taC5OA
oqq74NJhFv2cQyCwsQ0E2GpxKUcKCkRBJ3sEt+R8+65xvakAS2ARV2cCAwEAAQ==
-----END RSA PUBLIC KEY-----"""

MANAGEMENT_PUBLIC_KEY = """-----BEGIN RSA PUBLIC KEY-----
MIICCgKCAgEA3PVy/h5M9qFbXTyARzNzZ0xlEZknpZaaYzZ2gnlVmphB1aQvknBi
/4lFrPgQehlZEqn+F1Dc0harv8Z4X72Gf6EeoXUEzdOvQ5LPLtRS/RfDqpMNN/sY
sF/bql6DBCpjOm52dD9avps5CB5499uJH7peQhzyYMC+aW7RsKQaSiScVs2V71VF
IYF6FNW6D2eU9l/uHH3/rlmxuVSf8cWwmiHeMDmnHAhT/ZZgyzZeDZwFds6wShgL
JjSVuNGKxqKrYb5od0VQY5rhnzdOENrpe7z89ONgVSD9fCCGV+8G6cs/YAm3k9rA
w+JINvE/XiMxS4SAqGD9nYruaQfgbaMksm1x3pkq7L+za2/wysI7aLCq9bLIwirz
ytktOhWUsMwxfsFUI73zjZL7e5qDQ6fEb/xuyYZcjxJ16CXll9SwPTLnaw0PW2ei
7skFC/WWrz8reDo5AMIH6ZZBQDB84KcS5DLasU5jUgufIqmW0gip1OHbSlulbyxD
2H5IjHRQFZXnavlWjThYMoWvepvyU9NJ+w3lVBYyGIn4bDre2AIk8dRCsUEVjBu3
jobF+YY+RbwmVcFwK1ufTFEHowTskvCFZYqHa3n47xEoouLDXHnfHnDF41taC5OA
oqq74NJhFv2cQyCwsQ0E2GpxKUcKCkRBJ3sEt+R8+65xvakAS2ARV2cCAwEAAQ==
-----END RSA PUBLIC KEY-----"""

TEST = True
API_ACCOUNT_KEY_ZKB = "zkbd"

DEBUG_ENABLE_SILK = False

INSTANCE_SIGNING_KEY = '{"d":"A-L8wY7qLip4jn9iDzSsgnRc8iaEzOIQ9imzDHVMUfQveV-PTuA3dtD1BkNX1uYqJvjyOcum418r3yKW3CmPiYClMe5yqBrG0zbVCNhfMhxD-ufS6wuOXmD4GD-07BKd-WBBzwrJCt20BeWrE1fpjvKbEMs7iVsxgMzC3CRnyBvZGKSR3tiALA9WCO7onog-eUxLSwTIlnOf1TBc_IU4aznoO81Gdy0qYdEobED2ZXy1Ha8Ukx57pSoSVl04fg72KisoJq51pM_jyaRhIP_sqsYqHoNIpGCF6BF8zOREAR501_ZOvsMPJAQ2LdbA_eYwIB2nFeQXDbGgBmO1ZLoV2Q","dp":"caq5Z2nOktAWdOFmYLubpi2_tASMWA9fJNdhBw0sbtK0iPwSkF1bmVgvh1fVyCcJpbvmpmRk3E9Z3_VeCgzhYSBQZx986mbp7CNKal76zTdJHVpN_AkNyl4PXOn1pwWb8dRNDTT5DC9GBFLvxaHA7VHZhRlhDmZewjQwh94d7lU","dq":"RnEiz26rjJGEC_u4yPhAeYopO6BLB46oJqubpzav-vbaLEtWW1KzWcxYEF8fYysz0Iuo6iPDEiTghvow3LhfckzDa2qjnqt8RMIPL7LYqKuVHE3P9GnoKGo39zzwQwR3OWMoBv24k61irrm9dWAQnDiodTe1Dw1WfaRI_FWpOQs","e":"AQAB","kid":"1d37e30c-84db-4f44-9d7d-10b776eb2548","kty":"RSA","n":"nm2VqWS_PZiOTygNyXidTTwvyQOvp-2RqZ1pFLjzbfoMquqMsiCpxQbii_wA_nC07pCTLVv7IatdETzaxAokeOln37dcNwBOp7QfKA_NMFmgJIbNeS5eQAenicK53-QMOi9yPjEeOpXwdbZ3UvxKVdDfMyiG_Patzr8T-630rnTaxlfjc_4ZduWQ_gadsST9bu10s4ZjxoioXB-tfYss0GwVuF4rZeH6--VecNVXVgsJcEXWK-6CJkn2qKIqalavMxLB7XKDJNtbODQO9fg5po2ewgbC9SGTaX0iDBNb7t3Pd_fC9HGgxoTqpUEJ6ztY-CJSmdS6518v2cAz4xmMTw","p":"1Nudu4MTaTcZD4dMa3w3orr_YGP0IAIK155B1l1v2jCyr7koua5hQ6OxM0e7H2iuqBBq334rYfDfxdOGevwrjZPwz2RATBplAxLmAfS6npaDmZZD8YJqkvSb1FM0NrXMGplMOaCHEi4OYNAB72-BW4IHWeKJYxdG91a5QgWWTYU","q":"vonQJIf-LLRt0O_v_U5cbYhGpBqRyTkrMlf-7lOxtA-755Z0OzjiEnys8wxZU6f0X_mBwdEJegDaNFRl-FedIcKWm4eqeQMFbEAnvwU2djwC_99VR4TO52VnjNd-7VZ76izLA4lyU6ONd6Q_gzZXC2SP-A5kw5P9tP2bscGSgMM","qi":"JM6oFw81IesxQdFxI1Z5uWBjl9SzqluxzvFLa619LCJrLk2LqXj6y6wPKvNNnM_oZneagtRTJN0BmgB6l-gjPX1uNVaAc3rUWd6iBydRVni_XDcoL9qLQ-5EqUW21Kvm-FPxoTM6tXg1zcfCx77e-zT-ficsvVegtYGzAmj1-os"}'
