import json

import structlog
from django.conf import settings
from jwt import PyJWK
from pydantic import ValidationError
from django.core.exceptions import ValidationError as DjangoValidationError
from jwcrypto import jwk
from pydantic import BaseModel
from typing import List, Dict, Optional, Any, Union

logger = structlog.get_logger()


# Functions to load a JWKPairSet from a JSON file and validate it against the JWKPairSet model
class KeyPair(BaseModel):
    p: Optional[str] = None  # First Prime Factor
    kty: str  # Key Type
    q: Optional[str] = None  # Second Prime Factor
    d: Optional[str] = None  # Private Exponent)
    e: str  # (Public Exponent)
    use: str  # Public Key Use
    kid: str  # Key ID
    qi: Optional[str] = None  # First CRT Coefficient
    dp: Optional[str] = None  # First Factor CRT Exponent
    alg: str  # Algorithm
    dq: Optional[str] = None  # Second Factor CRT Exponent
    n: str  # Modulus


class JWKPairList(BaseModel):
    keys: List[KeyPair]


def validate_jwk(value: Dict[str, Any]) -> None:
    try:
        KeyPair.model_validate(value)
    except ValidationError as e:
        raise DjangoValidationError("Invalid JWK format: " + str(e))


def is_valid_jwk(value: Dict[str, Any]) -> bool:
    try:
        KeyPair.model_validate(value)
    except ValidationError:
        return False
    return True


def is_valid_jwks(value: Dict[str, Any]) -> bool:
    try:
        JWKPairList.model_validate(value)
    except ValidationError:
        return False
    return True


def load_and_validate_jwk_from_file(filename: str) -> JWKPairList:
    # Usage: settings = load_and_validate_settings('path_to_settings_file.json')
    with open(filename) as file:
        data = json.load(file)
    # Load the data into a JWKPairList to validate it
    return JWKPairList(**data)


def load_jwk_from_env(jwk_path: Optional[str]) -> Optional[JWKPairList]:
    # Usage: settings = load_jwk_from_env('path_to_settings_file.json')
    if jwk_path is None:
        logger.warning("JWK path not set")
        return None

    try:
        return load_and_validate_jwk_from_file(jwk_path)
    except Exception as e:
        logger.warning("Could not load JWK from env", error=e)
        return None


def convert_jwk_to_pem(
    jwks: Union[str, bytes, dict, jwk.JWK], private_key=None
) -> bytes:
    # Note: you must use a public key only (not private key) for jwt.decode
    # i.e. use private_key=False, if passing in a private key, so that the encoded pem
    # only contains a public key
    if isinstance(jwks, dict) or isinstance(jwks, jwk.JWK):
        jwk_key = jwk.JWK.from_json(json.dumps(jwks))
    elif isinstance(jwks, bytes):
        jwk_key = jwk.JWK.from_pem(jwks)
    else:
        jwk_key = jwk.JWK.from_pem(jwks.encode())

    if private_key is None:
        private_key = jwk_key.has_private

    return jwk_key.export_to_pem(private_key=private_key, password=None)


def extract_public_jwk(private_jwk: dict) -> dict:
    """
    Converts a private RSA JWK to a public JWK.
    Note: Decide on whether to commit to using jwcrypto (then switch to convert_jwk_to_pem) or to use pyjwt.
    """
    public_jwk = {
        "kty": private_jwk["kty"],  # Key Type remains the same
        "n": private_jwk["n"],  # Modulus remains the same
        "e": private_jwk["e"],  # Exponent remains the same
    }
    # Optionally, retain key ID (kid) if present
    if "kid" in private_jwk:
        public_jwk["kid"] = private_jwk["kid"]

    return public_jwk


def get_instance_public_key():
    jwk_dict = json.loads(settings.INSTANCE_SIGNING_KEY)
    return PyJWK(extract_public_jwk(jwk_dict))
