import json
from http import HTTPStatus
from typing import Optional, Any, Union, List, Tuple
from urllib.parse import parse_qs, urljoin

import structlog
from django.core.cache import cache
from django.db import ProgrammingError
from pydantic import ValidationError
from django.db.models import QuerySet

import jwt
import requests
from channels.db import database_sync_to_async
from django.conf import settings
from django.contrib.auth import authenticate as django_auth, get_user_model
from django.contrib.auth.models import User, AnonymousUser
from django.http import HttpRequest
from jwt import PyJWK
from ninja.security import HttpBasicAuth
from ninja.security import HttpBearer

from asgiref.sync import sync_to_async

from core.helpers import (
    optionally_update_user_model,
)
from projectconfig import schemas

from dossier.exceptions import HttpError
from dossier.models import Account, DossierUser, JWK
from dossier.services import validate_whitelistaccess
from projectconfig.jwk import (
    JWK<PERSON>airList,
    convert_jwk_to_pem,
    is_valid_jwk,
    get_instance_public_key,
)
from projectconfig.roles import INTERNAL_ROLE
from projectconfig.schemas import DossierCreateJWT

logger = structlog.get_logger(__name__)

"""How does authentication work?

We have two sets of apis, internal apis used for the dossier frontend and external apis used for clients

1. Internal APIs

These rely on tokens signed by keycloak

The user logs in via keycloak, this gives them a signed access token and a refresh token 
(to fetch new access tokens). A copy of the public key is given to dossier-backend
and the signed token is authenticated against it

The backend internal APIs, are here 
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/projectconfig/api.py?ref_type=heads

The authentication class they use is here 
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/projectconfig/authentication.py?ref_type=heads#L180

which inherits from 
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/projectconfig
/authentication.py?ref_type=heads#L88

That authenticates across a public key provided by keycloak
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/projectconfig/authentication.py?ref_type=heads#L99

On auth failure it attempts to fetch a new key from keycloak

https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/projectconfig/authentication.py?ref_type=heads#L110

https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/projectconfig/authentication.py?ref_type=heads#L166-173

Note that we first attempt to use JWKs stored in the database here as well (like in the external api, as such there is shared code between the two),
but as a fallback we use the public key coming from the environment.

2. External APIs

The clients e.g. Swissfex or ZKB sign their own tokens, and provide us with a public key
which we store against their account in the database
The JWK model stores the public key and the account it belongs to
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/dossier/models.py?ref_type=heads#L139-149

E.g. for swissfex external api, auth occurs here
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/swissfex/api.py?ref_type=heads#L40

Which calls https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/swissfex/auth.py?ref_type=heads

Which calls authenticate from account
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/swissfex/auth.py?ref_type=heads#L27
master
https://gitlab.com/hypodossier/document-universe/-/blob/master/dossier-backend/projectconfig/authentication.py?ref_type=heads#L314-364

The authenticate_from_account looks at the account name in the token, attempts to load any public keys saved 
associated with that account and then authenticates
"""


def set_attr(model, attr, value):
    if value is not None:
        model.__setattr__(attr, value)
    return model


def get_user_or_create(
    account: Account,
    username: str,
    email: str = None,
    fname: str = None,
    lname: str = None,
) -> DossierUser:
    defaults = {}
    if email is not None:
        defaults["email"] = email

    if fname is not None:
        defaults["first_name"] = fname

    if lname is not None:
        defaults["last_name"] = lname

    # Take the shortest route first - assume user and account already exist
    dossier_user = (
        DossierUser.objects.prefetch_related("account", "user")
        .filter(user__username=username, account=account)
        .first()
    )

    # Return if this is the case
    if dossier_user is not None:
        assert dossier_user.user.is_active is True
        optionally_update_user_model(dossier_user.user, defaults)
        return dossier_user

    # Else, maybe account does not exist
    user = get_user_model().objects.filter(username=username).first()

    if user is not None:
        # If user exists, check to see if parameters need updating
        optionally_update_user_model(user, defaults)
    else:
        # Else create user
        user: User = get_user_model().objects.create_user(username=username, **defaults)

    assert user.is_active is True

    # Create dossier user
    # We could use create instead of get_or_create, but for safety we use get_or_create
    # as under the hood it uses a transaction
    dossier_user, _ = DossierUser.objects.prefetch_related(
        "account", "user"
    ).get_or_create(user=user, account=account)
    return dossier_user


class BasicAuth(HttpBasicAuth):
    def authenticate(
        self, request: HttpRequest, username: str, password: str
    ) -> Optional[Any]:
        return django_auth(request, username=username, password=password)


def has_role(role: str, decoded_payload: dict):
    roles = decoded_payload.get("user_roles")
    return role in roles if roles and len(roles) > 0 else False


class Auth:
    def __init__(self, token):
        self.token = token
        self.decoded_payload = self.jwt_auth(token)
        self.account = None

    @staticmethod
    def jwt_auth(token: str) -> dict:
        # Try public keys saved on account
        try:
            validate_create_jwt: DossierCreateJWT = authenticate_from_account(token)
            if validate_create_jwt is not None:
                return jwt.decode(
                    token,
                    "",
                    algorithms=["RS256"],
                    options={"verify_signature": False},
                    audience="account",
                )
        except Exception:
            logger.info(
                "Could not validate the token with authenticate_from_account, will try public keys",
                token=str(token),
            )

        # Try instance and keycloak public keys
        keys_to_try: List[Tuple[str, bytes | PyJWK]] = [
            ("instance", get_instance_public_key()),
            ("keycloak", settings.PUBLIC_KEY),
        ]
        options = {"verify_signature": True, "exp": True}
        auth_attempt_failures = []
        signature_errors = 0

        for key_type, key in keys_to_try:
            try:
                return jwt.decode(
                    jwt=token,
                    key=key,
                    algorithms=["RS256"],
                    options=options,
                    audience="account",
                )
            except jwt.exceptions.InvalidSignatureError:
                auth_attempt_failures.append(f"Invalid signature with {key_type} key")
                signature_errors += 1
                logger.info(
                    "Invalid signature with key", key_type=key_type, token=token
                )
            except jwt.exceptions.ExpiredSignatureError:
                # Don't continue trying other keys if we know the token is expired
                logger.warning(
                    "Jwt signature has expired",
                    key_type=key_type,
                    token=token,
                )
                raise HttpError(HTTPStatus.UNAUTHORIZED, "Signature has expired")
            except Exception as e:
                auth_attempt_failures.append(f"Error with {key_type} key: {str(e)}")
                logger.exception(
                    "Unexpected error while decoding jwt.",
                    key_type=key_type,
                    token=token,
                )

        if signature_errors == len(keys_to_try):
            logger.error(
                "Signature error with all keys.",
                failures=auth_attempt_failures,
            )
        else:
            logger.error(
                "Authentication failed with all keys.", failures=auth_attempt_failures
            )

        raise HttpError(
            HTTPStatus.UNAUTHORIZED, "Could not validate token with any available key."
        )

    def has_role(self, role: str) -> bool:
        roles = self.decoded_payload.get("user_roles")
        return role in roles if roles and len(roles) > 0 else False

    def jwt_check_user_is_manager(self):
        return has_role(
            role=settings.NAME_OF_ROLE_MANAGER_FROM_JWT,
            decoded_payload=self.decoded_payload,
        )

    def get_user_or_create(self):
        account = self.get_account()

        return get_user_or_create(
            account=account,
            username=self.decoded_payload["preferred_username"],
            email=self.decoded_payload.get("email"),
            fname=self.decoded_payload.get("given_name"),
            lname=self.decoded_payload.get("family_name"),
        )

    def external_dossier_id(self) -> str:
        return self.decoded_payload.get("external_dossier_id")

    def get_account(self):
        # Cache the account in the object, as we call it twice
        if self.account is None:
            if self.decoded_payload.get("account_key"):
                account_key = self.decoded_payload.get("account_key")
                account = Account.objects.prefetch_related("whitelist_accesses").get(
                    key=account_key
                )
            else:
                account = Account.objects.prefetch_related("whitelist_accesses").get(
                    name=settings.HARDCODED_NAME_OF_HYPPODOSSIER_ACCOUNT
                )
            self.account = account
        else:
            account = self.account
        return account

    def validate_whitelistaccess(self):
        account = self.get_account()
        validate_whitelistaccess(account, self.decoded_payload)

    def authenticate(self) -> DossierUser:
        self.validate_whitelistaccess()
        return self.get_user_or_create()


def get_keycloak_jwks():
    data = requests.get(
        urljoin(settings.KEYCLOAK_REALM_ENDPOINT, ".well-known/openid-configuration")
    ).json()

    jwks_uri = data["jwks_uri"]

    return requests.get(jwks_uri).json()


class JWTAuthRequest(HttpRequest):
    auth: DossierUser
    user: DossierUser
    is_manager: bool
    is_internal: bool
    external_dossier_id: str
    jwt: dict


class JWTAuth(HttpBearer):
    sync_view_name = "api:ninja.operation._sync_view"

    @staticmethod
    def authenticate(
        request: JWTAuthRequest, token, *args, **kw
    ) -> Optional[DossierUser]:
        if not token:
            logger.warning("Token not provided")
            return None

        try:
            auth = Auth(token)
            user = auth.authenticate()
        except ProgrammingError as e:
            logger.error(
                "Programming error during authentication. DB is not ready. Did you run all migrations?",
                e=e,
            )
            return None
        except Exception:
            logger.warning("Could not authenticate user with public key")
            return None

        request.is_manager = auth.jwt_check_user_is_manager()
        request.is_internal = auth.has_role(INTERNAL_ROLE)
        request.external_dossier_id = auth.external_dossier_id()
        request.jwt = auth.decoded_payload
        if isinstance(request.user, AnonymousUser):
            request.user = user
        return user


async def async_auth(request, *args, **kw):
    """
    Async wrapper around JWTAuth auth class. Used for Django Ninja aync auth, using existing
    sync auth class.
    """
    try:
        token = request.headers["Authorization"].split(" ")[1]
        decoded_payload = await sync_to_async(JWTAuth.authenticate)(request, token)
        return decoded_payload
    except Exception:
        logger.exception("unable to validate token", exc_info=True)
        return None


class QueryAuthMiddleware:
    """
    Custom middleware (insecure) that takes user IDs from the query string.
    """

    def __init__(self, app):
        # Store the ASGI application we were passed
        self.app = app

    async def __call__(self, scope, receive, send):
        self.scope = dict(scope)

        payload = parse_qs(scope["query_string"].decode())
        token = self._get_token(payload)
        auth = Auth(token)

        dossier_user = await database_sync_to_async(auth.authenticate)()
        scope["user"] = dossier_user.user
        scope["dossieruser"] = dossier_user
        scope["is_manager"] = auth.jwt_check_user_is_manager()
        scope["account"] = dossier_user.account
        scope["jwt"] = auth.decoded_payload

        return await self.app(scope, receive, send)

    def _get_token(self, payload):
        token = payload["token"]

        if token is None:
            raise Exception("There are more than 1 token.")
        elif len(token) > 1:
            raise Exception("The token has not been passed.")

        return token[0]


def authenticate_from_JWK(
    token: str, jwks: Union[str, dict]
) -> Union[schemas.DossierCreateJWT, None]:
    """Authenticate from a JWKS

    Args:
    token (str): A signed JWS to be verified.
    key (str or dict): A key to attempt to verify the payload with. Can be
        individual JWK or JWK set.
    """

    decoded_payload = None
    try:
        # jwks can either be a string or a dict of keys
        if isinstance(jwks, str) or is_valid_jwk(jwks):
            decoded_payload = jwt.decode(
                token,
                key=convert_jwk_to_pem(jwks, private_key=False),
                audience="account",
                algorithms=["RS256"],
                options={"verify_signature": True, "exp": True},
            )
        else:
            auth_attempt_failures = []
            for key_dict in jwks["keys"]:
                try:
                    decoded_payload = jwt.decode(
                        token,
                        convert_jwk_to_pem(key_dict, private_key=False),
                        audience="account",
                        algorithms=["RS256"],
                        options={"verify_signature": True, "verify_exp": True},
                    )
                except jwt.exceptions.InvalidSignatureError as e:
                    auth_attempt_failures.append(str(e))
                    continue
            if decoded_payload is None:
                logger.info(
                    "Could not decode jwt with jwks present on Account.",
                    auth_attempt_failures=auth_attempt_failures,
                )
                raise HttpError(401, "Failed to decode token")

    except jwt.exceptions.ExpiredSignatureError:
        if not settings.TEST:
            logger.warning("Token has expired")
        raise HttpError(401, "Token has expired")
    except jwt.exceptions.PyJWTError:
        if not settings.TEST:
            logger.warning("Failed to decode token")
        raise HttpError(401, "Failed to decode token")

    user_roles = decoded_payload.get("user_roles")
    if user_roles is None or user_roles == "" or user_roles == []:
        user_roles = []

    return schemas.DossierCreateJWT(
        exp=decoded_payload["exp"],
        account_key=decoded_payload["account_key"],  # "swissfex" or "swissfexd"
        first_name=decoded_payload["given_name"],  # "service-swissfex-first"
        last_name=decoded_payload["family_name"],  # "service-swissfex-last"
        username=decoded_payload["preferred_username"],  # "service-account-swissfex"
        email=decoded_payload["email"],  # "<EMAIL>"
        external_dossier_id=decoded_payload.get(
            "external_dossier_id"
        ),  # Optonal Parametere, Curren    tly not used and provided by post data
        user_roles=user_roles,
    )


def merge_jwk_qs(jwk_qs: QuerySet) -> JWKPairList:
    """
    Merge the jwk queryset into a single JWKPairList.

    Args:
    - jwk_qs: A QuerySet containing jwk data.

    Returns:
    - A JWKPairList containing merged keys from the queryset.
    A JWKPairList is equivalent to a JWKSet, declared as a pydantic model.
    """

    jwk_list = list(jwk_qs.all().values_list("jwk", flat=True))

    return JWKPairList(**{"keys": jwk_list})


def authenticate_from_account(token: str) -> DossierCreateJWT | None:
    """
    Load jwk from account and authenticate.

    Args:
    - token: The JWT token used for authentication.

    Returns:
    - The result of authentication using the jwk data.
    """
    # Decode the token without verifying the signature to retrieve the account key
    try:
        untrusted = jwt.decode(
            token,
            audience="account",
            algorithms=["RS256"],
            options={"verify_signature": False},
        )
        account_key = untrusted.get("account_key")

        if account_key is None:
            raise HttpError(401, "No account key supplied in token")
    except jwt.exceptions.PyJWTError:
        if not settings.TEST:
            logger.warning("Failed to decode token", exc_info=True)
        raise HttpError(401, "Invalid token")

    cache_key = f"jwk{account_key.replace(' ', '')}"
    cached_jwk = cache.get(cache_key)

    if cached_jwk:
        try:
            jwk = JWKPairList.model_validate_json(cached_jwk)
        except ValidationError as e:
            logger.warning(msg="Could not parse cached jwk", error=str(e))
            cache.delete(cache_key)
            cached_jwk = None

    if not cached_jwk:
        # Ensure the account and associated jwk exists
        jwk_qs = JWK.objects.filter(account__key=account_key, enabled=True)

        if not jwk_qs.exists():
            if not settings.TEST:
                logger.warning(f"No jwk found for account {account_key}")
            raise HttpError(401, "No Public key found for account")

        jwk: JWKPairList = merge_jwk_qs(jwk_qs)
        cache.set(
            cache_key, json.dumps(jwk.model_dump(exclude_unset=True)), timeout=60 * 5
        )

    return authenticate_from_JWK(token, jwk.model_dump(exclude_unset=True))


async def async_authenticate_from_account(request, *args, **kw):
    token = request.headers.get("Authorization", "").split(" ")[-1]
    return await sync_to_async(authenticate_from_account)(token)
