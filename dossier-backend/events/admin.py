from django.contrib import admin
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django_json_widget.widgets import JSO<PERSON><PERSON>orWidget
from import_export.admin import ExportMixin
from rangefilter.filters import DateRangeFilter

from events.models import Event


# Register your models here.
@admin.register(Event)
class EventAdmin(ExportMixin, admin.ModelAdmin):
    list_display = ["uuid", "type", "created_at"]
    list_filter = ("created_at", ("created_at", DateRangeFilter), "type")
    search_fields = ["uuid"]
    formfield_overrides = {
        JSONField: {"widget": JSONEditorWidget},
    }
