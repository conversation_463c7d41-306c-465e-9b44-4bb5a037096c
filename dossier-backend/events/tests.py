import datetime
import json

from dateutil import tz
from freezegun import freeze_time

from events.helpers import full_qualified_name
from events.models import Event
from events.schemas import EventDetail
from events.services import publish


class ExampleEvent(EventDetail):
    """Sample event class for testing purposes."""

    name: str


def test_full_qualified_name():
    test_event = ExampleEvent(name="test", username="testuser")
    assert full_qualified_name(test_event) == "events.tests.ExampleEvent"


@freeze_time("2012-01-14")
def test_publish(db):
    test_event = ExampleEvent(name="test", username="testuser")
    publish(test_event)
    event = Event.objects.get(uuid=test_event.uuid)
    assert event.type == full_qualified_name(test_event)
    assert event.details == json.loads(test_event.model_dump_json())
    assert event.created_at == datetime.datetime(2012, 1, 14, tzinfo=tz.UTC)
