import djclick as click
import structlog
from django.contrib.auth import get_user_model
from faker import Faker

from hypohaus.factories import HypohausAccountFactoryFaker

User = get_user_model()
logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
def load_account(
    account_key: str,
):
    """
    Create or update a hypohaus account with close to production config. This loads
    document categories.

    Example:

    python manage.py reset-db
    python manage.py load_hypohaus_data load-account hypohaus

    """
    Faker.seed(234777)
    bfac = HypohausAccountFactoryFaker(
        account_key=account_key,
    )
    bfac.account.save()
