from enum import Enum


class DossierState(str, Enum):
    OPEN = "OPEN"
    CLOSING = "CLOSING"
    CLOSED = "CLOSED"
    EXPORT_ERROR = "EXPORT_ERROR"


# When a Dossier work status moved into any of these states, the Dossier is considered read-only
# this is analogous to the Dossier.access_mode property, with final evaluation of the Dossier's
# access mode being determined by most restrictive by annotate_with_calculated_access_mode
# and dossier access grant
DOSSIER_READ_ONLY_WORK_STATUS = [
    "READY_FOR_EXPORT_DEAL",
    "READY_FOR_EXPORT_FICO",
    "READY_FOR_EXPORT_FIPLA",
    "READY_FOR_EXPORT_NO_DEAL",
    "EXPORT_ARCHIVE_AVAILABLE",
    "EXPORT_ERROR",
    "EXPORT_DONE",
    "CLOSING",
    "CLOSED",
]
