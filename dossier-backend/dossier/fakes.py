import random
from pathlib import Path
from typing import Op<PERSON>, <PERSON><PERSON>, List

import structlog
from django.core.files.base import ContentFile
from faker import Faker

from dossier.doc_cat_helpers import (
    load_document_categories_from_path,
)
from dossier.factories import (
    RandomUserFactory,
    FicoRoleFactory,
    AssigneeRoleFactory,
    SemanticDocumentFactory,
    DossierUserFactory,
    DossierFactory,
    AccountFactory,
    SemanticPageFactory,
)
from dossier.models import (
    PageCategory,
    DocumentCategory,
    DossierFile,
    OriginalFile,
    FileStatus,
    ExtractedFile,
    ConfidenceLevel,
    Account,
    Dossier,
    AssignedRealestatePropertyOriginalFile,
    JWK,
    BusinessCaseType,
)
from processed_file.models import (
    ProcessedFile,
    ProcessedPage,
    PageObject,
    PageObjectTitle,
    PageObjectType,
)
from semantic_document.helpers import get_start_work_status_from_dossier_account
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPagePageObject,
)
from statemgmt.configurations import DATA_PATH
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine


from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import io
from pdf2image import convert_from_bytes

logger = structlog.get_logger()

fake = Faker()


def get_random_document_category(
    account: Account, valid_document_category_keys: Optional[List[str]] = None
) -> Tuple[DocumentCategory, List[DocumentCategory]]:
    """
    Get a random document category from the account.
    @param account:
    @param valid_document_category_keys: Choice can be restricted e.g. like this ['PLAN_FLOOR']. If None is set all categories could be returned
    @return:
    """
    qs = DocumentCategory.objects.filter(account=account)

    if valid_document_category_keys:
        qs = qs.filter(name__in=valid_document_category_keys)

    document_categories = list(qs.all())
    document_category = random.choice(document_categories)
    return document_category, document_categories


def create_processed_page_law(
    dossier: Dossier, document_category: DocumentCategory, page_category: PageCategory
):
    img_file, pdf_file, txt_file = get_law_files()

    data = pdf_file.read_bytes()

    searchable_pdf_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=data, name="lawsimplicity.pdf"),
        bucket=dossier.bucket,
    )
    original_file = OriginalFile.objects.create(
        dossier=dossier,
        file=searchable_pdf_file,
        status=FileStatus.PROCESSED,
    )
    extracted_file = ExtractedFile.objects.create(
        dossier=dossier,
        original_file=original_file,
        path_from_original="lawssimplicity.pdf",
        status=FileStatus.PROCESSED,
        file=searchable_pdf_file,
    )
    processed_file = ProcessedFile.objects.create(
        dossier=dossier, extracted_file=extracted_file
    )
    image_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=img_file.read_bytes(), name=img_file.name),
        bucket=dossier.bucket,
    )
    searchable_txt_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=txt_file.read_bytes(), name=txt_file.name),
        bucket=dossier.bucket,
    )
    processed_page = ProcessedPage.objects.create(
        processed_file=processed_file,
        document_category=document_category,
        page_category=page_category,
        dossier=dossier,
        confidence_value=random.randint(90, 99) / 100,
        number=0,
        lang=dossier.lang,
        image=image_file,
        searchable_pdf=searchable_pdf_file,
        searchable_txt=searchable_txt_file,
    )

    return processed_page


def add_some_fake_semantic_documents(
    dossier,
    add_docs_with_same_title: bool = False,
    num_docs: Optional[int] = random.randint(1, 10),
    allow_empty_docs: bool = True,
    valid_document_category_keys: Optional[List[str]] = None,
    max_pages=10,
    min_num_pages=1,
    no_page_objects_per_page=0,
    log=True,
    title_suffix: str = None,
) -> List[SemanticDocument]:
    """
    Add dummy documents to this dossier
    @param valid_document_category_keys:
    @param allow_empty_docs:
    @param dossier:
    @param add_docs_with_same_title: If true then it is guaranteed that 2 documents with the same title are generated. Else this happens randomly
    @param num_docs: optional param to define how many documents are created. If not set, between 1 and 10 documents are created
    @param max_pages: maximum number of pages per document
    @param min_num_pages: minimum number of pages per document
    @return: List of documents that has been created
    """
    generic_page_cat, _ = PageCategory.objects.get_or_create(name="GENERIC_PAGE")

    document_category, document_categories = get_random_document_category(
        dossier.account, valid_document_category_keys=valid_document_category_keys
    )

    processed_page = create_processed_page_law(
        dossier=dossier,
        document_category=document_category,
        page_category=generic_page_cat,
    )

    semantic_documents = []
    for i in range(num_docs):
        confidence = random.randint(90, 100) / 100
        doc_cat = random.choice(document_categories)
        if title_suffix is None:
            title_suffix = random.choice(["asdfsuffix", None])
        semantic_document = create_fake_semantic_document(
            confidence,
            doc_cat,
            dossier,
            generic_page_cat,
            processed_page,
            title_suffix,
            allow_empty_docs,
            max_pages=max_pages,
            min_num_pages=min_num_pages,
            no_page_objects_per_page=no_page_objects_per_page,
            log=log,
        )
        semantic_documents.append(semantic_document)

    if add_docs_with_same_title:
        doc_cat_tax = document_categories[0]
        for dc in document_categories:
            if dc.name == "TAX_DECLARATION":
                doc_cat_tax = dc
                break
        assert doc_cat_tax  # can be different from tax if list of document categories is restricted

        doc_cat = doc_cat_tax
        suffix = "duplicate doc"
        d1 = create_fake_semantic_document(
            0.88,
            doc_cat,
            dossier,
            generic_page_cat,
            processed_page,
            suffix,
            allow_empty_docs,
        )
        semantic_documents.append(d1)
        d2 = create_fake_semantic_document(
            0.88,
            doc_cat,
            dossier,
            generic_page_cat,
            processed_page,
            suffix,
            allow_empty_docs,
        )
        semantic_documents.append(d2)

    return semantic_documents


def get_law_files():
    data_directory = Path(__file__).parent / "data"
    pdf_file = data_directory / "lawssimplicity.pdf"
    txt_file = data_directory / "lawsimplicity.txt"
    img_file = data_directory / "lawssimplicity.jpg"
    return img_file, pdf_file, txt_file


def create_random_pageobject_title():
    page_object_title, _ = PageObjectTitle.objects.get_or_create(
        key=fake.word() + "_" + fake.word() + "_" + fake.word(),
        defaults={
            "de": fake.word(),
            "en": fake.word(),
            "fr": fake.word(),
            "it": fake.word(),
        },
    )
    return page_object_title


def create_random_pageobject_type():
    page_object_type, _ = PageObjectType.objects.get_or_create(
        name=fake.word() + "_" + fake.word() + "_" + fake.word(),
    )
    return page_object_type


def create_dummy_page_object(
    processed_page: ProcessedPage,
) -> PageObject:
    page_object = PageObject.objects.create(
        processed_page=processed_page,
        key=create_random_pageobject_title(),
        type=create_random_pageobject_type(),
        value=fake.text(),
        confidence_value=random.uniform(0, 1),
        confidence_formatted=fake.word(),
        confidence_level=random.choice(
            [choice[0] for choice in ConfidenceLevel.choices]
        ),
        visible=True,
        ref_height=fake.random_int(min=0, max=1000),
        ref_width=fake.random_int(min=0, max=1000),
        top=fake.random_int(min=0, max=1000),
        left=fake.random_int(min=0, max=1000),
        right=fake.random_int(min=0, max=1000),
        bottom=fake.random_int(min=0, max=1000),
    )

    return page_object


def create_letter_pdf(letter_char: str) -> bytes:
    buffer = io.BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    c.setFont("Helvetica", 400)
    c.drawString(150, 250, letter_char)
    c.save()
    return buffer.getvalue()


def create_processed_page_letter(
    dossier: Dossier,
    document_category: DocumentCategory,
    page_category: PageCategory,
    letter_char: str,
):
    # Create PDF with giant letter
    pdf_data = create_letter_pdf(letter_char)

    # Convert PDF to image
    images = convert_from_bytes(pdf_data)
    img_buffer = io.BytesIO()
    images[0].save(img_buffer, format="PNG")
    img_data = img_buffer.getvalue()

    # Create text file content
    txt_data = letter_char.encode("utf-8")

    # Create DossierFile for PDF
    searchable_pdf_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=pdf_data, name=f"letter_{letter_char}.pdf"),
        bucket=dossier.bucket,
    )

    # Create database entries following same structure as original
    original_file = OriginalFile.objects.create(
        dossier=dossier,
        file=searchable_pdf_file,
        status=FileStatus.PROCESSED,
    )

    extracted_file = ExtractedFile.objects.create(
        dossier=dossier,
        original_file=original_file,
        path_from_original=f"letter_{letter_char}.pdf",
        status=FileStatus.PROCESSED,
        file=searchable_pdf_file,
    )

    processed_file = ProcessedFile.objects.create(
        dossier=dossier, extracted_file=extracted_file
    )

    # Create image and text DossierFiles
    image_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=img_data, name=f"letter_{letter_char}.png"),
        bucket=dossier.bucket,
    )

    searchable_txt_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=txt_data, name=f"letter_{letter_char}.txt"),
        bucket=dossier.bucket,
    )

    # Create ProcessedPage
    processed_page = ProcessedPage.objects.create(
        processed_file=processed_file,
        document_category=document_category,
        page_category=page_category,
        dossier=dossier,
        confidence_value=1.0,  # Since we're generating it, confidence is 100%
        number=0,
        lang=dossier.lang,
        image=image_file,
        searchable_pdf=searchable_pdf_file,
        searchable_txt=searchable_txt_file,
    )

    return processed_page


def create_fake_semantic_document(
    confidence,
    doc_cat,
    dossier,
    generic_page_cat,
    processed_page,
    title_suffix,
    allow_empty_docs,
    max_pages=10,
    min_num_pages=1,
    no_page_objects_per_page=0,
    log=True,
) -> SemanticDocument:
    if log:
        logger.info(
            f"create_fake_semantic_document({min_num_pages = }, {max_pages = }, {dossier.name = })..."
        )

    semantic_document = SemanticDocument.objects.create(
        dossier=dossier,
        confidence_value=confidence,
        confidence_level=ConfidenceLevel.HIGH,
        confidence_formatted=f"{int(confidence * 100)}%",
        document_category=doc_cat,
        title_suffix=title_suffix,
        work_status=get_start_work_status_from_dossier_account(dossier=dossier),
    )

    if allow_empty_docs:
        min_num_pages = 0

    for page_number in range(0, random.randint(min_num_pages, max_pages)):
        semantic_page = SemanticPage.objects.create(
            dossier=dossier,
            page_category=generic_page_cat,
            semantic_document=semantic_document,
            document_category=semantic_document.document_category,
            processed_page=processed_page,
            lang=dossier.lang,
            number=page_number,
            confidence_value=1,
            confidence_level=ConfidenceLevel.HIGH,
            confidence_formatted="100%",
        )

        if no_page_objects_per_page > 0:
            for _ in range(no_page_objects_per_page):
                page_object = create_dummy_page_object(
                    processed_page=processed_page,
                )
                SemanticPagePageObject.objects.create(
                    semantic_page=semantic_page, page_object=page_object
                )

    return semantic_document


def create_fake_semantic_document_custom_page_creator(
    confidence,
    doc_cat,
    dossier,
    generic_page_cat,
    processed_page_creator,  # Different parameter - function to create processed pages
    title_suffix,
    allow_empty_docs,
    max_pages=10,
    min_num_pages=1,
    no_page_objects_per_page=0,
    log=True,
) -> SemanticDocument:
    if log:
        logger.info(
            f"create_fake_semantic_document({min_num_pages = }, {max_pages = }, {dossier.name = })..."
        )

    semantic_document = SemanticDocument.objects.create(
        dossier=dossier,
        confidence_value=confidence,
        confidence_level=ConfidenceLevel.HIGH,
        confidence_formatted=f"{int(confidence * 100)}%",
        document_category=doc_cat,
        title_suffix=title_suffix,
        work_status=get_start_work_status_from_dossier_account(dossier=dossier),
    )

    if allow_empty_docs:
        min_num_pages = 0

    num_pages = random.randint(min_num_pages, max_pages)

    for page_number in range(0, num_pages):
        # Convert page number to corresponding letter (A=0, B=1, etc.)
        letter = chr(65 + (page_number % 26))  # Wrap around after Z

        # Create a new processed page with the corresponding letter
        current_processed_page = processed_page_creator(
            dossier=dossier,
            document_category=doc_cat,
            page_category=generic_page_cat,
            letter_char=letter,
        )

        semantic_page = SemanticPage.objects.create(
            dossier=dossier,
            page_category=generic_page_cat,
            semantic_document=semantic_document,
            document_category=semantic_document.document_category,
            processed_page=current_processed_page,  # Use the new processed page
            lang=dossier.lang,
            number=page_number,
            confidence_value=1,
            confidence_level=ConfidenceLevel.HIGH,
            confidence_formatted="100%",
        )

        if no_page_objects_per_page > 0:
            for _ in range(no_page_objects_per_page):
                # Create a pageobject
                page_object = PageObject.objects.create(
                    processed_page=current_processed_page,  # Use the new processed page
                    key=create_random_pageobject_title(),
                    type=create_random_pageobject_type(),
                    value=letter,
                    confidence_value=random.uniform(0, 1),
                    confidence_formatted=fake.word(),
                    confidence_level=random.choice(
                        [choice[0] for choice in ConfidenceLevel.choices]
                    ),
                    visible=True,
                    ref_height=1000,
                    ref_width=1000,
                    top=200,
                    left=200,
                    right=800,
                    bottom=800,
                )

                SemanticPagePageObject.objects.create(
                    semantic_page=semantic_page, page_object=page_object
                )

    return semantic_document


def load_initial_document_categories(
    account, document_categories_json_path: str = None
):
    """
    This loads the default categories into the account (no account-specific document categories e.g. for bekb)

    @param account:
    @return:
    """
    return load_document_categories_from_path(account, document_categories_json_path)


def add_an_original_file(dossier):
    img_file, pdf_file, txt_file = get_law_files()
    searchable_pdf_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=pdf_file.read_bytes(), name="lawsimplicity.pdf"),
        bucket=dossier.bucket,
    )
    return OriginalFile.objects.create(
        dossier=dossier,
        file=searchable_pdf_file,
        status=FileStatus.PROCESSED,
    )


def add_fake_semantic_documents_for_merge_testing(
    dossier: Dossier,
    document_category: DocumentCategory,
    num_docs,
    real_estate_property=None,
    max_pages=2,
    min_num_pages=2,
):
    # Wrapper function to create semantic documents for testing merge pages
    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=num_docs,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=max_pages,
        min_num_pages=min_num_pages,
    )

    for semantic_document in semantic_documents:
        semantic_document.document_category = document_category
        semantic_document.save()

        original_files = semantic_document.dossier.original_files.all()

        if real_estate_property:
            AssignedRealEstatePropertySemanticDocument.objects.create(
                semantic_document=semantic_document,
                realestate_property=real_estate_property,
            )

            for original_file in original_files:
                AssignedRealestatePropertyOriginalFile.objects.create(
                    originalfile=original_file,
                    realestate_property=real_estate_property,
                )

            print(
                f"add_fake_semantic_documents_for_merge_testing(): {real_estate_property.key = }"
            )
        print(
            f"add_fake_semantic_documents_for_merge_testing(): {semantic_document.title = }"
        )
    return semantic_documents, real_estate_property


def create_fake_account_with_dossiers():

    # faker = Faker()
    # language_faker = {
    #     "de": Faker("de_CH"),
    #     "fr": Faker("fr_CH"),
    #     "it": Faker("it_CH"),
    #     "en": Faker("en_US"),
    # }

    existing_default_account = Account.objects.filter(key="default").first()
    if existing_default_account:
        Dossier.objects.filter(account=existing_default_account).delete()
        existing_default_account.delete()

    statemenetment_machine_name = "Test State Management Machine"
    update_state_machine(
        Path(DATA_PATH / "bekb_state_machine.json"), statemenetment_machine_name
    )

    state_machine = StateMachine.objects.get(name=statemenetment_machine_name)
    account = AccountFactory(
        key="default",
        name="test",
        default_bucket_name="dms-default-bucket",
        enable_dossier_permission=True,
        enable_dossier_search=True,
        active_work_status_state_machine=state_machine,
        show_business_case_type=True,
    )

    load_document_categories_from_path(account, info_logging=False)

    BusinessCaseType.objects.create(
        account=account,
        key="BUSINESS_CASE_TYPE_1",
        name_de="Business Case Type 1",
        name_fr="Business Case Type 1",
        name_en="Business Case Type 1",
        name_it="Business Case Type 1",
    )
    BusinessCaseType.objects.create(
        account=account,
        key="BUSINESS_CASE_TYPE_2",
        name_de="Business Case Type 2",
        name_fr="Business Case Type 2",
        name_en="Business Case Type 2",
        name_it="Business Case Type 2",
    )

    AssigneeRoleFactory(account=account)
    FicoRoleFactory(account=account)

    JWK.objects.create(
        account=account,
        jwk={
            "e": "AQAB",
            "n": "kCmvvqDTcjFI-Xphm_cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv-ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27-CC1p_lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2-VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj-XqcZXvKAo_v1U2A9c5CztadZSa7TL8TZa_TOByxbYCaElxWKs-TWVmnj-TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITw",
            "alg": "RS256",
            "kid": "rm-e5WEVEX9eXTgRvHoYMjGAmCP5lFwgTDVC00iSYQQ",
            "kty": "RSA",
            "use": "sig",
            "x5c": [
                "MIICrTCCAZUCBgF5qD0C+DANBgkqhkiG9w0BAQsFADAaMRgwFgYDVQQDDA9kZXYtaHlwb2Rvc3NpZXIwHhcNMjEwNTI2MTAzMjIxWhcNMzEwNTI2MTAzNDAxWjAaMRgwFgYDVQQDDA9kZXYtaHlwb2Rvc3NpZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCQKa++oNNyMUj5emGb9wiILg6/3ULcFyoRE0soOdvtyrmU4uU8EwQGBbtUsq/5nJ1CTzoJyjBEzkKTyax7DQ3AGd+c22nxeMtGqWx0QN6tHNa3q8BOACWFc124byN8KgI2KoNPbv4ILWn+U9wg07HeFaemm/xdWuzisNQRJJfRFEuGCdBrOvWo92r2GiFjZ04E/g03xovP3z1gjb5WNOvdIkSlm8lfAg1y53lYmylS24ELIu3WUg4/dKP5epxle8oCj+/VTYD1zkLO1p1lJrtMvxNlr9M4HLFtgJoSXFYqz5NZWaeP5M8CgAE9xXoiNK9GDnuIW4AylqI988PkKEhPAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAI4kPHlNw9vRxXJyiwQjeRRg1IvdokqOVu/fbo70v6R+YsrpeX8PElWFkAHjN3YFX4HE5uaCefjFL+fNIHnmUYLlNVxCEDyOADAmwvu7L4MCZdRrO8ihyZUf5pUMo0WhW+8+uXQX8kyfOaC0W3xcbbJjZ2znQGWdHzWNL17YYV8ywC/+UoGfxpr0EpgIbXEa3+DIAZHvijxrwS1SGIjckCmE62myt7BM3CrfNgOGm9P5rNDYgLT/GhUWQZ1DjyC7BX5/KaPUv2aTSpHZg/PaBed/0S7RzLiAbzEKWH3uO3Vxx/lzHcYyw5Z8v+X13QLrueS76Trwpfd9Suv0JbjTZxU="
            ],
            "x5t": "ZgPdjvTvk6GxD72kltcX7IMWmRc",
            "x5t#S256": "llNBMfKaveRZKsurpX-qqpxqDYzawRbidCHGmeR3qV8",
        },
        enabled=True,
    )

    test_user1 = RandomUserFactory(username="<EMAIL>")
    DossierUserFactory(user=test_user1, account=account)

    dossier_users = []
    for i in range(1, 500):
        user = RandomUserFactory()
        dossier_users.append(DossierUserFactory(user=user, account=account))

    dossiers = []
    for i in range(1, 5):
        dossier = DossierFactory(account=account, owner=test_user1)
        semantic_document = SemanticDocumentFactory(dossier=dossier)
        for i in range(1, 5):
            SemanticPageFactory(
                dossier=dossier,
                semantic_document=semantic_document,
                page_number=i,
            )

        dossiers.append(dossier)

    # dossiers = [*testuser1_dossiers]
    #
    # dossiers = Dossier.objects.bulk_create(
    #     dossiers, ignore_conflicts=True, update_fields=[]
    # )
    #
    # statuses = Status.objects.filter(
    #     state_machine=account.active_work_status_state_machine
    # ).all()
    #
    # business_case_types = BusinessCaseType.objects.filter(account=account).all()
    # for dossier in dossiers:
    #     dossier.created_at = timezone.make_aware(
    #         faker.past_datetime(start_date=datetime(2024, 1, 1, 1, 38, 2, 594731)),
    #         timezone.get_current_timezone(),
    #     )
    #
    #     dossier.work_status = faker.random_element(statuses)
    #     dossier.expiry_date = timezone.make_aware(
    #         faker.future_datetime(end_date=datetime(2025, 1, 1, 1, 38, 2, 594731)),
    #         timezone.get_current_timezone(),
    #     )
    #
    #     dossier.businesscase_type = faker.random_element([None, *business_case_types])
    #
    #     if faker.boolean(chance_of_getting_true=10):
    #         dossier.note = language_faker[dossier.lang.lower()].text()
    #
    #     UserInvolvement.objects.create(
    #         dossier=dossier,
    #         user=faker.random_element(dossier_users),
    #         role=faker.random_element(roles),
    #     )
    #
    # Dossier.objects.bulk_update(
    #     dossiers,
    #     fields=[
    #         "created_at",
    #         "expiry_date",
    #         "work_status",
    #         "note",
    #         "businesscase_type",
    #     ],
    # )
    #
    # dossier = create_dossier(
    #     account,
    #     "test-dossier images",
    #     language="de",
    #     owner=test_user1,
    # )
    # dossier.save()
    #
    # add_semantic_document(dossier)
    #
    # # add_some_fake_semantic_documents(dossier, num_docs=5)
    # assert SemanticDocument.objects.filter(dossier=dossier).count() == 5
