"""all internal services with access to the database but without the api endpoint"""

import json
import re
import uuid
from datetime import timed<PERSON><PERSON>, datetime
from io import BytesIO
from pathlib import Path
from typing import List, Union, Dict, Optional
from uuid import UUID

import structlog
from PIL import Image
from asgiref.sync import sync_to_async
from django.conf import settings
from django.contrib.auth.models import User, AbstractUser
from django.core.files.base import ContentFile
from django.db import transaction
from django.db.models import Case, Count, F, Max, Q, QuerySet, Value, When
from django.db.models.functions import Greatest
from django.shortcuts import get_object_or_404
from django.utils import timezone
from jwcrypto.jwk import JWK
from jwcrypto.jwt import JWT
from minio.deleteobjects import DeleteObject
from structlog.contextvars import bound_contextvars

from doccheck import models as doccheck_models
from doccheck.models import DocCheck
from doccheck.services import create_case
from dossier import helpers as dossier_helpers, models
from dossier import schemas
from dossier.doc_cat_helpers import load_document_categories_from_path
from dossier.helpers import is_document_topic_property, prepare_page_object_v2
from dossier.helpers_v2 import filter_and_sort_dossiers, add_search_to_query_set
from dossier.models import (
    BusinessCaseType,
    Account,
    FileStatus,
    AccessDelegation,
    DossierUser,
    Dossier,
    UserInvolvement,
    DossierRole,
    RealestateProperty,
    minio_client,
    DossierAccessCheckProvider,
    PageCategory,
)
from dossier.processing_config import SemanticDocumentSplittingStyle
from dossier.schemas import AccountCreateSchema
from dossier.schemas import PageObjectApiDataWithUUID
from semantic_document.models import (
    SemanticDocument,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPage,
    SemanticPagePageObject,
)
from statemgmt.models import StateMachine, Status
from statemgmt.services import validate_state_transition

structlog_logger = structlog.get_logger()


def create_dossier(
    account: Account,
    dossier_name: str,
    language: str,
    owner: AbstractUser,
    businesscase_type_id: str = None,
    external_id: str = None,
    save=True,
) -> Dossier:
    if dossier_name is None:
        name = f"Dossier vom {timezone.localtime(timezone.now())}"
    else:
        name = dossier_name
    start_status = None
    # Note: this state machine refers to the statemachine associated with a Dossier
    if account.active_work_status_state_machine:
        start_status = account.active_work_status_state_machine.start_status
    doccheck_case = None
    if account.active_doc_check:
        if businesscase_type_id:
            dossier_business_case_type = BusinessCaseType.objects.get(
                account=account, uuid=businesscase_type_id
            )
            doccheck_business_case_type = doccheck_models.BusinessCaseType.objects.get(
                doc_check=account.active_doc_check, key=dossier_business_case_type.key
            )
        else:
            # Dossier might have no businesscase_type (set as null=True).
            # But Case always needs to have a business_case_type (set as null=False).
            # Use the default record in DocCheck.BusinessCaseType with key='UNKNOWN' in such cases.
            doccheck_business_case_type = doccheck_models.BusinessCaseType.objects.get(
                doc_check=account.active_doc_check, key="UNKNOWN"
            )

        doccheck_case = create_case(
            account.active_doc_check, doccheck_business_case_type
        )

    new_dossier = Dossier(
        uuid=uuid.uuid4(),
        name=name,
        lang=language,
        owner=owner,
        account=account,
        bucket=account.default_bucket_name,
        work_status=start_status,
        businesscase_type_id=businesscase_type_id,
        doccheck_case=doccheck_case,
        external_id=external_id,
        created_at=timezone.now(),
    )

    dossier_helpers.update_dossier_expired_date_by_account(new_dossier, save=save)

    return new_dossier


def change_dossier_work_status(
    dossier: Dossier, context: Dict[str, bool], new_state: Status
):
    validate_state_transition(context, dossier.work_status, new_state)
    dossier.work_status = new_state


def create_bekb_dossier_state_context():
    return dict(
        is_user=True,
        is_system=False,
        is_true=True,
        is_false=False,
        bekb_all_collaterals_mapped=True,
        is_doccheck_check_fulfilled=True,
        bekb_fico_can_archive=False,
        bekb_fico_can_not_archive=True,
        bekb_has_kbus_business_nr=True,
    )


def create_businesscase_type_export(account_key: str, dest_path: Path):
    businesscase_types = []
    for bct in (
        BusinessCaseType.objects.filter(account__key=account_key)
        .order_by("order")
        .all()
    ):
        businesscase_types.append(
            schemas.BusinessCaseTypeLoad(
                account_key=account_key,
                key=bct.key,
                name_de=bct.name_de,
                description_de=bct.description_de,
                name_en=bct.name_en,
                description_en=bct.description_en,
                name_fr=bct.name_fr,
                description_fr=bct.description_fr,
                name_it=bct.name_it,
                description_it=bct.description_it,
                order=bct.order,
            )
        )

    export = schemas.BusinessCaseTypeExport(root=businesscase_types)
    dest_path.write_text(json.dumps(export.model_dump(), indent=4))
    structlog_logger.info(f"Exported {len(businesscase_types)} business case types")


def create_businesscase_type_import(
    business_case_type_json_path: Path, account_key: str = None
) -> List[BusinessCaseType]:
    if account_key:
        structlog_logger.info(
            f"updating business case types from {business_case_type_json_path} using account {account_key}"
        )
        account = Account.objects.get(key=account_key)
    else:
        structlog_logger.info(
            f"updating business case types from {business_case_type_json_path} using account provided this file"
        )
        account = None

    export = schemas.BusinessCaseTypeExport.model_validate_json(
        business_case_type_json_path.read_text()
    )

    businesscase_types: List[BusinessCaseType] = []
    for bct in export.root:
        btc2, created = BusinessCaseType.objects.update_or_create(
            defaults=dict(
                name_de=bct.name_de,
                description_de=bct.description_de,
                name_en=bct.name_en,
                description_en=bct.description_en,
                name_fr=bct.name_fr,
                description_fr=bct.description_fr,
                name_it=bct.name_it,
                description_it=bct.description_it,
                order=bct.order,
            ),
            account=account or Account.objects.get(key=bct.account_key),
            key=bct.key,
        )
        businesscase_types.append(btc2)

        if created:
            structlog_logger.info(f"Created new business case type: {btc2}")
        else:
            structlog_logger.info(f"Updated existing business case type: {btc2}")

    return businesscase_types


def dossier_list(
    username,
    account_key,
    filter_businesscase_type=None,
    filter_dossier_role=None,
    filter_user_involvment_usernames=None,
    filter_status_type=None,
    is_a_pers_user=None,
    ordering_value="created_at",
    filter_business_partner_type=None,
    search_value=None,
    type_ordering="desc",
    is_manager=False,
):
    dossier_qs = Dossier.objects.select_related(
        "bekbdossierproperties__business_partner"
    ).filter(account__key=account_key)
    query_set = dossier_qs.annotate(
        count_documents=Count("original_files"),
        # count_documents=Count(
        #     "semantic_documents",
        #     filter=Q(semantic_documents__deleted_at__isnull=True),
        #     distinct=True,
        # ),
        status=Count(
            "original_files", filter=Q(original_files__status=FileStatus.PROCESSING)
        ),
        creation=F("created_at"),
    ).annotate(
        status=Case(
            When(status=0, then=Value(FileStatus.PROCESSED)),
            When(status__gt=0, then=Value(FileStatus.PROCESSING)),
        )
    )

    if not is_a_pers_user:
        query_set = query_set.filter(
            Q(bekbdossierproperties__isnull=True) | Q(bekbdossierproperties__pers=False)
        )
    query_set = filter_and_sort_dossiers(
        query_set,
        type_ordering,
        search_value,
        ordering_value,
        filter_businesscase_type,
        filter_dossier_role,
        filter_business_partner_type,
        filter_status_type,
        filter_user_involvment_usernames,
        is_manager,
        username,
        account_key,
    )
    return query_set


def get_list_of_assigned_dossiers(
    username: str,
    account_key: str,
    filter_owner_username_list: List[str],
    search_value: str,
    enable_dossier_permission: bool,
):
    """
    @param account_key:
    @param search_value:
    @param username: name of logged in use. Access delegation needs to be applied on this name as a delegate
    @param filter_owner_username_list: comma separated list of usernames that we want to filter for.
        So if U is a delegate of X but X is not in filter_owner_username do not show dossiers of X
    @param enable_dossier_permission: access delegation permission status
    @return: Queryset for relevant dossiers

    """
    if enable_dossier_permission:
        if len(filter_owner_username_list) == 0:
            # No filter is set then we show all dossiers that we have permission for
            assigned_permission = AccessDelegation.objects.filter(
                delegate__username=username
            ).filter(Q(expire_time__isnull=True) | Q(expire_time__gte=timezone.now()))
        else:
            assigned_permission = AccessDelegation.objects.filter(
                delegate__username=username,
                delegator__username__in=filter_owner_username_list,
            ).filter(Q(expire_time__isnull=True) | Q(expire_time__gte=timezone.now()))
        delegate_user_list = [
            delegate_user.delegator for delegate_user in assigned_permission
        ]
        query_set = Dossier.objects.filter(owner__in=delegate_user_list).filter(
            account__key=account_key
        )
        query_set = add_search_to_query_set(query_set, search_value)
    else:
        # No access delegation -> no dossiers added
        query_set = Dossier.objects.none()

    return query_set


def delegation_list(field_name: str, user: User, account: Account):
    ordering_filter_column_name = (
        "delegate" if field_name == "delegator" else "delegator"
    )

    return (
        AccessDelegation.objects.filter(**{field_name: user})
        .filter(account=account)
        .filter(Q(expire_time__isnull=True) | Q(expire_time__gte=timezone.now()))
        .order_by(
            f"{ordering_filter_column_name}__first_name",
            f"{ordering_filter_column_name}__last_name",
            f"{ordering_filter_column_name}__username",
        )
    )


def get_users_on_same_account(account: Account, username: str, search: str):
    users_on_same_account = (
        DossierUser.objects.filter(account=account)
        .filter(~Q(user__username=username))
        .filter(
            Q(user__first_name__icontains=search)
            | Q(user__last_name__icontains=search)
            | Q(
                user__email__regex=r"(?i)^.*"
                + re.escape(search)
                + r".*@.*|^([^@]+)"
                + re.escape(search)
                + r"@.*"
            )
        )
        .order_by("user__first_name", "user__last_name", "user__username")
    )

    return users_on_same_account


def check_delegation_exist(account: Account, delegator_user: User, delegate_user: User):
    count = AccessDelegation.objects.filter(
        account=account, delegator=delegator_user, delegate=delegate_user
    ).count()
    if count > 0:
        return True
    return False


def check_valid_delegation_exists(
    account: Account, delegator_user: User, delegate_user: User
) -> bool:
    count = (
        AccessDelegation.objects.filter(
            account=account, delegator=delegator_user, delegate=delegate_user
        )
        .exclude(expire_time__lt=timezone.now())
        .count()
    )

    return count > 0


def update_delegation_expire_time(
    account: Account, delegator_user: User, delegate_user: User, expire_time: timezone
):
    access_delegation = AccessDelegation.objects.get(
        account=account, delegator=delegator_user, delegate=delegate_user
    )
    access_delegation.expire_time = expire_time
    access_delegation.save()
    return access_delegation


def delegate_user_exists(delegate: User, account: Account):
    try:
        DossierUser.objects.get(user=delegate, account=account)
        return False
    except DossierUser.DoesNotExist:
        return True


def download_extraction_excel_feature(dossier_uuid: UUID):
    excel_feature = True
    try:
        dossier = Dossier.objects.filter(uuid=dossier_uuid).first()
        excel_feature = dossier.account.enable_download_extraction_excel
    except:
        pass
    return excel_feature


def get_or_create_case_to_dossier_if_active_doccheck(
    dossier: Dossier,
) -> Optional[doccheck_models.Case]:
    if dossier.doccheck_case:
        return dossier.doccheck_case

    active_doccheck = dossier.account.active_doc_check
    if active_doccheck:
        doccheck_business_case_type = get_doccheck_business_case_type(dossier)
        doccheck_case = create_case(active_doccheck, doccheck_business_case_type)
        dossier.doccheck_case = doccheck_case
        dossier.save()
        return doccheck_case


def add_case_to_dossiers_with_doccheck(account_key: str):
    assert account_key
    dossiers_with_doccheck_and_no_case = Dossier.objects.filter(
        account__key=account_key,
        account__active_doc_check__isnull=False,
        doccheck_case__isnull=True,
    ).all()
    structlog_logger.info(
        f"Found {len(dossiers_with_doccheck_and_no_case)} dossiers with an active doccheck in the account {account_key} but no doccheck case assigned..."
    )
    count = 0
    for dossier in dossiers_with_doccheck_and_no_case:
        doccheck_business_case_type = get_doccheck_business_case_type(dossier)

        doccheck_case = create_case(
            dossier.account.active_doc_check, doccheck_business_case_type
        )
        dossier.doccheck_case = doccheck_case
        dossier.save()
        count += 1
        structlog_logger.info(
            f"count={count}: added doccheck case for dossier {dossier.name}"
        )
    structlog_logger.info(f"Added {count} doccheck cases in total.")


def get_doccheck_business_case_type(dossier) -> BusinessCaseType:
    if dossier.businesscase_type_id:
        dossier_business_case_type = BusinessCaseType.objects.get(
            account=dossier.account, uuid=dossier.businesscase_type_id
        )
        doccheck_business_case_type = doccheck_models.BusinessCaseType.objects.get(
            doc_check=dossier.account.active_doc_check,
            key=dossier_business_case_type.key,
        )
    else:
        # Dossier might have no businesscase_type (set as null=True).
        # But Case always needs to have a business_case_type (set as null=False).
        # Use the default record in DocCheck.BusinessCaseType with key='UNKNOWN' in such cases.
        doccheck_business_case_type = doccheck_models.BusinessCaseType.objects.get(
            doc_check=dossier.account.active_doc_check, key="UNKNOWN"
        )
    return doccheck_business_case_type


def validate_whitelistaccess(account, token):
    rules = account.whitelist_accesses.all()
    for rule in rules:
        jwt_attribute = token.get(rule.jwt_attribute)
        rule = re.compile(rule.regex)
        assert rule.match(jwt_attribute)


def change_assignee(dossier_user, dossier):
    owner_role, _ = DossierRole.objects.get_or_create(
        defaults=dict(
            name_de="Zuständiger",
            name_fr="Responsable",
            name_en="Assignee",
            name_it="Assegnatario",
        ),
        key="ASSIGNEE",
        account=dossier.account,
    )
    involvement, _ = UserInvolvement.objects.update_or_create(
        defaults=dict(user=dossier_user), role=owner_role, dossier=dossier
    )
    return involvement


def create_expiration_date(minutes=60) -> int:
    """
    Creates an expiration date 60 minutes from the current time as unix timestamp.

    Returns:
        int: The expiration date as a unix timestamp.
    """
    return int((timezone.now() + timedelta(minutes=minutes)).timestamp())


def get_dossier_s3_objects(dossier: Dossier) -> List[str]:
    dossier_uuid = str(dossier.uuid)
    dossier_bucket = dossier.bucket

    if dossier_bucket == "" or dossier_bucket is None:
        raise Exception("dossier_bucket is not set")

    # Should not be possible as UUID is required field
    if dossier_uuid == "" or dossier_uuid is None:
        raise Exception("dossier_uuid is not set")

    associated_objects = []

    for obj in minio_client.list_objects(
        bucket_name=dossier_bucket, prefix=dossier_uuid, recursive=True
    ):
        associated_objects.append(obj.object_name)

    return associated_objects


def get_dossier_s3_objects_to_delete(dossier: Dossier) -> list[DeleteObject]:
    return [DeleteObject(x) for x in get_dossier_s3_objects(dossier)]


def delete_dossier_associated_objects(dossier: Dossier) -> list[DeleteObject]:
    # Attempt to delete objects created in S3 bucket, to handle cleanup when rolling back a copy transaction

    with bound_contextvars(
        dossier_uuid=str(dossier.uuid), dossier_bucket=dossier.bucket
    ):
        # Add timing
        start = timezone.now()

        objects_to_delete = get_dossier_s3_objects_to_delete(dossier)

        structlog_logger.info(
            "s3_list_objects_to_delete",
            duration=round((timezone.now() - start).total_seconds(), 2),
        )

        start = timezone.now()

        errors = list(
            minio_client.remove_objects(
                bucket_name=dossier.bucket,
                delete_object_list=objects_to_delete,
            )
        )

        structlog_logger.info(
            "s3_dossier_delete",
            duration=round((timezone.now() - start).total_seconds(), 2),
        )

        if len(errors) > 0:
            structlog_logger.error(
                "s3_dossier_delete_error",
                duration=round((timezone.now() - start).total_seconds(), 2),
                errors=errors,
            )

    return objects_to_delete


@transaction.atomic()
def dossier_hard_delete(dossier_uuid: UUID) -> list[DeleteObject]:
    # For structlog, bound_contextvars means any loggers inside the context
    # contain dossier_uuid
    dossier = Dossier._base_manager.get(uuid=dossier_uuid)
    with bound_contextvars(
        dossier_uuid=str(dossier_uuid), account_key=dossier.account.key
    ):
        dossier_bucket = dossier.bucket

        # Add timing
        start = timezone.now()

        # Import minio_client from dossier.models or should we move it else where?
        objects_to_delete = get_dossier_s3_objects_to_delete(dossier)

        structlog_logger.info(
            "s3_list_objects_to_delete",
            duration=round((timezone.now() - start).total_seconds(), 2),
        )

        dossier.delete()

        start = timezone.now()

        errors = list(
            minio_client.remove_objects(
                bucket_name=dossier_bucket,
                delete_object_list=objects_to_delete,
            )
        )

        structlog_logger.info(
            "s3_dossier_delete",
            duration=round((timezone.now() - start).total_seconds(), 2),
        )

        if len(errors) > 0:
            structlog_logger.error(
                "s3_dossier_delete_error",
                duration=round((timezone.now() - start).total_seconds(), 2),
                errors=errors,
            )

    return objects_to_delete


async def async_delete_expired_dossiers():
    """Async wrapper around delete expired dossiers"""

    # we will need to batch dispatch deletions in async
    # I.e. one group at a time, so we parallelize the deletion of data within each group
    # but make sure that we don't DDOS the database trying to run everything at once
    sync_to_async(delete_expired_dossiers)()


def delete_expired_dossiers():
    """Find dossiers which are expired and call function to delete their associated files

    Note: The Dossier manager is overwritten to deliberately not include expired dossiers in default filter
    Hence we use the _base_manager to get all dossiers, so we can filter for expired ones.
    """
    dossiers_to_delete = Dossier._base_manager.filter(expiry_date__isnull=False).filter(
        expiry_date__lt=timezone.now()
    )

    # Inefficient to delete one by one, but I prefer to delete each dossier within the same transaction
    # so if there is a failure, on the s3 bucket contents of a single dossier are effected (i.e. roll back)
    # to that specific one.
    for dossier in dossiers_to_delete.all():
        dossier_hard_delete(dossier.uuid)


async def async_delete_dossier(dossier_uuid: UUID):
    """Async wrapper around delete dossier"""
    async with transaction.atomic():
        await sync_to_async(dossier_hard_delete)(dossier_uuid)


def assign_real_estate_property_to_semantic_document(
    semantic_document: SemanticDocument,
):
    # Handle assignment of real estate properties from original file to semantic documents
    if semantic_document.document_category and is_document_topic_property(
        semantic_document.document_category
    ):
        with transaction.atomic():
            # There could be really several original files if the pages in the semantic document are from
            # different original files
            real_estate_property = RealestateProperty.objects.filter(
                assignedrealestatepropertyoriginalfile__originalfile__extractedfile__processed_files__processed_pages__semantic_pages__semantic_document=semantic_document
            ).first()
            if real_estate_property is not None:
                # There should only be one AssignedRealEstatePropertySemanticDocument per semantic document
                AssignedRealEstatePropertySemanticDocument.objects.filter(
                    semantic_document=semantic_document
                ).all().delete()

                AssignedRealEstatePropertySemanticDocument.objects.create(
                    semantic_document=semantic_document,
                    realestate_property=real_estate_property,
                )

                structlog_logger.info(
                    f"assigned realestate_property key {real_estate_property.key} to semantic_document uuid {semantic_document.uuid}"
                )


def get_semantic_document_last_change(
    semantic_document: SemanticDocument,
) -> Optional[datetime]:
    """
    Recommended to do a .prefetch_related("semantic_pages")
    @param semantic_document:
    @return:
    """
    max_page_updated_at = SemanticPage.all_objects.filter(
        semantic_document=semantic_document
    ).aggregate(Max("updated_at"))["updated_at__max"]
    # When we soft delete a page we set the deleted_at field on the page
    # Need to use all_objects to get the soft deleted pages
    # To work around nasty hack of overwriting default manager
    max_page_deleted_at = SemanticPage.all_objects.filter(
        semantic_document=semantic_document, deleted_at__isnull=False
    ).aggregate(Max("deleted_at"))["deleted_at__max"]
    last_change_dates: List[datetime] = [
        date
        for date in [
            max_page_updated_at,
            max_page_deleted_at,
            semantic_document.last_page_change_date,
            semantic_document.last_entity_change_date,
            semantic_document.updated_at,
        ]
        if date is not None
    ]
    last_change = max(last_change_dates) if last_change_dates else None
    return last_change


def get_dossier_last_change_query_set(
    dossiers_qs: Union[QuerySet, List[Dossier]],
) -> Union[QuerySet, List[Dossier]]:
    """
    Annotate a queryset or list of Dossier instances with the latest change time.

    This function takes into account changes (updates or deletions) of Dossier instances
    themselves as well as their associated SemanticDocument and SemanticPage instances.

    The latest change time is determined based on the 'updated_at' and 'deleted_at'
    fields of these models. For each Dossier instance, a new field 'max_updated_at'
    is added, which stores the timestamp of the most recent change among the aforementioned elements.

    Parameters:
        dossiers_qs (QuerySet of Dossier): The original Dossiers to annotate.

    Returns:
        An annotated QuerySet or list of Dossier instances, each equipped with a new
        'max_updated_at' field storing the timestamp of the latest change.
    """
    return dossiers_qs.annotate(
        semantic_doc_max_updated_at=Max("semantic_documents__updated_at"),
        semantic_doc_max_deleted_at=Max("semantic_documents__deleted_at"),
        semantic_page_max_updated_at=Max("semantic_pages__updated_at"),
        semantic_page_max_deleted_at=Max("semantic_pages__deleted_at"),
    ).annotate(
        max_updated_at=Greatest(
            "updated_at",
            "semantic_doc_max_updated_at",
            "semantic_doc_max_deleted_at",
            "semantic_page_max_updated_at",
            "semantic_page_max_deleted_at",
        )
    )


def get_dossiers_last_change(
    dossiers_qs: Union[QuerySet, List[Dossier]], time_delta: timedelta
) -> Union[QuerySet, List[Dossier]]:
    date_threshold = timezone.now() - time_delta

    return get_dossier_last_change_query_set(dossiers_qs).filter(
        max_updated_at__lte=date_threshold
    )


def map_splitting_style_pc_to_of(
    semantic_document_splitting_style: SemanticDocumentSplittingStyle,
):
    if semantic_document_splitting_style == SemanticDocumentSplittingStyle.NO_SPLITTING:
        return models.SemanticDocumentSplittingStyle.NO_SPLITTING
    return models.SemanticDocumentSplittingStyle.DEFAULT


def is_pers(dossier_user: DossierUser):
    """
    Check if a user is a person based on their associated groups.

    Args:
        dossier_user (DossierUser): The user to verify.

    Returns:
        bool: True if the user is a person, False otherwise.
    """
    return dossier_user.user.groups.filter(
        name=f"BEKB/{dossier_user.account.key}/PERS"
    ).exists()


def get_aggregate_page_objects_for_dossier(
    dossier: Dossier,
) -> List[PageObjectApiDataWithUUID]:

    dossier_page_page_objects = (
        SemanticPagePageObject.objects.filter(semantic_page__dossier=dossier)
        .select_related(
            "page_object",
            "semantic_page",
            "semantic_page__semantic_document",
            "page_object__key",
            "page_object__type",
            "page_object__processed_page",
        )
        .order_by(
            "semantic_page__semantic_document",
            "semantic_page__number",
            "page_object__top",
            "page_object__right",
        )
    )

    # Filter so that page_object.key is unique per semantic document
    filtered_sp_page_objects = []
    key_set = set()
    for spo in dossier_page_page_objects:
        # We want unique page_object.key per semantic doc, so add the two together
        key = spo.page_object.key.key + str(spo.semantic_page.semantic_document.uuid)
        if key not in key_set:
            key_set.add(key)

            filtered_sp_page_objects.append(
                PageObjectApiDataWithUUID.model_validate(
                    prepare_page_object_v2(
                        semantic_page_object=spo, use_semantic_page_no=True
                    )
                )
            )

    return filtered_sp_page_objects


def create_new_account(payload: AccountCreateSchema):
    data = payload.model_dump(exclude_unset=True, exclude_none=True)
    active_work_status_state_machine = data.pop(
        "active_work_status_state_machine", None
    )
    active_doc_check = data.pop("active_doc_check", None)
    dossier_access_check_provider = data.pop("dossier_access_check_provider", None)
    account = Account.objects.create(**data)
    if active_work_status_state_machine:
        account.active_work_status_state_machine = get_object_or_404(
            StateMachine, uuid=active_work_status_state_machine
        )
    if active_doc_check:
        account.active_doc_check = get_object_or_404(DocCheck, uuid=active_doc_check)
    if dossier_access_check_provider:
        account.dossier_access_check_provider = get_object_or_404(
            DossierAccessCheckProvider, uuid=dossier_access_check_provider
        )

    load_document_categories_from_path(account, info_logging=False)
    return account


def create_page_categories():
    page_categories_data = """
    0,GENERIC_CLASSIFIER_PAGE
    1,GENERIC_PAGE
    101,ALMOST_EMPTY_PAGE
    990,UNKNOWN_DE
    100,WHITE_PAGE
    102,QUITE_EMPTY_PAGE
    100,EMPTY_PAGE
    100,CRIMINAL_RECORDS
    100,VESTED_BENEFITS_ACCOUNT
    100,PENSION3A_ACCOUNT_STATEMENT
    110,PENSION3A_ACCOUNT_STATEMENT_ADDITIONAL_PAGE
    110,SALARY_CERTIFICATE
    3,GENERIC_FIRST_PAGE
    110,TAX_DECLARATION_PAGE_PERSONAL_DATA
    120,TAX_DECLARATION_PAGE_INCOME
    130,TAX_DECLARATION_PAGE_DEDUCTIONS
    140,TAX_DECLARATION_PAGE_ASSETS
    199,TAX_DECLARATION_MISC
    167,TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM
    150,TAX_DECLARATION_DEBT_INVENTORY
    166,TAX_DECLARATION_INSURANCE_PREMIUMS
    160,TAX_DECLARATION_ACCOUNTS_FORM_FRONT
    161,TAX_DECLARATION_ACCOUNTS_FORM_DETAILS
    100,PENSION2_CERTIFICATE_PAGE_ONE
    101,PENSION2_CERTIFICATE_PAGE_TWO
    100,DEBT_COLLECTION_FIRST_PAGE
    2,GENERIC_SINGLE_PAGE
    """
    for line in page_categories_data.strip().split("\n"):
        page_category_id, page_category_name = line.split(",")
        if not PageCategory.objects.filter(name=page_category_name).exists():
            PageCategory.objects.update_or_create(
                defaults=dict(id=page_category_id), name=page_category_name
            )


def image_to_content_file(image: Image, filename: str, format="JPEG"):
    bytes = BytesIO()
    image.save(bytes, format=format)
    content_file = ContentFile(bytes.getvalue(), name=filename)
    return content_file


def get_grant_token_for_dossier(dossier: Dossier) -> str:
    field_set = dossier.account.cdp_field_set
    key = JWK.from_json(settings.INSTANCE_SIGNING_KEY)
    jwt = JWT(
        header={"alg": "RS256"},
        claims={
            "iss": "dms",
            "aud": "cdp",
            "iat": int(timezone.now().timestamp()),
            "exp": create_expiration_date(minutes=10),
            "nbf": int(timezone.now().timestamp()),
            "dossier_uuid": str(dossier.uuid),  # always internal dossier uuid
            "field_set": field_set,
        },
    )
    jwt.make_signed_token(key)
    return jwt.serialize()
