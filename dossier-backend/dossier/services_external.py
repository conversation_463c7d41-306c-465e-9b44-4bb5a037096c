from datetime import datetime, timed<PERSON>ta
from uuid import UUID

import structlog
from django.contrib.auth import get_user_model
from django.core.files import File
from django.db import transaction
from django.db.models import QuerySet, Count, Max
from django.utils import timezone
from django.utils.text import get_valid_filename
from ninja import UploadedFile
from ninja.errors import HttpError
from http import HTTPStatus

from pydantic import BaseModel

from dossier.doc_cat_helpers import UNKNOWN_DOCUMENT_CATEGORY_KEYS
from dossier.helpers_access_check import get_dossier_with_access_check
from dossier.processing_config import OriginalFileProcessingConfig
from dossier.schemas import EntityTypes, Language
from dossier.schemas_external import (
    DossierCloseReadyResponse,
    PrepareCloseDossierResponse,
    DossierCloseResponse,
)
from dossier.tasks import process_original_file
from semantic_document import models as sem_models
from semantic_document import helpers as sem_helpers
from dossier import schemas
from typing import Callable, <PERSON><PERSON>, <PERSON>, Optional, Any, List
from dossier.helpers_v2 import get_qs_for_semantic_documents
from dossier.models import (
    Account,
    FileStatus,
    DossierUser,
    OriginalFile,
    ExtractedFile,
    Dossier,
    DossierFile,
    RealestateProperty,
    SemanticDocumentSplittingStyle,
    ProcessingStrategy,
    OriginalFileSource,
    DossierCloseStrategy,
)

from dossier.services import (
    create_dossier,
    get_semantic_document_last_change,
    map_splitting_style_pc_to_of,
    change_dossier_work_status,
)

from dossier import schemas_external

from dossier.exceptions import HttpError as DossierHttpError
from semantic_document.models import SemanticDocument
from semantic_document.helpers import compute_semantic_document_date
from semantic_document.services import (
    set_dossier_semantic_documents_state_ready_for_export,
)
from semantic_document.services_doccheck import is_doccheck_fulfilled
from statemgmt.models import Status
from dossier.statemachine_types import DossierState
from statemgmt.services import (
    calculate_next_possible_states,
    validate_state_transition,
    StateTransitionError,
)

User = get_user_model()

# services used by external APIs e.g. ZKB and Swissfex
# They are here as "from semantic_document import helpers" creates a circular import in dossier/semantic_document_pdf_export.py

logger = structlog.get_logger()


def serialize_semantic_document(
    semantic_document: sem_models.SemanticDocument,
    semantic_document_serializer: Callable,
    page_serializer: Callable,
    map_confidence: Callable,
    show_pages: bool = False,
):
    last_change = get_semantic_document_last_change(semantic_document)

    if RealestateProperty.objects.filter(
        assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
    ).exists():
        entity_type = EntityTypes.REALESTATEPROPERTY
        entity_key = (
            RealestateProperty.objects.filter(
                assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
            )
            .first()
            .key
        )
    else:
        entity_type = None
        entity_key = None

    return semantic_document_serializer(
        uuid=semantic_document.uuid,
        title=semantic_document.title,
        document_category_key=semantic_document.document_category.name,
        document_category_confidence=map_confidence(semantic_document.confidence_value),
        updated_at=semantic_document.updated_at,
        entity_type=entity_type,
        entity_key=entity_key,
        access_mode=semantic_document.access_mode,
        external_semantic_document_id=semantic_document.external_semantic_document_id,
        semantic_pages=(
            [
                page_serializer(
                    uuid=page.uuid,
                    number=page.number,
                    image_url=page.processed_page.image.fast_url,
                    updated_at=page.updated_at,
                )
                for page in semantic_document.semantic_pages.all()
            ]
            if show_pages
            else []
        ),  # Only return pages if show_pages is true
        last_change=last_change,
        work_status_key=(
            semantic_document.work_status.key if semantic_document.work_status else None
        ),
        semantic_document_date=(
            compute_semantic_document_date(semantic_document)
            if semantic_document.dossier.account.enable_custom_semantic_document_date
            else None
        ),
    )


def get_semantic_documents(
    dossier: Dossier,
    semantic_document_serializer: Callable,
    page_serializer: Callable,
    map_confidence: Callable,
    show_pages: bool = False,
) -> list:
    # Used by ZKB and Swissfex API
    semantic_documents_queryset: QuerySet = get_qs_for_semantic_documents(
        dossier=dossier,
        show_soft_deleted=False,
        hide_empty_semantic_documents=True,
    ).prefetch_related(
        "semantic_pages",
        "semantic_pages__processed_page__image",
        "document_category",
        "dossier__account",
    )

    semantic_documents_list = []

    for semantic_document in semantic_documents_queryset.all():
        serialized_semantic_document = serialize_semantic_document(
            semantic_document=semantic_document,
            semantic_document_serializer=semantic_document_serializer,
            page_serializer=page_serializer,
            map_confidence=map_confidence,
            show_pages=show_pages,
        )
        semantic_documents_list.append(serialized_semantic_document)

    return semantic_documents_list


def get_file_status(
    dossier: Dossier,
    file_status_serialiser: Callable,
    extracted_file_serialiser: Callable,
    dossier_processing_status_serialiser: Callable,
    original_file_serializer: Callable,
):
    # Used by ZKB and Swissfex API
    original_files_qs = (
        OriginalFile.objects.filter(dossier=dossier)
        .prefetch_related("file", "extractedfile_set")
        .all()
    )

    original_files = []

    original_file: OriginalFile

    processed = 0
    processing = 0
    for original_file in original_files_qs:
        extracted_files = []
        extracted_file: ExtractedFile
        for extracted_file in original_file.extractedfile_set.all():
            extracted_files.append(
                extracted_file_serialiser(
                    uuid=extracted_file.uuid,
                    path_from_original=extracted_file.path_from_original,
                    file_name=extracted_file.file.name,
                    created_at=extracted_file.created_at,
                    updated_at=extracted_file.updated_at,
                    file_url=extracted_file.file.get_fast_url(),  # use fast_url? or url?
                    status=file_status_serialiser(extracted_file.status.lower()),
                )
            )

        original_files.append(
            original_file_serializer(
                uuid=original_file.uuid,
                name=original_file.file.name,
                status=file_status_serialiser(original_file.status.lower()),
                file_url=original_file.file.get_fast_url(),  # use fast_url? or url?
                created_at=original_file.created_at,
                updated_at=original_file.updated_at,
                extracted_files=extracted_files,
            )
        )

        if original_file.status.lower() == FileStatus.PROCESSED.lower():
            processed += 1
        elif original_file.status.lower() == FileStatus.PROCESSING.lower():
            processing += 1

    progress = 0

    if processed + processing > 0:
        progress = int(processed / (processed + processing) * 100)

    return dossier_processing_status_serialiser(
        dossier_uuid=dossier.uuid,
        external_id=dossier.external_id,
        progress=progress,  # Calculate as a percentage of extracted_files having status PROCESSED?
        original_files=original_files,
    )


def add_original_file(
    dossier: Dossier,
    file: Union[File, UploadedFile],
    allow_duplicate_and_rename: bool = False,
    force_external_semantic_document_id: Optional[str] = None,
    force_access_mode: Optional[OriginalFile.OriginalFileForceAccessMode] = None,
    force_semantic_document_custom_attribute: Optional[str] = None,
    processing_config: Optional[OriginalFileProcessingConfig] = None,
    source: Optional[OriginalFileSource] = None,
    create_user=None,
) -> Tuple[int, OriginalFile]:
    file.name = get_valid_filename(file.name)

    original_files = (
        OriginalFile.objects.filter(dossier=dossier).prefetch_related("file").all()
    )

    exist_original_file_names = [
        original_file.file.name for original_file in original_files
    ]

    file_name_exists = file.name in exist_original_file_names

    if allow_duplicate_and_rename is False and file_name_exists:
        raise HttpError(
            HTTPStatus.CONFLICT,
            f"The file {file.name} already exists in the dossier {dossier.uuid}",
        )

    file.name = sem_helpers.create_copy_file_name(
        file.name,
        exist_original_file_names,
        rename=False,  # rename is a confusing parameter name, as it always changes the suffix
    )

    dossier_file = DossierFile.objects.create(
        dossier=dossier, data=file, bucket=dossier.bucket
    )
    original_file = OriginalFile.objects.create(
        dossier=dossier,
        file=dossier_file,
        force_external_semantic_document_id=force_external_semantic_document_id,
        force_access_mode=force_access_mode,
        force_semantic_document_custom_attribute=force_semantic_document_custom_attribute,
        force_document_category_key=(
            processing_config.force_document_category_key if processing_config else None
        ),
        force_title_suffix=(
            processing_config.force_title_suffix if processing_config else None
        ),
        force_semantic_document_splitting_style=(
            map_splitting_style_pc_to_of(
                processing_config.semantic_document_splitting_style
            )
            if processing_config
            else SemanticDocumentSplittingStyle.DEFAULT
        ),
        source=source,
        create_user=create_user,
    )

    if dossier.account.processing_strategy == ProcessingStrategy.DEFAULT:
        process_original_file(original_file, processing_config=processing_config)
    else:
        logger.info(
            "Skip processing for original file from service_external due to value of processing_strategy",
            dossier_uuid={dossier.uuid},
            dossier_external_id={dossier.external_id},
            processing_strategy=dossier.account.processing_strategy,
        )

    return 201, original_file


def create_or_update_dossier_from_external_id(
    account: Account,
    external_dossier_id: str,
    dossier_name: str,
    user: DossierUser,
    language: schemas.Language,
) -> Dossier:
    existing_dossier = Dossier.objects.filter(
        external_id=external_dossier_id, account=account
    ).first()

    # Update the name of the dossier if it already exists
    if existing_dossier and existing_dossier.name != dossier_name:
        existing_dossier.name = dossier_name
        existing_dossier.save()
        return existing_dossier

    new_dossier = create_dossier(
        account=account,
        dossier_name=dossier_name,
        language=language,
        owner=user.user,
        external_id=external_dossier_id,
    )
    new_dossier.save()

    return new_dossier


def update_or_create_real_estate_property(
    dossier, property: schemas.RealEstateProperty
) -> Tuple[int, RealestateProperty]:
    # Update or create a real estate property
    real_estate_property = RealestateProperty.objects.filter(
        dossier=dossier, key=property.key
    ).first()

    if real_estate_property is not None:
        # Annoyingly exclude_unset=True doesn't work as expected
        data_to_update = property.model_dump(exclude_unset=True)
        data_to_update.pop("key", None)

        for attr, value in data_to_update.items():
            if value:
                setattr(real_estate_property, attr, value)

        real_estate_property.save()

        real_estate_property.refresh_from_db()

        # Return 200 as updated as response
        return 200, real_estate_property

    created_property = RealestateProperty.objects.create(
        dossier=dossier, **property.model_dump(exclude_unset=True)
    )
    # It's honestly a little dirty to combine the 201 and the created_property as part of the service function
    # as a pure service should be independent of API signature (i.e. return code)
    # I'm keeping it here, as the second return value is a django orm object and not a schema
    return 201, created_property


def create_dossier_api(
    request, dossier_create: schemas_external.CreateDossier, language: Language = None
):
    # A wrapper for the create_dossier service function to be used by multiple external APIs
    # used to keep the DRY
    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account

    # Check if dossier already exists, and return a 409 conflict if it does
    dossier = Dossier.objects.filter(
        account=account, external_id=dossier_create.external_dossier_id
    ).first()
    if dossier is not None:
        return 409, {
            "code": 409,
            "message": f"Dossier with external_dossier_id {dossier_create.external_dossier_id} already exists",
        }

    if language is None:
        language = dossier_create.lang

    new_dossier = create_dossier(
        account=account,
        dossier_name=dossier_create.name,
        language=language,
        owner=dossier_user.user,
        external_id=dossier_create.external_dossier_id,
    )
    new_dossier.save()

    return 201, {
        "external_dossier_id": new_dossier.external_id,
        "uuid": new_dossier.uuid,
        "updated_at": new_dossier.updated_at,
        "created_at": new_dossier.created_at,
        "name": new_dossier.name,
    }


def get_dossier_with_access_check_api(
    dossier_user, external_dossier_id: str, is_manager=True
):
    """Custom wrapper for get_dossier_with_access_check to return a 404 with custom message if the dossier does not
    exist
    """
    # A little bit dirty as we are making two calls to the DB, but in terms of performance
    # should be fine
    if Dossier.objects.filter(external_id=external_dossier_id).exists() is False:
        raise DossierHttpError(
            status=404,
            detail=f"Dossier with external id {external_dossier_id} does not exist",
        )

    dossier = get_dossier_with_access_check(
        # dossier_user=request.auth.get_user_or_create(),
        dossier_user=dossier_user,
        is_manager=is_manager,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    return dossier


def update_dossier_api(
    request, external_dossier_id: str, dossier_change: schemas_external.ChangeDossier
):
    """Updates a new Dossier based on the provided parameters

    We provide a external_dossier_id as part of the URL and allow the client to change it
    as part of ChangeDossier
    """

    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    for attr, value in dossier_change.model_dump().items():
        if value:
            setattr(dossier, attr, value)

    dossier.save()
    dossier.refresh_from_db()

    return 201, {
        "external_dossier_id": dossier.external_id,
        "uuid": dossier.uuid,
        "updated_at": dossier.updated_at,
        "created_at": dossier.created_at,
        "name": dossier.name,
    }


class DossierCloseReadyStats(BaseModel):
    """
    This is an intermediate result to be interpreted by the DossierCloseStrategy
    """

    num_documents_all: int
    num_documents_export_not_started: int
    num_documents_in_export: int
    num_documents_exported: int
    num_documents_unknown: int
    num_original_files_in_processing: int
    allow_unknown_documents: bool
    original_file_processing_timestamp: Optional[datetime] = None


class DossierCloseReadyEval(BaseModel):
    # Main result of evaluation
    ready_for_close: bool

    strategy: DossierCloseStrategy

    # Some text that we return to explain the decision - for debugging only, not to be shown to user
    reason: str

    msg_nok_de: Optional[str] = None
    msg_nok_en: Optional[str] = None
    msg_nok_fr: Optional[str] = None
    msg_nok_it: Optional[str] = None


def create_dossier_close_stats(dossier: Dossier) -> DossierCloseReadyStats:

    all_documents = SemanticDocument.objects.filter(dossier=dossier)
    num_documents_all = all_documents.count()

    unknown_documents = all_documents.filter(
        document_category__name__in=UNKNOWN_DOCUMENT_CATEGORY_KEYS
    )
    num_documents_unknown = unknown_documents.count()

    # Check to see if semantic document export is done
    exported_documents = SemanticDocument.objects.filter(
        exports__semantic_document__dossier=dossier
    )
    num_documents_exported = (
        exported_documents.filter(exports__done__isnull=False).distinct().count()
    )
    num_documents_in_export = (
        exported_documents.filter(exports__done__isnull=True).distinct().count()
    )
    num_documents_export_not_started = (
        num_documents_all - num_documents_exported - num_documents_in_export
    )

    allow_unknown_documents = (
        dossier.account.enable_semantic_document_export_unknown_documents
    )

    stats = OriginalFile.objects.filter(
        dossier=dossier, status=FileStatus.PROCESSING
    ).aggregate(
        count=Count("uuid"), original_file_processing_timestamp=Max("updated_at")
    )

    num_original_files_in_processing = stats["count"]

    original_file_processing_timestamp = stats["original_file_processing_timestamp"]

    return DossierCloseReadyStats(
        num_documents_all=num_documents_all,
        num_documents_exported=num_documents_exported,
        num_documents_export_not_started=num_documents_export_not_started,
        num_documents_in_export=num_documents_in_export,
        num_documents_unknown=num_documents_unknown,
        num_original_files_in_processing=num_original_files_in_processing,
        allow_unknown_documents=allow_unknown_documents,
        original_file_processing_timestamp=original_file_processing_timestamp,
    )


def check_dossier_ready_for_closing(
    dossier: Dossier,
) -> Tuple[DossierCloseReadyStats, DossierCloseReadyEval]:
    """
    Check if a dossier can be closed

    @param dossier:
    @return: A boolean response "ready_for_close" and additional info that is used mainly for the False case
    """
    stats: DossierCloseReadyStats = create_dossier_close_stats(dossier)
    strategy = dossier.account.dossier_close_strategy

    evaluation = evaluate_dossier_close(stats, strategy)

    return stats, evaluation


def evaluate_original_files_in_processing(
    stats: DossierCloseReadyStats, strategy: DossierCloseStrategy
) -> Optional[DossierCloseReadyEval]:
    """
    Evaluate if original files are still being processed and return appropriate status.
    Returns None if no files are being processed.
    """

    if stats.num_original_files_in_processing == 0:
        return None

    processing_timeout = timedelta(hours=1)

    if stats.original_file_processing_timestamp is None:
        is_timeout = False
    else:

        is_timeout = (
            timezone.now() - stats.original_file_processing_timestamp
        ) > processing_timeout

    file_count = stats.num_original_files_in_processing

    if is_timeout:
        reason = f"Processing timeout: {file_count} original files stuck in processing"
        msg_nok_de = f"Zeitüberschreitung: {file_count} Originaldateien sind seit über 1 Stunde in Verarbeitung. Bitte Support kontaktieren."
        msg_nok_en = f"Timeout: {file_count} original files have been processing for over 1 hour. Please contact support."
        msg_nok_fr = f"Délai dépassé: {file_count} fichiers originaux sont en cours de traitement depuis plus d'une heure. Veuillez contacter le support."
        msg_nok_it = f"Timeout: {file_count} file originali sono in elaborazione da oltre 1 ora. Si prega di contattare il supporto."
    else:
        reason = f"Files still processing: {file_count} original files currently being processed"
        msg_nok_de = f"Es gibt noch {file_count} Originaldateien in Verarbeitung. Bitte warten Sie, bis die Verarbeitung abgeschlossen ist."
        msg_nok_en = f"There are still {file_count} original files being processed. Please wait until processing is complete."
        msg_nok_fr = f"Il y a encore {file_count} fichiers originaux en cours de traitement. Veuillez attendre que le traitement soit terminé."
        msg_nok_it = f"Ci sono ancora {file_count} file originali in elaborazione. Attendere il completamento dell'elaborazione."

    return DossierCloseReadyEval(
        ready_for_close=False,
        strategy=strategy,
        reason=reason,
        msg_nok_de=msg_nok_de,
        msg_nok_en=msg_nok_en,
        msg_nok_fr=msg_nok_fr,
        msg_nok_it=msg_nok_it,
    )


def evaluate_dossier_close(
    stats: DossierCloseReadyStats, strategy: DossierCloseStrategy
) -> DossierCloseReadyEval:
    """
    Apply the strategy to the stats and return either ready_for_close=True or messages indicating how to fix the
    state.
    @param stats:
    @param strategy:
    @return:
    """
    msg_nok_de = None
    msg_nok_en = None
    msg_nok_fr = None
    msg_nok_it = None
    # If we end up here we must check the additional requirements of the dossier close strategy
    match strategy:
        case DossierCloseStrategy.DEFAULT:
            reason = "DossierCloseStrategy.DEFAULT - nothing was checked"
            ready_for_close = True
            # No need to set msg_nok_xx
        case DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE:

            still_processing = evaluate_original_files_in_processing(
                stats=stats, strategy=strategy
            )
            if still_processing:
                return still_processing
            # We must have all exports finished
            semdoc_export_ok = stats.num_documents_all == stats.num_documents_exported
            ready_for_close = semdoc_export_ok
            if semdoc_export_ok:
                reason = "All semantic have been exported"
            else:
                reason = f"There are still {stats.num_documents_in_export} docs in export and {stats.num_documents_export_not_started} docs with export not started"
                if stats.num_documents_export_not_started > 0:
                    msg_nok_de = f"Es gibt noch Dokumente ({stats.num_documents_export_not_started}), die nicht aus HypoDossier übertragen wurden. Gehen Sie zu HypoDossier und benennen Sie jedes Dokument im Abschnitt 'Zuweisung / Dokumententrennung offen'. Anschliessend Dokumente übertragen."
                    msg_nok_en = f"There are still documents ({stats.num_documents_export_not_started}) that are not transferred from HypoDossier. Go to HypoDossier and rename each of the documents in section 'Assignment / Document separation open'. Afterwards transfer documents."
                    msg_nok_fr = f"Il reste encore des documents ({stats.num_documents_export_not_started}) qui n'ont pas été transférés depuis HypoDossier. Accédez à HypoDossier et renommez chaque document dans la section 'Affectation / Séparation des documents ouverte'. Ensuite, transférez les documents."
                    msg_nok_it = f"Ci sono ancora documenti ({stats.num_documents_export_not_started}) che non sono stati trasferiti da HypoDossier. Accedi a HypoDossier e rinomina ciascun documento nella sezione 'Assegnazione / Separazione documenti aperta'. Successivamente trasferisci i documenti."
                else:
                    # All exports have been triggered, but some are in progress
                    msg_nok_de = f"Es gibt noch Dokumente ({stats.num_documents_in_export}), die aktuell aus HypoDossier übertragen werden. 1-2 Minuten warten und erneut probieren."
                    msg_nok_en = f"There are still documents ({stats.num_documents_in_export}) that are currently being transferred from HypoDossier. Wait 1-2 minutes and try again."
                    msg_nok_fr = f"Il y a encore des documents ({stats.num_documents_in_export}) qui sont en cours de transfert depuis HypoDossier. Attendez 1-2 minutes et réessayez."
                    msg_nok_it = f"Ci sono ancora documenti ({stats.num_documents_in_export}) che sono attualmente in fase di trasferimento da HypoDossier. Attendere 1-2 minuti e riprovare."

        case DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE:
            still_processing = evaluate_original_files_in_processing(
                stats=stats, strategy=strategy
            )
            if still_processing:
                return still_processing
            if stats.allow_unknown_documents:
                reason = f"Unknown documents can be archived in close operation because allow_unknown_documents={stats.allow_unknown_documents}"
                ready_for_close = True
            else:
                if stats.num_documents_all == stats.num_documents_exported:
                    reason = "All documents have been archived"
                    ready_for_close = True
                else:
                    ready_for_close = stats.num_documents_unknown == 0
                    if ready_for_close:
                        reason = "All documents have been archived"
                    else:
                        reason = "Some documents are not archived and there are unknown documents which cannot be archived"
                        msg_nok_de = f"Es gibt noch Dokumente ({stats.num_documents_export_not_started}), die nicht aus HypoDossier übertragen wurden. Gehen Sie zu HypoDossier und benennen Sie jedes Dokument im Abschnitt 'Zuweisung / Dokumententrennung offen'. Anschliessend Dokumente übertragen."
                        msg_nok_en = f"There are still documents ({stats.num_documents_export_not_started}) that are not transferred from HypoDossier. Go to HypoDossier and rename each of the documents in section 'Assignment / Document separation open'. Afterwards transfer documents."
                        msg_nok_fr = f"Il reste encore des documents ({stats.num_documents_export_not_started}) qui n'ont pas été transférés depuis HypoDossier. Accédez à HypoDossier et renommez chaque document dans la section 'Affectation / Séparation des documents ouverte'. Ensuite, transférez les documents."
                        msg_nok_it = f"Ci sono ancora documenti ({stats.num_documents_export_not_started}) che non sono stati trasferiti da HypoDossier. Accedi a HypoDossier e rinomina ciascun documento nella sezione 'Assegnazione / Separazione documenti aperta'. Successivamente trasferisci i documenti."

        case _:
            raise Exception(f"Received invalid DocumentCloseStrategy: {strategy}")

    evaluation = DossierCloseReadyEval(
        ready_for_close=ready_for_close,
        strategy=strategy,
        reason=reason,
        msg_nok_de=msg_nok_de,
        msg_nok_en=msg_nok_en,
        msg_nok_fr=msg_nok_fr,
        msg_nok_it=msg_nok_it,
    )
    return evaluation


def perform_dossier_close(dossier) -> Tuple[bool, DossierCloseReadyEval]:
    stats, evaluation = check_dossier_ready_for_closing(dossier)
    success = evaluation.ready_for_close

    close_state = None
    if dossier.account.active_work_status_state_machine:
        close_state = Status.objects.get(
            key=DossierState.CLOSED.value,
            state_machine=dossier.account.active_work_status_state_machine,
        )

    context = create_dossier_state_context(
        dossier=dossier, is_system=True, is_user=False
    )
    if success:
        semdoc_export_request_uuids = (
            set_dossier_semantic_documents_state_ready_for_export(dossier)
        )

        delta = dossier.account.dossier_close_expiry_days
        dossier.expiry_date = timezone.now() + timedelta(days=delta)

        # Rely on the state machine, dossier.work_status to ensure the dossier is read-only

        if close_state:
            change_dossier_work_status(
                dossier=dossier,
                context=context,
                new_state=close_state,
            )

        # Save the changes
        dossier.save()
        logger.info(
            "close-dossier isok",
            success=success,
            dossier_id=dossier.uuid,
            access_mode=dossier.access_mode,
            delta=delta,
            expiry_date=dossier.expiry_date,
            stats=stats,
            eval=evaluation,
            num_semdocs_exported=len(semdoc_export_request_uuids),
            semdoc_export_request_uuids=semdoc_export_request_uuids,
        )
    else:
        logger.info(
            "close-dossier nok",
            success=success,
            dossier_id=dossier.uuid,
            access_mode=dossier.access_mode,
            expiry_date=dossier.expiry_date,
            stats=stats,
            eval=evaluation,
        )

    return success, evaluation


def perform_dossier_close_api_wrapper(
    dossier: Dossier,
) -> tuple[bool, DossierCloseReadyEval] | Any:

    # Check if dossier can be closed as part of context
    context = create_dossier_state_context(
        dossier=dossier, is_system=True, is_user=False
    )

    calculate_next_possible_states(context=context, current_state=dossier.work_status)

    # Get closed state
    closed_state = Status.objects.get(
        key=DossierState.CLOSED.value,
        state_machine=dossier.account.active_work_status_state_machine,
    )

    validate_state_transition(
        context=context,
        current_status=dossier.work_status,
        next_status=closed_state,
    )

    return perform_dossier_close(dossier)


def create_dossier_close_ready_response(
    stats: DossierCloseReadyStats, evaluation: DossierCloseReadyEval
) -> DossierCloseReadyResponse:

    return DossierCloseReadyResponse(
        num_documents_all=stats.num_documents_all,
        num_documents_exported=stats.num_documents_exported,
        num_documents_export_not_started=stats.num_documents_export_not_started,
        num_documents_in_export=stats.num_documents_in_export,
        num_documents_unknown=stats.num_documents_unknown,
        num_original_files_in_processing=stats.num_original_files_in_processing,
        ready_for_close=evaluation.ready_for_close,
        msg_nok_de=evaluation.msg_nok_de,
        msg_nok_en=evaluation.msg_nok_en,
        msg_nok_fr=evaluation.msg_nok_fr,
        msg_nok_it=evaluation.msg_nok_it,
    )


def create_dossier_state_context(
    *, dossier: Dossier, is_user: bool, is_system: bool
) -> dict:
    """Creates context for state transitions with relevant dossier information"""
    bekb_has_bekb_properties = False
    bekb_all_have_ekd_nr = True
    bekb_all_collaterals_mapped = True
    bekb_fico_can_archive = (
        dossier.businesscase_type is not None
        and dossier.businesscase_type.key in ["PROLONGATION", "SCANNING", "MUTATION"]
    )
    bekb_fico_can_not_archive = not bekb_fico_can_archive
    bekb_has_kbus_business_nr = True
    bekb_has_partner_partner = True
    has_at_least_one_document = dossier.semantic_documents.count() > 0
    has_businesscase_type = dossier.businesscase_type is not None

    if (
        hasattr(dossier, "bekbdossierproperties")
        and dossier.bekbdossierproperties is not None
    ):
        from bekb.services import (
            all_document_have_required_ekd_nr,
            all_collaterals_mapped,
        )

        bekb_has_bekb_properties = True
        bekb_all_have_ekd_nr = all_document_have_required_ekd_nr(
            dossier.bekbdossierproperties
        )
        bekb_has_kbus_business_nr = (
            dossier.bekbdossierproperties.business_case is not None
        )
        bekb_all_collaterals_mapped = all_collaterals_mapped(
            dossier.bekbdossierproperties
        )
        bekb_has_partner_partner = (
            dossier.bekbdossierproperties.partner_partner is not None
        )

    doccheck_enabled = dossier.account.enable_doccheck_in_statemgmt
    if doccheck_enabled:
        doccheck_fulfilled = is_doccheck_fulfilled(dossier)
    else:
        # Override doccheck and make it always fulfilled
        doccheck_fulfilled = True

    # Handle dossier close information
    # Check closing strategy, if its default i.e. DEFAULT: Basic strategy, no requirements
    # then we can always close, so skip the check via check_dossier_ready_for_closing
    # for efficiency reasons
    if dossier.account.dossier_close_strategy == DossierCloseStrategy.DEFAULT:
        dossier_close_is_possible = True
    else:
        dossier_close_is_possible = check_dossier_ready_for_closing(dossier)[
            1
        ].ready_for_close

    return dict(
        is_user=is_user,
        is_system=is_system,
        is_true=True,
        is_false=False,
        is_doccheck_fulfilled=doccheck_fulfilled,
        has_at_least_one_document=has_at_least_one_document,
        has_businesscase_type=has_businesscase_type,
        bekb_fico_can_archive=bekb_fico_can_archive,
        bekb_fico_can_not_archive=bekb_fico_can_not_archive,
        bekb_has_bekb_properties=bekb_has_bekb_properties,
        bekb_has_kbus_business_nr=bekb_has_kbus_business_nr,
        bekb_all_have_ekd_nr=bekb_all_have_ekd_nr,
        bekb_all_collaterals_mapped=bekb_all_collaterals_mapped,
        bekb_has_partner_partner=bekb_has_partner_partner,
        dossier_close_is_possible=dossier_close_is_possible,
    )


def prepare_dossier_for_closing(
    dossier, context, close_dossier_if_possible: bool = True
) -> tuple[int, dict[str, str]] | tuple[int, PrepareCloseDossierResponse]:
    """
    Service function to prepare a dossier for closing.

    Args:
        dossier: The dossier to prepare for closing
        context: The dossier state context
        close_dossier_if_possible: Whether to attempt closing if possible

    Returns:
        Tuple of (status_code, response)
    """
    # Get closing state
    closing_state = Status.objects.get(
        key=DossierState.CLOSING.value,
        state_machine=dossier.account.active_work_status_state_machine,
    )

    try:
        # Return human readable exception that dossier is not in a state that can not be closed
        validate_state_transition(
            context=context,
            current_status=dossier.work_status,
            next_status=closing_state,
        )
    except StateTransitionError as e:
        return 400, {"detail": e.message}

    with transaction.atomic():

        # Change state to closing
        change_dossier_work_status(
            dossier=dossier,
            context=context,
            new_state=closing_state,
        )
        # Still need to save the dossier as change_dossier_work_status does not save the dossier
        dossier.save()

        # Rely on the state machine, dossier.work_status to ensure the dossier is read-only

        # Trigger exports if needed
        if dossier.account.dossier_close_strategy in [
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
        ]:
            exports_triggered: List[UUID] = (
                set_dossier_semantic_documents_state_ready_for_export(dossier)
            )

    if close_dossier_if_possible and len(exports_triggered) == 0:
        try:
            stats, evaluation = check_dossier_ready_for_closing(dossier)

            if evaluation.ready_for_close:
                with transaction.atomic():
                    # If the thing can already be closed, then close it
                    try:
                        success, evaluation = perform_dossier_close_api_wrapper(dossier)

                    except StateTransitionError as e:
                        return 400, {"detail": e.message}

                close_response = DossierCloseResponse(
                    success=success,
                    msg_nok_de=evaluation.msg_nok_de,
                    msg_nok_en=evaluation.msg_nok_en,
                    msg_nok_fr=evaluation.msg_nok_fr,
                    msg_nok_it=evaluation.msg_nok_it,
                )

                return 200, PrepareCloseDossierResponse(
                    success=True,
                    exports_triggered_count=len(exports_triggered),
                    detail="Dossier moved to Closed state",
                    close_result=close_response,
                )
        except Exception as e:
            logger.info(
                "Error attempting to close dossier", dossier_uuid=dossier.uuid, error=e
            )

    return 200, PrepareCloseDossierResponse(
        success=True,
        exports_triggered_count=len(exports_triggered),
        detail="Dossier moved to Closing state",
    )
