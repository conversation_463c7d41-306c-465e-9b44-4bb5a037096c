import base64
import io
import mimetypes
from pathlib import Path

import minio
import urllib3
from django.conf import settings
from django.core.files import File
from django.core.files.storage import Storage
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.utils import timezone
from django.utils.deconstruct import deconstructible
from minio.sse import SseCustomerKey


@deconstructible
class HyS3Storage(Storage):
    def __init__(self):
        self.client = minio.Minio(
            settings.S3_ENDPOINT,
            settings.S3_ACCESS_KEY,
            settings.S3_SECRET_KEY,
            secure=settings.S3_SECURE,
            region=settings.S3_REGION,
        )

    def _save(self, file_path_name: str, content: InMemoryUploadedFile) -> str:
        bucket_name, object_name = split_bucket_objectname(file_path_name)
        object_name: Path = Path(object_name)  # app name + file.suffix
        content_bytes: io.BytesIO = io.BytesIO(content.read())
        content_length: int = len(content_bytes.getvalue())

        self.client.put_object(
            bucket_name=bucket_name,
            object_name=object_name.as_posix(),
            data=content_bytes,
            length=content_length,
            sse=(
                SseCustomerKey(base64.b64decode(settings.S3_SSE_C_B64))
                if settings.S3_SSE_C_B64
                else None
            ),
            content_type=self._guess_content_type(file_path_name, content),
        )
        return Path(file_path_name).as_posix()

    def _open(self, object_path, mode="rb", **kwargs) -> File:
        """
        Implements the Storage._open(name,mode='rb') method
        :param name (str): object_name [path to file excluding bucket name which is implied]
        :kwargs (dict): passed on to the underlying MinIO client's get_object() method
        """
        resp: urllib3.response.HTTPResponse = urllib3.response.HTTPResponse()

        bucket, object_name = split_bucket_objectname(object_path)

        if mode != "rb":
            raise ValueError(
                "Files retrieved from MinIO are read-only. Use save() method to override contents"
            )
        try:
            resp = self.client.get_object(bucket, object_name, kwargs)
            file = File(file=io.BytesIO(resp.read()), name=object_name)
        finally:
            resp.close()
            resp.release_conn()
        return file

    def url(self, name):
        bucket_name, object_name = split_bucket_objectname(name)
        response_headers = {"response-content-disposition": "attachment"}
        return self.client.presigned_get_object(
            bucket_name,
            object_name,
            response_headers=response_headers,
            request_date=timezone.now().replace(
                hour=0, minute=0, second=0, microsecond=0
            ),
        )

    def get_available_name(self, name, max_length=None):
        return name

    def exists(self, name):
        object_name = Path(name).as_posix()
        try:
            if self.stat(object_name):
                return True
            return False
        except AttributeError:
            # logger.info(e)
            return False

    @staticmethod
    def _guess_content_type(file_path_name: str, content: InMemoryUploadedFile):
        if hasattr(content, "content_type"):
            return content.content_type
        guess = mimetypes.guess_type(file_path_name)[0]
        if guess is None:
            return "application/octet-stream"  # default
        return guess

    def stat(self, name: str) -> bool:
        """Get object information and metadata of an object"""
        Path(name)
        bucket, object_name = split_bucket_objectname(name)
        try:
            obj = self.client.stat_object(bucket, object_name=object_name)
            return obj
        except (
            minio.error.S3Error,
            minio.error.ServerError,
            urllib3.exceptions.MaxRetryError,
        ):
            raise AttributeError(
                f"Could not stat object ({name}) in bucket ({self.bucket})"
            )


def split_bucket_objectname(name):
    splitted_path = name.split("/")
    bucket_name = splitted_path[0]

    object_name = "/".join(splitted_path[1:])
    return bucket_name, object_name


#
# def migrate_dossier_files_from_storages_to_hys3storage():
#     dossier_files = DossierFile.objects.all()
#     migrated =[]
#     for dossier_file in dossier_files:
#         name: str = dossier_file.data.name
#         if name.startswith(dossier_file.bucket):
#             continue
#
#         dossier_file.data.name = generate_path(dossier_file, name)
#         dossier_file.save()
#         migrated.append((name, dossier_file.data.name))
#     return migrated
