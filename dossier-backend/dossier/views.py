from django.conf import settings
from django.contrib.admin.views.decorators import staff_member_required
from django.http import HttpResponse
from django.shortcuts import render
from oauthlib.oauth2 import BackendApplicationClient
from requests_oauthlib import OAuth2Session

from dossier.models import ExtractedFile
from dossier.schemas import PageBasics, FileBasics
from processed_file.models import ProcessedPage


@staff_member_required
def extracted_files_hx(request, pk):
    context = {"extracted_files": ExtractedFile.objects.filter(dossier__uuid=pk)}
    return render(request, "dossier/extracted-files-hx.html", context)


@staff_member_required
def load_into_classifier_hx(reuqest, pk):
    extracted_file = ExtractedFile.objects.get(uuid=pk)

    page_basics = []
    processed_page: ProcessedPage
    for processed_page in extracted_file.processed_files.processed_pages.all():
        page_basics.append(
            PageBasics(
                number=processed_page.number,
                searchable_pdf_url=processed_page.searchable_pdf.fast_url,
                image_url=processed_page.image.fast_url,
            )
        )

    file_basics = FileBasics(
        pages=page_basics, source="Test", file_url=extracted_file.file.fast_url
    )

    backend_client = BackendApplicationClient(client_id=settings.KEYCLOAK_CLIENT_ID)
    client = OAuth2Session(client=backend_client)
    client.fetch_token(
        token_url=settings.KEYCLOAK_TOKEN_URL,
        client_id=settings.KEYCLOAK_CLIENT_ID,
        client_secret=settings.KEYCLOAK_CLIENT_SECRET,
    )

    res = client.post(
        url=f"{settings.CLASSIFIER_ENDPOINT}/document",
        data=file_basics.model_dump_json(),
    )
    res.raise_for_status()

    return HttpResponse("Transferred into classifier")
