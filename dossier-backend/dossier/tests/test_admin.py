import json

from django.urls import reverse
from pytest_django.asserts import assertContains

from dossier.models import <PERSON><PERSON><PERSON>, Account, <PERSON><PERSON><PERSON>


def test_dossier_admin_change_works(admin_client):
    account = Account.objects.create(name="test", key="test")
    dossier = Dossier.objects.create(account=account)

    url = reverse("admin:dossier_dossier_change", kwargs={"object_id": dossier.uuid})
    assert admin_client.get(url).status_code == 200


def test_read_write_is_in_admin(admin_client):
    url = reverse("admin:dossier_dossier_add")
    response = admin_client.get(url)
    assertContains(response, "Access mode")
    assertContains(response, "read_write")
    assertContains(response, "read_only")


def test_create_jwk(admin_client, mock_jwks_public):
    account = Account.objects.first()
    url = reverse("admin:dossier_jwk_add")
    data = {
        "account": account.uuid,
        "jwk": json.dumps(mock_jwks_public),
        "enabled": "on",
    }
    response = admin_client.post(url, data)
    assert response.status_code == 302  # Successful save with redirection
    assert JWK.objects.filter(account=account).count() == 1


# Test 2: Whether an incorrect JWK throws a validation error.
def test_invalid_jwk(admin_client, mock_jwks_public):
    account = Account.objects.first()
    url = reverse("admin:dossier_jwk_add")
    jwk_data = {**mock_jwks_public}
    # Remove required field
    jwk_data.pop("n")
    data = {
        "account": account.uuid,
        "jwk": json.dumps(jwk_data),
        "enabled": "on",
    }
    response = admin_client.post(url, data)
    assert (
        response.status_code == 200
    )  # return to the same page indicating failed validation
    assert (
        "Invalid JWK format: " in response.content.decode()
    )  # standard error message for Django's JSONField


def test_jwk_inline_in_account_admin(admin_client, mock_jwks_public):
    account = Account.objects.first()
    jwk = JWK.objects.create(account=account, description="Foo", jwk=mock_jwks_public)

    url = reverse("admin:dossier_account_change", args=[account.uuid])
    response = admin_client.get(url)

    assert response.status_code == 200
    assert (
        jwk.jwk["n"] in response.content.decode()
    )  # checks if the jwk value appears in the response
