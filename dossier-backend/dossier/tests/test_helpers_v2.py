import random
import uuid
from datetime import timed<PERSON>ta

from django.utils import timezone

from freezegun import freeze_time


import dossier.schemas as dossier_schemas

import pytest
from faker import Faker

from dossier import services
from dossier.fakes import (
    add_some_fake_semantic_documents,
    load_initial_document_categories,
    get_random_document_category,
)

from dossier.helpers_v2 import get_qs_for_semantic_documents
from dossier.models import Account, <PERSON>ssier<PERSON><PERSON>, Dossier
from projectconfig.authentication import get_user_or_create
from statemgmt.models import StateMachine
from dossier.schemas import DateRange

pytestmark = pytest.mark.django_db

# Fix random seed for reproducibility
random.seed(42)


# Some of these fixtures mirror those on swissfex tests.
# Review, refactor and consolidate them into dossier
@pytest.fixture(scope="session")
def faker():
    faker = Faker(locale="de_CH")
    faker.seed_instance(0)
    return faker


@pytest.fixture
def state_machine():
    state_machine, _ = StateMachine.objects.get_or_create(name="Dossier Status Initial")
    return state_machine


@pytest.fixture
def account(state_machine):
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="company",
            default_bucket_name="dms-default-bucket",
            active_work_status_state_machine=state_machine,
        ),
        dmf_endpoint="https://www.localhost",
        key="company",
    )

    return account


@pytest.fixture
def user(faker, account) -> DossierUser:
    return get_user_or_create(
        account=account, username=faker.email(domain="company.ch")
    )


@pytest.fixture
def dossier(account, user, faker) -> Dossier:
    dossier = services.create_dossier(
        account=account,
        dossier_name=faker.sentence(),
        language=random.choice(list(dossier_schemas.Language)),
        owner=user.user,
        external_id=str(uuid.uuid4()),
    )
    dossier.save()

    return dossier


@pytest.fixture
def document_categories(account):
    document_categories, _, _, _ = load_initial_document_categories(account)
    return document_categories


def test_get_random_document_category(dossier):
    account = Account.objects.filter(key="default").first()
    dc1, document_categories = get_random_document_category(
        account=account, valid_document_category_keys=None
    )
    assert dc1
    assert document_categories

    dc1, document_categories = get_random_document_category(
        account=account, valid_document_category_keys=["PLAN_FLOOR"]
    )
    assert dc1.name == "PLAN_FLOOR"
    assert document_categories


def count_deleted_documents_and_pages(queryset):
    count_docs = 0
    count_pages = 0
    for semantic_document in queryset.all():
        if semantic_document.deleted_at is not None:
            count_docs += 1
        for semantic_page in semantic_document.semantic_pages.all():
            if semantic_page.deleted_at is not None:
                count_pages += 1
    return count_docs, count_pages


@pytest.mark.parametrize(
    "show_all_documents_for_soft_deleted",
    [True, False],
)
def test_get_qs_for_semantic_documents_show_deleted(
    dossier, document_categories, show_all_documents_for_soft_deleted: bool
):
    # Create dummy documents, documents and pages have expiry dates set to None (i.e. not deleted)
    semantic_documents = add_some_fake_semantic_documents(dossier, num_docs=10)
    assert len(semantic_documents) == 10

    queryset = get_qs_for_semantic_documents(
        dossier,
        show_soft_deleted=True,
        hide_empty_semantic_documents=False,
        # If True, all documents are returned; if False only deleted documents are returned
        show_all_documents_for_soft_deleted=show_all_documents_for_soft_deleted,
    )
    if show_all_documents_for_soft_deleted:
        assert len(queryset) == len(semantic_documents)
    else:
        assert len(queryset) == 0

    # Step 1: Assert that none of the documents and pages is deleted
    num_docs_deleted, num_pages_deleted = count_deleted_documents_and_pages(queryset)
    assert num_docs_deleted == 0
    assert num_pages_deleted == 0

    # Step 2: delete some documents and remember how many were deleted
    changed_docs = 0
    # Set an expiry date in the past on the dossier
    for semantic_document in semantic_documents:
        if random.choice([True, False]):
            semantic_document.deleted_at = timezone.now() - timedelta(days=1)
            semantic_document.save()
            changed_docs += 1

    # Step 3: check if the number of deleted documents is correct
    # Total number of returned documents is still the same
    queryset = get_qs_for_semantic_documents(
        dossier,
        show_soft_deleted=True,
        hide_empty_semantic_documents=False,
        show_all_documents_for_soft_deleted=show_all_documents_for_soft_deleted,
    )
    if show_all_documents_for_soft_deleted:
        assert len(queryset) == len(semantic_documents)
    else:
        assert len(queryset) == changed_docs

    num_docs_deleted, num_pages_deleted = count_deleted_documents_and_pages(queryset)
    assert num_docs_deleted == changed_docs


def test_get_qs_for_semantic_documents_hide_deleted(dossier, document_categories):
    semantic_documents = add_some_fake_semantic_documents(dossier)
    queryset = get_qs_for_semantic_documents(
        dossier, show_soft_deleted=False, hide_empty_semantic_documents=False
    )

    assert len(queryset) == len(semantic_documents)

    for semantic_document in semantic_documents:
        semantic_document.deleted_at = timezone.now() - timedelta(days=1)
        semantic_document.save()


def test_get_qs_for_semantic_documents_hide_empty(dossier, document_categories):
    semantic_documents = add_some_fake_semantic_documents(
        dossier, True, 6, allow_empty_docs=False
    )
    for document in semantic_documents:
        document.semantic_pages.all().delete()

    queryset = get_qs_for_semantic_documents(
        dossier, show_soft_deleted=True, hide_empty_semantic_documents=True
    )
    results_without_empty = list(queryset)

    assert len(results_without_empty) == 0

    queryset = get_qs_for_semantic_documents(
        dossier, show_soft_deleted=True, hide_empty_semantic_documents=False
    )

    results_with_empty = list(queryset)

    assert len(results_with_empty) == len(semantic_documents)


def test_get_qs_for_semantic_documents_with_date_range(dossier, document_categories):
    # Setup Data
    semantic_documents = add_some_fake_semantic_documents(
        dossier, False, 3, allow_empty_docs=False
    )
    now = timezone.now()  # Get the current time in UTC

    # Test scenario 1: Past 30 days
    with freeze_time(now - timedelta(days=30)):
        for semantic_document in semantic_documents:
            # Trigger updating updated_at to exactly 30 days ago at midnight
            semantic_document.save()

        from_date_30_days = (
            (now - timedelta(days=30))
            .replace(hour=0, minute=0, second=0, microsecond=0)
            .strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        )
        to_date_now = now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        date_range_30_days = DateRange(from_date=from_date_30_days, to_date=to_date_now)

        queryset_scenario_1 = get_qs_for_semantic_documents(
            dossier,
            show_soft_deleted=False,
            hide_empty_semantic_documents=False,
            date_range=date_range_30_days,
        )
        assert len(queryset_scenario_1) == len(semantic_documents)

    # Test scenario 2: Past 7 days
    with freeze_time(now - timedelta(days=7)):
        for semantic_document in semantic_documents:
            # Trigger updating updated_at to exactly 7 days ago at midnight
            semantic_document.save()

        from_date_7_days = (
            (now - timedelta(days=7))
            .replace(hour=0, minute=0, second=0, microsecond=0)
            .strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        )
        to_date_7_days = now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        date_range_7_days = DateRange(
            from_date=from_date_7_days, to_date=to_date_7_days
        )
        queryset_scenario_2 = get_qs_for_semantic_documents(
            dossier,
            show_soft_deleted=False,
            hide_empty_semantic_documents=False,
            date_range=date_range_7_days,
        )
        assert len(queryset_scenario_2) == len(semantic_documents)

    # Test scenario 3: This week
    with freeze_time(now - timedelta(days=now.weekday())):
        start_of_week = now - timedelta(days=now.weekday())
        end_of_week = now  # End of the week is today
        for semantic_document in semantic_documents:
            # Trigger updating updated_at to this week
            semantic_document.save()

        from_date_this_week = start_of_week.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        to_date_this_week = end_of_week.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        date_range_this_week = DateRange(
            from_date=from_date_this_week, to_date=to_date_this_week
        )
        queryset_scenario_3 = get_qs_for_semantic_documents(
            dossier,
            show_soft_deleted=False,
            hide_empty_semantic_documents=False,
            date_range=date_range_this_week,
        )
        assert len(queryset_scenario_3) == len(semantic_documents)

    # Test scenario 4: Yesterday
    with freeze_time(now - timedelta(days=1)):
        yesterday = now - timedelta(days=1)
        for semantic_document in semantic_documents:
            # Trigger updating updated_at to yesterdays date
            semantic_document.save()

        from_date_yesterday = (
            yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        ).strftime("%Y-%m-%dT%H:%M:%S.000000Z")
        to_date_yesterday = (
            yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
        ).strftime("%Y-%m-%dT23:59:59.999999Z")

        date_range_yesterday = DateRange(
            from_date=from_date_yesterday, to_date=to_date_yesterday
        )

        queryset_scenario_4 = get_qs_for_semantic_documents(
            dossier,
            show_soft_deleted=False,
            hide_empty_semantic_documents=False,
            date_range=date_range_yesterday,
        )

        assert len(queryset_scenario_4) == len(semantic_documents)

    # Test scenario 5: Today
    with freeze_time(now.replace(hour=0, minute=0, second=0, microsecond=0)):
        for semantic_document in semantic_documents:
            # Trigger updating updated_at to today's date
            semantic_document.save()

        from_date_today = (
            timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        ).strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        to_date_today = timezone.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        date_range_today = DateRange(from_date=from_date_today, to_date=to_date_today)
        queryset_scenario_5 = get_qs_for_semantic_documents(
            dossier,
            show_soft_deleted=False,
            hide_empty_semantic_documents=False,
            date_range=date_range_today,
        )
        assert len(queryset_scenario_5) == len(semantic_documents)

        # Test scenario 6: Past hour
        with freeze_time(now - timedelta(hours=1)):
            for semantic_document in semantic_documents:
                # Trigger updating updated_at to past 1hr
                semantic_document.save()

            from_date_past_hour = (now - timedelta(hours=1)).strftime(
                "%Y-%m-%dT%H:%M:%S.%fZ"
            )
            to_date_past_hour = now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            date_range_past_hour = DateRange(
                from_date=from_date_past_hour, to_date=to_date_past_hour
            )

            queryset_scenario_6 = get_qs_for_semantic_documents(
                dossier,
                show_soft_deleted=False,
                hide_empty_semantic_documents=False,
                date_range=date_range_past_hour,
            )
            assert len(queryset_scenario_6) == len(semantic_documents)

        # Test scenario 7: Past 15 minutes
        with freeze_time(now - timedelta(minutes=15)):
            for semantic_document in semantic_documents:
                # Trigger updating updated_at to past 15m
                semantic_document.save()

            from_date_past_15_minutes = (now - timedelta(minutes=15)).strftime(
                "%Y-%m-%dT%H:%M:%S.%fZ"
            )
            to_date_past_15_minutes = now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            date_range_past_15_minutes = DateRange(
                from_date=from_date_past_15_minutes, to_date=to_date_past_15_minutes
            )

            queryset_scenario_7 = get_qs_for_semantic_documents(
                dossier,
                show_soft_deleted=False,
                hide_empty_semantic_documents=False,
                date_range=date_range_past_15_minutes,
            )
            assert len(queryset_scenario_7) == len(semantic_documents)
