import uuid

import pytest
import jwt
from jwcrypto import jwk

from dossier.models import Account, Dossie<PERSON>
from dossier.services import create_expiration_date
from swissfex.conftest import AuthenticatedClient
from dossier import schemas


@pytest.fixture(scope="session")
def private_key():
    return jwk.JWK.generate(kty="RSA", size=2048)


@pytest.fixture(scope="session")
def public_key_jwt(private_key):
    return {
        **private_key.export_public(as_dict=True),
        "kid": str(uuid.uuid4()),
        "alg": "RS256",
        "use": "sig",
    }


@pytest.fixture
def sample_account(db, public_key_jwt):
    account = Account.objects.create(
        name="test development account",
        default_bucket_name="dms-default-bucket",
        key="unittest",
        dmf_endpoint="https://www.localhost",
    )

    account.jwks.create(jwk=public_key_jwt)
    return account


@pytest.fixture
def create_client(private_key, sample_account):
    def create_client(external_dossier_id: str):
        return AuthenticatedClient(
            jwt.encode(
                payload={
                    "aud": "account",
                    "exp": create_expiration_date(minutes=60),
                    "name": "service-swissfex-first service-swissfex-last",
                    "given_name": "service-swissfex-first",
                    "family_name": "service-swissfex-last",
                    "preferred_username": "<EMAIL>",
                    "email": "<EMAIL>",
                    "account_key": sample_account.key,
                    "external_dossier_id": external_dossier_id,
                    "jti": str(uuid.uuid4()),  # unique identifier for the token
                },
                key=private_key.export_to_pem(private_key=True, password=None),
                algorithm="RS256",
            )
        )

    return create_client


def test_public_key_has_relevant_claims(public_key_jwt):
    keys = public_key_jwt.keys()
    claims = ["kty", "n", "e", "kid", "alg", "use"]
    assert all(claim in keys for claim in claims)


def test_create_dossier_uses_external_dossier_id_from_token(create_client):
    extern_dossier_id = "123"
    client = create_client(extern_dossier_id)

    result = client.post(
        "/api/dossier/",
        data=schemas.CreateDossier(name="Test Dossier", lang="fr").model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200
    object = schemas.CreatedObjectReference.model_validate_json(result.content)
    dossier = Dossier.objects.get(uuid=object.uuid)
    assert dossier.external_id == extern_dossier_id


def test_external_dossier_with_external_dossier_id_from_token(create_client):
    extern_dossier_id = "123"

    client = create_client(extern_dossier_id)

    result = client.post(
        "/api/dossier/",
        data=schemas.CreateDossier(name="Test Dossier", lang="fr").model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200

    result = client.get(f"/api/dossier/external_id/{extern_dossier_id}")
    assert result.status_code == 200
