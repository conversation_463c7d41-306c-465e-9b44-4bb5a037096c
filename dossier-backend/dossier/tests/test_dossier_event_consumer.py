from datetime import timedelta
from pathlib import Path
from django.utils import timezone
from uuid import uuid4

import pytest

from dossier.conftest import dossier_file_mock
from dossier.doc_cat_helpers import DOCUMENT_CATEGORY_UNKNOWN
from dossier.fakes import add_an_original_file
from dossier.management.commands.dossier_event_consumer_v2 import (
    add_extracted_file,
    add_processing_result,
)
from dossier.models import Dossier, Account, DossierFile, DocumentCategory
from processed_file.models import PageObjectTitle
from projectconfig.settings import ZKB_VBV_DOCUMENT_CATEGORY_KEY
from semantic_document.models import SemanticDocument
from zkb.tests.factories import ZKBAccountFactoryFaker

data_file = Path(__file__).parent / "data"

file_extracted = data_file / "FileExtractedV1.json"
processing_result = data_file / "ProcessingResult.json"


@pytest.mark.django_db
def test_dossier_deleted_at():
    uuid = uuid4()
    account = Account.objects.first()
    Dossier.objects.create(uuid=uuid, account=account)
    d1 = Dossier.objects.get(uuid=uuid)
    assert d1
    d2 = Dossier.get_by_uuid(uuid=uuid)
    assert d2
    d1.expiry_date = timezone.now() - timedelta(days=1)
    d1.save()
    d2.expiry_date = timezone.now() - timedelta(days=1)
    d2.save()

    with pytest.raises(Dossier.DoesNotExist):
        Dossier.objects.get(uuid=uuid)

    d2_2 = Dossier.get_by_uuid(uuid=uuid)
    assert d2_2


@pytest.mark.django_db
def test_create_bulk_dossier_file(monkeypatch):
    account = Account.objects.first()
    dossier = Dossier.objects.create(
        uuid="2aa86a5d-43f1-462b-a34c-c63a460a9aa5", account=account
    )

    dossier_files = []
    for _ in range(512):
        dossier_file = DossierFile(
            bucket=account.default_bucket_name, dossier_id=dossier.uuid
        )
        dossier_files.append(dossier_file)

    def urlopen_mock(self, method, url, *args, **kwargs):
        raise RuntimeError(
            f"The test was about to {method} {self.scheme}://{self.host}{url}"
        )

    monkeypatch.setattr(
        "urllib3.connectionpool.HTTPConnectionPool.urlopen", urlopen_mock
    )

    DossierFile.objects.bulk_create(dossier_files)

    # original_dossier_file = DossierFile.objects.create(bucket=account.default_bucket_name, dossier_id=dossier.uuid)


@pytest.mark.django_db
def test_page_object_title():
    key = "yadfasdf"
    grumpf, created = PageObjectTitle.objects.get_or_create(key=key)
    assert grumpf
    assert grumpf.key == key


@pytest.mark.django_db
@pytest.mark.parametrize(
    "settings_vbv,change_file_to_vbv,doc_cat_vbv_exists,allow_create_doc_cat",
    [
        # Here doc cat VBV already exists because loaded with ZKB factory
        [True, True, True, True],
        [True, False, True, True],
        [False, True, True, True],
        [False, False, True, True],
        # Same but the doc cat does not exist and will be created in event consumer
        [True, True, False, True],
        [True, False, False, True],
        [False, True, False, True],
        [False, False, False, True],
        # Same but the doc cat does not exist and will NOT be created -> doc cat is changed to UNKNOWN.
        # This is the default behaviour
        [True, True, False, False],
        [True, False, False, False],
        [False, True, False, False],
        [False, False, False, False],
    ],
)
def test_special_handling_for_zkb_vbv(
    monkeypatch,
    settings_vbv: bool,
    change_file_to_vbv: bool,
    doc_cat_vbv_exists: bool,
    allow_create_doc_cat: bool,
):
    """

    @param monkeypatch:
    @param settings_vbv: If True then apply special handling for vbv in add_processing_result (set to read-only).
    Else treat normally.
    @param change_file_to_vbv: If True then modify input file to be vbv
    @param doc_cat_vbv_exists: If True then the account has the VBV category. Else it is deleted in the
    beginning of the test
    @param allow_create_doc_cat: If True then event consumer is allowed to create new document categories.
    Else it will be mapped to the default UNKNOWN category
    @return:
    """
    monkeypatch.setattr(
        "dossier.management.commands.dossier_event_consumer_v2.create_dossier_file_from_url",
        dossier_file_mock,
    )

    zkb_account_factory = ZKBAccountFactoryFaker()
    zkb_account_factory.load_initial_document_categories()
    dossier = zkb_account_factory.create_dossier()
    assert dossier

    vbv_cat = DocumentCategory.objects.get(
        account=dossier.account, name=ZKB_VBV_DOCUMENT_CATEGORY_KEY
    )
    assert vbv_cat
    if not doc_cat_vbv_exists:
        vbv_cat.delete()
        assert (
            DocumentCategory.objects.filter(
                account=dossier.account, name=ZKB_VBV_DOCUMENT_CATEGORY_KEY
            ).count()
            == 0
        )

    original_file = add_an_original_file(dossier)

    original_file.force_external_semantic_document_id = "vbv_ext_id"
    original_file.save()

    assert original_file

    # Create an extracted file
    extracted_str = (
        Path(__file__).parent / "data" / "zkb" / "zkb_vbv" / "FileExtractedVBV01.json"
    ).read_text()
    extracted_str = extracted_str.replace("DUMMY_DOSSIER_UUID", str(dossier.uuid))
    extracted_str = extracted_str.replace(
        "DUMMY_ORIGINAL_FILE_UUID", str(original_file.uuid)
    )

    add_extracted_file(extracted_str)

    # Create a processed file of type VBV.
    processed_str = (
        Path(__file__).parent
        / "data"
        / "zkb"
        / "zkb_vbv"
        / "ProcessingResultVBV01.json"
    ).read_text()
    processed_str = processed_str.replace("DUMMY_DOSSIER_UUID", str(dossier.uuid))
    if change_file_to_vbv:
        processed_str = processed_str.replace("TAX_DECLARATION", vbv_cat.name)

    add_processing_result(
        processed_str,
        zkb_vbv_use_extracted_file=settings_vbv,
        allow_create_doc_cat=allow_create_doc_cat,
    )

    # Check that we have exactly one semantic document and it is set to read only
    sem_docs = list(SemanticDocument.objects.filter(dossier=dossier).all())
    assert len(sem_docs) == 1
    sem_doc: SemanticDocument = sem_docs[0]

    assert sem_doc.external_semantic_document_id == "vbv_ext_id"

    if change_file_to_vbv:
        if doc_cat_vbv_exists or allow_create_doc_cat:
            assert sem_doc.document_category.name == vbv_cat.name
        else:
            # VBV doc cat does not exist and it is not created. Doc is mapped to unknown
            assert sem_doc.document_category.name == DOCUMENT_CATEGORY_UNKNOWN.name
    else:
        assert sem_doc.document_category.name == "TAX_DECLARATION"

    if settings_vbv and change_file_to_vbv:
        if not doc_cat_vbv_exists and not allow_create_doc_cat:
            assert sem_doc.document_category.name == DOCUMENT_CATEGORY_UNKNOWN.name
            assert (
                sem_doc.access_mode
                == SemanticDocument.SemanticDocumentAccessMode.READ_WRITE
            )
        else:
            assert sem_doc.document_category.name != DOCUMENT_CATEGORY_UNKNOWN.name
            assert (
                sem_doc.access_mode
                == SemanticDocument.SemanticDocumentAccessMode.READ_ONLY
            )
    else:
        assert (
            sem_doc.access_mode
            == SemanticDocument.SemanticDocumentAccessMode.READ_WRITE
        )
    assert sem_doc.semantic_pages.count() == 13
