import uuid
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import List
from django.utils import timezone

import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db.models import Exists, OuterRef
from freezegun import freeze_time
from ninja.errors import HttpError
from pydantic import Field, BaseModel

from pytest_mock import MockerFixture

from core.generics import AnyHttpUrlStr


from dossier import schemas
from dossier.conftest import (
    models_for_comparison_with_dossier_FK,
    set_intermediate_models,
    create_synthetic_dossier,
)
from dossier.fakes import (
    add_some_fake_semantic_documents,
    add_an_original_file,
    create_fake_semantic_document,
)
from dossier.helpers import get_all_s3_objects_in_bucket
from dossier.helpers_model_copier import (
    copy_dossier_models,
    update_dossier_to_use_different_bucket,
)
from dossier.models import (
    DossierFile,
    OriginalFile,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
    DocumentCategory,
    minio_client,
    PageCategory,
    DossierExport,
    Account,
)
from dossier.services import (
    dossier_hard_delete,
    get_dossier_last_change_query_set,
    get_dossiers_last_change,
    get_dossier_s3_objects,
    get_aggregate_page_objects_for_dossier,
)

from dossier.services import (
    assign_real_estate_property_to_semantic_document,
)
from dossier.services_external import (
    get_semantic_documents,
    get_file_status,
    add_original_file,
    create_or_update_dossier_from_external_id,
)
from projectconfig.authentication import get_user_or_create
from semantic_document.models import (
    SemanticDocument,
    SemanticPagePageObject,
)

from dossier.models import (
    Dossier,
)

User: AbstractUser = get_user_model()

pytestmark = pytest.mark.django_db


def map_confidence(confidence_value):
    if confidence_value > 0.95:
        return schemas.ConfidenceLevel.HIGH
    if confidence_value > 0.8:
        return schemas.ConfidenceLevel.MEDIUM
    return schemas.ConfidenceLevel.LOW


class SemanticPageSchema(BaseModel):
    uuid: uuid.UUID
    number: int = Field(
        description="Page number is zero based. First page has page number 0"
    )
    image_url: str

    updated_at: datetime


class SemanticDocumentSchema(BaseModel):
    uuid: uuid.UUID
    title: str

    document_category_key: str
    document_category_confidence: schemas.ConfidenceLevel

    semantic_pages: List[SemanticPageSchema]

    updated_at: datetime


def test_get_semantic_documents(service_account, load_document_categories):
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=service_account
    )

    add_some_fake_semantic_documents(dossier=dossier, allow_empty_docs=False)

    semantic_documents = get_semantic_documents(
        dossier=dossier,
        semantic_document_serializer=SemanticDocumentSchema,
        page_serializer=SemanticPageSchema,
        map_confidence=map_confidence,
    )

    assert len(semantic_documents) == dossier.semantic_documents.count()


class FileStatusEnum(str, Enum):
    PROCESSING = "processing"
    ERROR = "error"
    PROCESSED = "processed"


class ExtractedFileSchema(BaseModel):
    uuid: uuid.UUID
    path_from_original: str
    file_name: str
    status: FileStatusEnum
    file_url: AnyHttpUrlStr
    created_at: datetime
    updated_at: datetime


class OriginalFileSchema(BaseModel):
    uuid: uuid.UUID
    name: str
    status: FileStatusEnum
    extracted_files: List[ExtractedFileSchema]
    file_url: AnyHttpUrlStr
    created_at: datetime
    updated_at: datetime


class DossierProcessingStatusSchema(BaseModel):
    dossier_uuid: uuid.UUID
    external_id: str
    progress: int
    original_files: List[OriginalFileSchema]


def test_get_file_status(service_account, load_document_categories):
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=service_account
    )

    add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False, num_docs=5
    )

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    searchable_pdf_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(
            content=file_path.read_bytes(), name="240_betreibungsauskunft-mt-at.pdf"
        ),
        bucket=dossier.bucket,
    )

    original_file = OriginalFile.objects.create(
        dossier=dossier,
        file=searchable_pdf_file,
        status=FileStatusEnum.PROCESSING,
    )

    status = get_file_status(
        dossier=dossier,
        file_status_serialiser=FileStatusEnum,
        extracted_file_serialiser=ExtractedFileSchema,
        dossier_processing_status_serialiser=DossierProcessingStatusSchema,
        original_file_serializer=OriginalFileSchema,
    )

    for original_file in status.original_files:
        original_file_object = OriginalFile.objects.get(uuid=original_file.uuid)
        extracted_files = original_file_object.extractedfile_set.all()

        assert len(original_file.extracted_files) == len(extracted_files)

        for extracted_file in extracted_files:
            assert extracted_file.uuid in [
                extracted_file.uuid for extracted_file in original_file.extracted_files
            ]

    # Check that 50% of original files are processed
    assert status.progress == 50


def test_add_original_file(
    service_account, load_document_categories, mocker: MockerFixture
):
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=service_account
    )

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    status_code, object_reference = add_original_file(dossier=dossier, file=file)

    assert status_code == 201
    assert object_reference == dossier.original_files.first()

    original_file = OriginalFile.objects.get(uuid=object_reference.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    # Test we can't add duplicate files by default
    with pytest.raises(HttpError):
        add_original_file(dossier=dossier, file=file)

    # Test we can add a duplicate file if we set allow_duplicate to True
    status_code, new_object_reference = add_original_file(
        dossier=dossier,
        file=file,
        allow_duplicate_and_rename=True,
    )

    # Check that file model objects are different
    assert original_file.uuid != new_object_reference.uuid

    # Check that new duplicate file has new name
    assert original_file.file.name != new_object_reference.file.name

    # Check that they belong to the same dossier
    assert original_file.dossier == new_object_reference.dossier


def test_create_or_update_dossier_from_external_id(
    service_account, load_document_categories
):
    current_user = get_user_or_create(
        account=service_account,
        username="<EMAIL>",
        email="<EMAIL>",
        fname="service-user-first",
        lname="service-user-last",
    )

    external_dossier_id = str(uuid.uuid4())

    created_dossier = create_or_update_dossier_from_external_id(
        account=service_account,
        external_dossier_id=external_dossier_id,
        dossier_name="Test Dossier",
        language=schemas.Language.de,
        user=current_user,
    )

    dossier = Dossier.objects.get(
        external_id=external_dossier_id, account=service_account
    )
    assert created_dossier == dossier

    # Test Updating dossier name if dossier already exists
    created_dossier = create_or_update_dossier_from_external_id(
        account=service_account,
        external_dossier_id=external_dossier_id,
        dossier_name="Test Dossier2",
        language=schemas.Language.de,
        user=current_user,
    )

    dossier.refresh_from_db()

    assert created_dossier == dossier

    assert dossier.name == "Test Dossier2"


@pytest.mark.parametrize(
    ("document_category_key", "assigned"),
    [
        ("SALES_DOCUMENTATION", True),  # 610
        ("ASSET_INCOME", False),
        ("UNKNOWN", False),
        ("PASSPORT_CH", False),
        ("TAX_DECLARATION", False),  # 310
        # Do not handle case where it is missing - should not happen
    ],
)
def test_assign_real_estate_property_to_semantic_document(
    service_account, load_document_categories, document_category_key, assigned
):
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=service_account
    )

    # Create the real RealestateProperty
    real_estate_property, _ = RealestateProperty.objects.get_or_create(
        dossier=dossier, key="Mansion"
    )

    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False
    )

    original_file = dossier.original_files.first()

    AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=original_file, realestate_property=real_estate_property
    )

    semantic_document: SemanticDocument = semantic_documents[0]

    # Override document category with parameter value set by test
    doc_cat = DocumentCategory.objects.filter(
        account=dossier.account, name=document_category_key
    ).get()
    semantic_document.document_category = doc_cat
    semantic_document.save()

    # just create another original file, so it could get mixed up with the one we want to test
    snd_original_file = add_an_original_file(dossier)
    AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=snd_original_file,
        realestate_property=RealestateProperty.objects.create(
            dossier=dossier, key="Hut"
        ),
    )
    assert dossier.original_files.count() == 2

    assert semantic_document.assignedrealestatepropertysemanticdocument_set.count() == 0

    # ass1 = list(AssignedRealestatePropertyOriginalFile.objects.all())

    assign_real_estate_property_to_semantic_document(semantic_document)

    # ass2 = list(AssignedRealestatePropertyOriginalFile.objects.all())

    if assigned:
        assert (
            RealestateProperty.objects.filter(
                assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
            ).count()
            == 1
        )

        assert (
            RealestateProperty.objects.filter(
                assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
            ).first()
            == real_estate_property
        )

    else:
        assert (
            RealestateProperty.objects.filter(
                assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
            ).count()
            == 0
        )


def test_delete_dossier_synthetic_success(
    synthetic_dossier, load_document_categories, temp_minio_bucket
):
    dossier = synthetic_dossier

    update_dossier_to_use_different_bucket(
        dossier=dossier, temp_minio_bucket=temp_minio_bucket
    )

    add_some_fake_semantic_documents(dossier=dossier, allow_empty_docs=False)

    set_intermediate_models(dossier)

    initial_dossier_objects = set(get_dossier_s3_objects(dossier=dossier))
    initial_all_s3_objects = set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )

    assert len(initial_dossier_objects) > 0

    # Technically not needed as intersection is covered by issubset
    # however, this way we explicitly test that intersection result changes to false at the end of the test
    assert len(initial_all_s3_objects.intersection(initial_dossier_objects)) > 0
    assert initial_dossier_objects.issubset(initial_all_s3_objects)

    # Run deletion
    dossier_hard_delete(dossier.uuid)

    for model, query_key in models_for_comparison_with_dossier_FK:
        assert model.objects.filter(**{query_key: dossier.uuid}).count() == 0

    # Test that we have deleted the associated s3 objects
    assert len(get_dossier_s3_objects(dossier=dossier)) == 0

    updated_all_s3_objects = set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )

    # Test that we have ONLY deleted
    assert len(initial_all_s3_objects) - len(initial_dossier_objects) == len(
        updated_all_s3_objects
    )

    assert len(updated_all_s3_objects.intersection(initial_dossier_objects)) == 0


def test_delete_using_sample_dossier(db, temp_minio_bucket):
    old_dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    update_dossier_to_use_different_bucket(
        dossier=old_dossier, temp_minio_bucket=temp_minio_bucket
    )

    old_dossier.external_id = "test external id"
    old_dossier.save()

    set_intermediate_models(old_dossier)

    old_dossier_objects = set(get_dossier_s3_objects(dossier=old_dossier))
    initial_all_s3_objects = set(
        get_all_s3_objects_in_bucket(dossier_bucket=old_dossier.bucket)
    )

    assert len(old_dossier_objects) > 0

    new_instances = list(
        copy_dossier_models(dossier=old_dossier, external_id="new_external_id").values()
    )

    dossier_file_count = old_dossier.dossierfile_set.count()

    new_dossier: Dossier = new_instances[0]

    new_dossier_objects = set(get_dossier_s3_objects(dossier=new_dossier))

    # Assert we have copied s3 files
    assert len(old_dossier_objects) >= len(new_dossier_objects)
    # Technically we have only copied dossier files without export status

    copied_dossier_files = DossierFile.objects.filter(dossier=new_dossier).exclude(
        Exists(DossierExport.objects.filter(file=OuterRef("pk")))
    )

    assert len(new_dossier_objects) == copied_dossier_files.count()

    all_s3_objects_with_copy = set(
        get_all_s3_objects_in_bucket(dossier_bucket=old_dossier.bucket)
    )

    # Assert that the new total file count has increased by new dossier objects
    assert len(all_s3_objects_with_copy) == len(initial_all_s3_objects) + len(
        new_dossier_objects
    )

    assert new_dossier_objects.issubset(all_s3_objects_with_copy)

    new_dossier_uuid = str(new_dossier.uuid)
    new_dossier_bucket = new_dossier.bucket

    model_counts = {}

    for model, query_key in models_for_comparison_with_dossier_FK:
        old_count = model.objects.filter(**{query_key: old_dossier.uuid}).count()
        new_count = model.objects.filter(**{query_key: new_dossier.uuid}).count()
        assert old_count == new_count
        # Check that we actually have entries
        assert old_count > 0
        model_counts[model.__name__] = new_count

        # Run deletion
    s3_objects_to_delete = dossier_hard_delete(new_dossier.uuid)

    assert len(s3_objects_to_delete) == dossier_file_count

    # Check files no longer exit on S3 bucket

    s3_objects = minio_client.list_objects(
        bucket_name=new_dossier_bucket, prefix=new_dossier_uuid, recursive=True
    )

    assert list(s3_objects) == []

    # Assert that old S3 data has not changed
    assert initial_all_s3_objects == set(
        get_all_s3_objects_in_bucket(dossier_bucket=old_dossier.bucket)
    )

    assert old_dossier_objects == set(get_dossier_s3_objects(dossier=old_dossier))


def test_delete_using_sample_dossier_integrity(db, temp_minio_bucket):
    # Same as test_delete_using_sample_dossier, but we create an additional copy of the dossier
    # and do additional checks to make sure that we have not accidentally deleted other models
    # not threadsafe

    old_dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    update_dossier_to_use_different_bucket(
        dossier=old_dossier, temp_minio_bucket=temp_minio_bucket
    )

    old_dossier.external_id = "test external id"
    old_dossier.save()

    set_intermediate_models(old_dossier)

    copy_dossier_models(dossier=old_dossier, external_id="new_external_id_to_keep")

    # Do a full count of all associated models
    initial_total_model_counts = {}

    for model, query_key in models_for_comparison_with_dossier_FK:
        count = model.objects.all().count()

        assert count > 0

        initial_total_model_counts[model.__name__] = count

    # Get all s3 objects
    all_s3_objects = set(
        get_all_s3_objects_in_bucket(dossier_bucket=old_dossier.bucket)
    )

    new_instances_to_delete = list(
        copy_dossier_models(
            dossier=old_dossier, external_id="new_external_id_to_delete"
        ).values()
    )

    new_dossier_to_delete: Dossier = new_instances_to_delete[0]

    dossier_file_count = new_dossier_to_delete.dossierfile_set.count()

    new_dossier_to_delete_uuid = str(new_dossier_to_delete.uuid)
    new_dossier_to_delete_bucket = new_dossier_to_delete.bucket

    model_counts = {}

    for model, query_key in models_for_comparison_with_dossier_FK:
        old_count = model.objects.filter(**{query_key: old_dossier.uuid}).count()
        new_count = model.objects.filter(
            **{query_key: new_dossier_to_delete.uuid}
        ).count()
        assert old_count == new_count
        # Check that we actually have entries
        assert old_count > 0
        model_counts[model.__name__] = new_count

        assert model.objects.all().count() > initial_total_model_counts[model.__name__]

    assert len(
        list(minio_client.list_objects(bucket_name=old_dossier.bucket, recursive=True))
    ) == len(
        list(
            minio_client.list_objects(
                bucket_name=new_dossier_to_delete_bucket,
                prefix=new_dossier_to_delete_uuid,
                recursive=True,
            )
        )
    ) + len(
        all_s3_objects
    )

    # Run deletion
    s3_objects_to_delete = dossier_hard_delete(new_dossier_to_delete.uuid)

    assert len(s3_objects_to_delete) == dossier_file_count

    # Check total model counts are the same
    for model, query_key in models_for_comparison_with_dossier_FK:
        assert model.objects.all().count() == initial_total_model_counts[model.__name__]

    old_dossier.refresh_from_db()
    assert old_dossier.original_files.count() > 0

    # NOTE: This may fail due to race condition i.e. Delete minio is non blocking and
    # files may still exist
    # Check files no longer exit on S3 bucket
    s3_objects = list(
        minio_client.list_objects(
            bucket_name=new_dossier_to_delete_bucket,
            prefix=new_dossier_to_delete_uuid,
            recursive=True,
        )
    )

    assert len(s3_objects) == 0

    # Check that s3 file count is the same
    assert len(all_s3_objects) == len(
        list(
            minio_client.list_objects(
                bucket_name=old_dossier.bucket,
                recursive=True,
            )
        )
    )


def test_get_dossier_last_change_empty_dossier(load_document_categories):
    # Test handling the case where we have no semantic documents or pages
    # make sure nothing weird happens
    now = timezone.now()
    frozen_time = now - timedelta(days=30)

    with freeze_time(frozen_time):
        dossier = create_synthetic_dossier()

    dossiers = get_dossier_last_change_query_set(
        dossiers_qs=Dossier._base_manager.filter(uuid=dossier.uuid)
    )

    assert len(dossiers) == 1

    annotated_dossier = dossiers[0]
    assert annotated_dossier.created_at == frozen_time
    assert annotated_dossier.updated_at == frozen_time
    assert annotated_dossier.max_updated_at == frozen_time


def test_get_dossier_last_change_query_set_fake_semantic_docs(
    load_document_categories,
):
    now = timezone.now()

    frozen_time_past = now - timedelta(days=60)

    # Create dossier "in the past" and check that max_updated_at is the same timestamp
    with freeze_time(frozen_time_past):
        dossier = create_synthetic_dossier()

    assert (
        get_dossier_last_change_query_set(
            dossiers_qs=Dossier._base_manager.filter(uuid=dossier.uuid)
        )[0].max_updated_at
        == frozen_time_past
    )

    # Add documents "now" and check that max_updated_at is updated to now
    with freeze_time(now):
        # can't shift too far back otherwise
        # minio.error.S3Error: S3 operation failed; code: RequestTimeTooSkewed
        add_some_fake_semantic_documents(
            dossier=dossier, allow_empty_docs=False, num_docs=2
        )

    dossiers = get_dossier_last_change_query_set(
        dossiers_qs=Dossier._base_manager.filter(uuid=dossier.uuid)
    )
    assert len(dossiers) == 1

    annotated_dossier = dossiers[0]
    assert annotated_dossier.created_at == frozen_time_past
    assert annotated_dossier.updated_at == frozen_time_past
    assert (
        annotated_dossier.max_updated_at == now
    )  # because semantic documents have been added


def test_get_dossier_last_change_query_set(
    load_document_categories,
):
    now = timezone.now()

    frozen_time_past_60 = now - timedelta(days=60)
    frozen_time_past_30 = now - timedelta(days=30)

    with freeze_time(frozen_time_past_60):
        dossier = create_synthetic_dossier()

    # Create dossier "in the past 1" and check that max_updated_at is the same timestamp
    assert (
        get_dossier_last_change_query_set(
            dossiers_qs=Dossier._base_manager.filter(uuid=dossier.uuid)
        )[0].max_updated_at
        == frozen_time_past_60
    )

    # Add documents "in the past 2" and check that max_updated_at is updated to "past 2"
    with freeze_time(frozen_time_past_30):
        create_fake_semantic_document(
            confidence=0.88,
            doc_cat=DocumentCategory.objects.filter(account=dossier.account).first(),
            dossier=dossier,
            generic_page_cat=PageCategory.objects.get(id=1),
            processed_page=None,
            title_suffix="asdfsuffix",
            allow_empty_docs=False,
        )

    assert (
        get_dossier_last_change_query_set(
            dossiers_qs=Dossier._base_manager.filter(uuid=dossier.uuid)
        )[0].max_updated_at
        == frozen_time_past_30
    )


def test_get_dossier_last_change(
    load_document_categories,
):
    now = timezone.now()

    frozen_time_60 = now - timedelta(days=60)
    frozen_time_30 = now - timedelta(days=30)
    frozen_time_15 = now - timedelta(days=15)

    with freeze_time(frozen_time_60):
        dossier_1 = create_synthetic_dossier(external_id="test external id1")
        dossier_2 = create_synthetic_dossier(external_id="test external id2")

    assert (
        get_dossier_last_change_query_set(
            # Note: if we use _base_manager we get all objects including expired ones
            dossiers_qs=Dossier._base_manager.filter(
                uuid__in=[dossier_1.uuid, dossier_2.uuid]
            )
        )[0].max_updated_at
        == frozen_time_60
    )

    with freeze_time(frozen_time_30):
        create_fake_semantic_document(
            confidence=0.88,
            doc_cat=DocumentCategory.objects.filter(account=dossier_1.account).first(),
            dossier=dossier_1,
            generic_page_cat=PageCategory.objects.get(id=1),
            processed_page=None,
            title_suffix="asdfsuffix",
            allow_empty_docs=False,
        )

    with freeze_time(frozen_time_15):
        create_fake_semantic_document(
            confidence=0.88,
            doc_cat=DocumentCategory.objects.filter(account=dossier_2.account).first(),
            dossier=dossier_2,
            generic_page_cat=PageCategory.objects.get(id=1),
            processed_page=None,
            title_suffix="asdfsuffix",
            allow_empty_docs=False,
        )

    dossiers = get_dossiers_last_change(
        dossiers_qs=Dossier._base_manager.filter(
            uuid__in=[dossier_1.uuid, dossier_2.uuid]
        ),
        time_delta=timedelta(days=5),
    )

    assert len(dossiers) == 2

    dossiers = get_dossiers_last_change(
        dossiers_qs=Dossier._base_manager.filter(
            uuid__in=[dossier_1.uuid, dossier_2.uuid]
        ),
        time_delta=timedelta(days=25),
    )

    assert len(dossiers) == 1

    assert dossiers[0] == dossier_1

    assert (
        get_dossiers_last_change(
            dossiers_qs=Dossier._base_manager.filter(
                uuid__in=[dossier_1.uuid, dossier_2.uuid]
            ),
            time_delta=timedelta(days=35),
        ).exists()
        is False
    )


def test_get_aggregate_page_objects_for_dossier(django_assert_max_num_queries):

    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    sem_docs = add_some_fake_semantic_documents(
        dossier,
        num_docs=2,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    # 2 doc x 5 pages x 2 page objects
    with django_assert_max_num_queries(1):

        assert len(get_aggregate_page_objects_for_dossier(dossier)) == 20

    semantic_document_1 = sem_docs[0]
    semantic_document_2 = sem_docs[1]

    semantic_document_1_semantic_page_page_objects = (
        SemanticPagePageObject.objects.filter(
            semantic_page__semantic_document=semantic_document_1
        ).select_related("page_object", "semantic_page")
    )

    semantic_document_2_semantic_page_page_objects = (
        SemanticPagePageObject.objects.filter(
            semantic_page__semantic_document=semantic_document_2
        ).select_related("page_object", "semantic_page")
    )

    # Set the first page object.key  of semantic_document_1_page_objects and semantic_document_2_page_objects
    # to be equal to each other expect no change in page object count as they are in different semantic docs

    page_object_key = semantic_document_1_semantic_page_page_objects[0].page_object.key

    semantic_document_2_semantic_page_page_objects_first_page_object = (
        semantic_document_2_semantic_page_page_objects[0].page_object
    )
    semantic_document_2_semantic_page_page_objects_first_page_object.key = (
        page_object_key
    )

    semantic_document_2_semantic_page_page_objects_first_page_object.save()

    # Check I've saved it
    assert (
        SemanticPagePageObject.objects.get(
            semantic_page__semantic_document=semantic_document_2,
            page_object__key=page_object_key,
        ).page_object
        == semantic_document_2_semantic_page_page_objects_first_page_object
    )

    with django_assert_max_num_queries(1):

        assert len(get_aggregate_page_objects_for_dossier(dossier)) == 20

    # Now set second page of semantic document 2 to have the same page object,
    # this should decrease the count as only fetch the first page object if duplicaes exist

    semantic_document_2_semantic_page_page_objects_second_page_object = (
        semantic_document_2_semantic_page_page_objects[1].page_object
    )
    semantic_document_2_semantic_page_page_objects_second_page_object.key = (
        page_object_key
    )

    semantic_document_2_semantic_page_page_objects_second_page_object.save()

    # Check I've saved it
    assert (
        SemanticPagePageObject.objects.filter(
            semantic_page__semantic_document=semantic_document_2,
            page_object__key=page_object_key,
        ).count()
        == 2
    )

    with django_assert_max_num_queries(1):

        assert len(get_aggregate_page_objects_for_dossier(dossier)) == 19
