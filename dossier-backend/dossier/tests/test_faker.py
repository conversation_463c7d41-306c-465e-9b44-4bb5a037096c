from dossier.factories import (
    <PERSON>ssierFactory,
    OriginalFileFactory,
    ProcessedFileFactory,
    ProcessedPageFactory,
    FakePageDossierFile,
    create_fake_page,
    SemanticDocumentFactory,
    AccountFactory,
)
from dossier.fakes import (
    create_fake_account_with_dossiers,
)
from dossier.models import (
    OriginalFile,
    FileStatus,
)
from dossier.services import image_to_content_file


def test_fake_semantic_document(db):
    create_fake_account_with_dossiers()


def test_dossier_factory(db):
    account = AccountFactory()
    dossier = DossierFactory(name="test dossier", account=account)
    assert dossier.name == "test dossier"
    assert dossier.account == account
    assert dossier.owner


def test_fake_page_dossier_file(db):
    FakePageDossierFile()


def test_create_fake_page_dossier_file(db):
    image = create_fake_page("55")
    filename = "image.jpg"
    content_file = image_to_content_file(image, filename)
    original_file: OriginalFile = OriginalFileFactory(content_file=content_file)

    assert original_file.file.name == filename
    content_file.seek(0)
    assert original_file.file.data.read() == content_file.read()
    assert original_file.status == FileStatus.PROCESSING


def test_processed_file_factory(db):
    processed_file = ProcessedFileFactory()
    assert processed_file.dossier.account
    assert processed_file.extracted_file


def test_semantic_document_factory(db):
    semantic_document = SemanticDocumentFactory()
    assert semantic_document.dossier


def test_processed_page_factory(db):

    processed_page = ProcessedPageFactory()
    assert processed_page.processed_file
    assert processed_page.dossier
    assert processed_page.number == 0
