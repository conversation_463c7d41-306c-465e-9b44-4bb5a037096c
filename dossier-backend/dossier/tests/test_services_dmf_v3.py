import json

import pytest
from django.contrib.auth import get_user_model
from ninja.errors import HttpError

from bekb.models import BEKBDossierProperties
from bekb.services import set_pers
from dossier.conftest import prepare_test_dossier_bekb
from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import UserInvolvement
from dossier.services_dmf_v3 import (
    assign_user_to_dossier,
    export_page_objects_for_hurdle_view,
)
from processed_file.models import PageObjectType, PageObject

User = get_user_model()

pytestmark = pytest.mark.django_db


def test_assign_user_to_dossier(bekbuser1_user, bekbuser2_user):
    account, dossier, dossier.owner = prepare_test_dossier_bekb()

    involvement = UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE")

    assert involvement.user.user != bekbuser1_user
    assert involvement.user.user != bekbuser2_user

    bekb_property = BEKBDossierProperties.objects.get(
        account=dossier.account, dossier=dossier
    )
    assert bekb_property.pers is False

    # Test 1: assign a normal dossier to a normal user
    assign_user_to_dossier(dossier=dossier, dossier_user=bekbuser1_user)
    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE").user
        == bekbuser1_user
    )

    # Test 2: assign dossier again to same user -> does nothing
    assign_user_to_dossier(dossier=dossier, dossier_user=bekbuser1_user)
    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE").user
        == bekbuser1_user
    )

    # Test 3: Fail to assign a pers dossier to a non-pers user
    # Update to pers = true
    bekb_property.pers = True
    bekb_property.save()

    with pytest.raises(
        HttpError,
        match="Can not assign a user without pers group to dossier which as pers enabled",
    ):
        assign_user_to_dossier(dossier=dossier, dossier_user=bekbuser2_user)

    # Test 4: Assign pers dossier to pers user
    set_pers(dossier.account, user=bekbuser2_user.user, pers=True)
    assign_user_to_dossier(dossier=dossier, dossier_user=bekbuser2_user)
    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="ASSIGNEE").user
        == bekbuser2_user
    )

    # Test 5: Assign user to a different dossier role (e.g. FICO)
    bekb_property.pers = False
    bekb_property.save()
    involvement_fico = UserInvolvement.objects.get(dossier=dossier, role__key="FICO")
    assert involvement_fico.user.user != bekbuser1_user
    assert involvement_fico.user.user != bekbuser2_user
    assign_user_to_dossier(
        dossier=dossier,
        dossier_user=bekbuser1_user,
        dossier_roles=[str(involvement_fico.role.uuid)],
    )

    assert (
        UserInvolvement.objects.get(dossier=dossier, role__key="FICO").user
        == bekbuser1_user
    )

    # Test 6: Fail to assign dossier with non existing user role to user
    with pytest.raises(
        HttpError,
        match="Can not assign a user .+ to an invalid role .+",
    ):
        assign_user_to_dossier(
            dossier=dossier,
            dossier_user=bekbuser1_user,
            dossier_roles=[
                "10000000-1127-4344-b407-************"
            ],  # fake uuid that is not valid
        )


def test_export_page_objects_for_hurdle_view(synthetic_dossier):
    # setup
    NUM_DOCS = 2
    NUM_PAGES = 1
    NUM_SPPOS = 10
    semantic_documents = add_some_fake_semantic_documents(
        dossier=synthetic_dossier,
        num_docs=NUM_DOCS,
        allow_empty_docs=False,
        min_num_pages=NUM_PAGES,
        max_pages=NUM_PAGES,
        no_page_objects_per_page=NUM_SPPOS,
    )
    assert len(semantic_documents) == NUM_DOCS

    assert len(export_page_objects_for_hurdle_view(synthetic_dossier)) == 0

    page_object_type_finhurdle, _ = PageObjectType.objects.get_or_create(
        name="FINHURDLE"
    )
    page_object_type_not_finhurdle, _ = PageObjectType.objects.get_or_create(
        name="NOT_FINHURDLE"
    )
    PageObject.objects.filter(
        semantic_page_page_objects__semantic_page__semantic_document__in=semantic_documents
    ).update(
        type=page_object_type_not_finhurdle,
        visible=True,
        value=json.dumps({"value": ""}),
    )

    # no deleted semdocs - no finhurde sppos
    assert (
        len(export_page_objects_for_hurdle_view(synthetic_dossier)) == 0
    ), "No finhurdle sppos should be retrieved"

    # no deleted semdocs - all sppos are finhurdle
    PageObject.objects.filter(
        semantic_page_page_objects__semantic_page__semantic_document__in=semantic_documents
    ).update(
        type=page_object_type_finhurdle, visible=True, value=json.dumps({"value": ""})
    )
    assert (
        len(export_page_objects_for_hurdle_view(synthetic_dossier)) == 2 * NUM_SPPOS
    ), "All sppos should be retrieved"

    # delete one semantic document
    deleted_semantic_document = semantic_documents[0]
    deleted_semantic_document.delete()
    retrieved_sppos = export_page_objects_for_hurdle_view(synthetic_dossier)
    assert (
        len(retrieved_sppos) == NUM_SPPOS
    ), "All sppos of the alive semdoc should be retrieved"

    # set one sppo in alive semdoc to be non-finhurdle
    alive_semantic_document = semantic_documents[1]
    sppo = PageObject.objects.filter(
        semantic_page_page_objects__semantic_page__semantic_document=alive_semantic_document
    ).first()
    sppo.type = page_object_type_not_finhurdle
    sppo.save()
    retrieved_sppos = export_page_objects_for_hurdle_view(synthetic_dossier)
    assert (
        len(retrieved_sppos) == NUM_SPPOS - 1
    ), "All but one sppos of the alive semdoc should be retrieved"
