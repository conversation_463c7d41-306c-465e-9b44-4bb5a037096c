import structlog
from django.utils import timezone as django_timezone

from dossier.helpers_timezone import log_timezone_info

logger = structlog.get_logger(__name__)


def test_timezones():
    utc_now = django_timezone.now()  # Aware datetime in UTC
    zurich_now = django_timezone.localtime(utc_now)  # Convert to Zurich time

    logger.info("UTC:", utc=utc_now, utc_str=str(utc_now))
    logger.info("Zurich:", zurich=zurich_now, zurich_str=str(zurich_now))


def test_timestamp_stuff():
    """
    This must always pass - intend is rather to demonstrate the use of timestamp then to test Django behavior
    :return:
    """
    utc_now = django_timezone.now()  # Aware datetime in UTC
    zurich_now = django_timezone.localtime(utc_now)  # Convert to Zurich time

    now_ts = int(zurich_now.timestamp())
    now_ts_utc = int(utc_now.timestamp())

    assert now_ts_utc == now_ts


def test_log_timezone_info():
    log_timezone_info(logger)
