from pathlib import Path
from uuid import uuid4

import pytest
import requests
from django.core.files.base import ContentFile

from dossier.models import <PERSON>ssier<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Account, generate_path
from dossier.s3_storage import split_bucket_objectname


def test_root_and_object_name():
    name = "bucket/prefix/prefix2/filename.test"
    bucket_name, object_name = split_bucket_objectname(name)
    assert bucket_name == "bucket"
    assert object_name == "prefix/prefix2/filename.test"


@pytest.mark.django_db
def test_generate_path():
    filename = "my_filename.txt"
    file_uuid = uuid4()
    dossier_file, dossier_uuid = create_sample_dossier_file(b"", file_uuid, filename)

    file_path = f"dossier/{dossier_uuid}/files/{file_uuid}/my_filename.txt"
    assert generate_path(dossier_file, filename) == file_path
    assert dossier_file.data.name == file_path


@pytest.mark.django_db
def test_basic_dossier_file_creation():
    content = b"hallo 123"
    filename = "my_filename.txt"
    file_uuid = uuid4()
    dossier_file, dossier_uuid = create_sample_dossier_file(
        content, file_uuid, filename
    )
    assert dossier_file.data.read() == content

    res = requests.get(dossier_file.fast_url)
    res.raise_for_status()


@pytest.mark.django_db
def test_basic_dossier_file_fast_url():
    content = b"hallo 123"
    filename = "my_filename.txt"
    file_uuid = uuid4()
    dossier_file, dossier_uuid = create_sample_dossier_file(
        content, file_uuid, filename
    )
    assert dossier_file.data.read() == content

    fast_url = dossier_file.fast_url
    res = requests.get(fast_url)
    res.raise_for_status()

    file_path = f"dossier/{dossier_uuid}/files/{file_uuid}/my_filename.txt"
    assert generate_path(dossier_file, filename) == file_path


@pytest.mark.django_db
def test_dossier_file_put():
    dossier_file, dossier_uuid = create_sample_dossier_file(
        b"old_content", uuid4(), "my_filename.txt"
    )
    content_v2 = b"new hello word"
    res = requests.put(dossier_file.put_url, data=content_v2)
    res.raise_for_status()

    dossier_file.refresh_from_db()
    assert dossier_file.data.read() == content_v2


def create_sample_dossier_file(content, file_uuid, filename):
    account = Account.objects.create(key="test", name="Test")
    dossier_uuid = uuid4()
    dossier = Dossier.objects.create(
        uuid=dossier_uuid, name="Testdossier", account=account
    )
    dossier_file = DossierFile.objects.create(
        uuid=file_uuid,
        dossier=dossier,
        bucket="dossier",
        data=ContentFile(content, filename),
    )
    return dossier_file, dossier_uuid


@pytest.mark.django_db
def test_put_dossier_file_link():
    account = Account.objects.create(key="test", name="Test")
    dossier = Dossier.objects.create(name="Testdossier", account=account)

    dossier_file = DossierFile(dossier=dossier)
    dossier_file.uuid = uuid4()
    dossier_file.bucket = "dossier"
    dossier_file.data.name = generate_path(
        dossier_file, "240_betreibungsauskunft-mt-at.pdf"
    )
    dossier_file.save()
    url = dossier_file.put_url
    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    dossier_file.refresh_from_db()
    with file_path.open("rb") as fp:
        requests.put(url, data=fp.read())

    assert (
        dossier_file.sha256sum()
        == "0de4c2b56fdd5aae35dcc1b06554700b3a1603673478f5e2cae341738a72748f"
    )
