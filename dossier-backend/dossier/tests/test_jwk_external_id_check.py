import os
import uuid

import pytest
from django.urls import reverse
import jwt

from dossier import services
from dossier.services import create_expiration_date
from django.conf import settings
from projectconfig.authentication import get_user_or_create
from projectconfig.jwk import load_jwk_from_env, convert_jwk_to_pem

from swissfex.conftest import (
    DATA_PATH,
    AuthenticatedClient,
    mock_jwks_public,  # noqa: F401
    set_swissfex_JWK,  # noqa: F401
    swissfex_account,  # noqa: F401
)


@pytest.mark.django_db
def test_access_dossier_via_external_id_jwk(
    swissfex_account, mock_jwks_public, set_swissfex_JWK
):
    external_dossier_id = "some_dossier_id"

    mock_jwks_public_private = load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()

    token_data = {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-swissfex-first service-swissfex-last",
        "given_name": "service-swissfex-first",
        "family_name": "service-swissfex-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        "user_roles": ["api_role"],
        "account_key": "swissfexd",
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }

    key = mock_jwks_public_private["keys"][0]
    mock_token = jwt.encode(
        payload={
            "aud": "account",
            "user_roles": [settings.API_ROLE],
            "external_dossier_id": external_dossier_id,
            **token_data,
        },
        key=convert_jwk_to_pem(key),
        algorithm="RS256",
    )

    authenticated_client = AuthenticatedClient(token=mock_token)

    dossier_user = get_user_or_create(
        account=swissfex_account, username="<EMAIL>"
    )
    dossier = services.create_dossier(
        account=swissfex_account,
        dossier_name="Example Dossier",
        language="en",
        owner=dossier_user.user,
        external_id=external_dossier_id,
    )
    dossier.save()

    response = authenticated_client.get(
        reverse(
            "api:get_dossier_external_id",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    assert response.status_code == 200

    # Now encode an incorrect external_dossier_id
    mock_token = jwt.encode(
        payload={
            "aud": "account",
            "user_roles": [settings.API_ROLE],
            "external_dossier_id": "wrong",
            **token_data,
        },
        key=convert_jwk_to_pem(key),
        algorithm="RS256",
    )

    mis_authenticated_client = AuthenticatedClient(token=mock_token)

    response = mis_authenticated_client.get(
        reverse(
            "api:get_dossier_external_id",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    assert response.status_code == 404
