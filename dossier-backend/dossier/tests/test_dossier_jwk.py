import pytest
from django.core.exceptions import ValidationError as DjangoValidationError

from dossier.models import Account, JWK
from jwcrypto import jwk as jwcrypto_jwk

from projectconfig.jwk import validate_jwk

pytestmark = pytest.mark.django_db


def generate_jwk_public():
    jwk = jwcrypto_jwk.JWK.generate(
        kty="RSA", kid="swissfex", size=2048, use="sig", alg="RS256"
    )
    return jwk.export_public(as_dict=True)


def test_jwk_creation():
    jwk_data = generate_jwk_public()
    account = Account.objects.create(
        name="swissfex development account",
        default_bucket_name="dms-default-bucket",
        key="swissfexd",
    )

    JWK.objects.all().delete()

    jwk = JWK.objects.create(account=account, jwk=jwk_data)

    assert jwk.jwk == jwk_data
    assert JWK.objects.count() == 1
    # Test reverse relation
    assert account.jwks.first() == jwk


def test_validate_jwk_valid():
    # Should not raise an exception
    validate_jwk(generate_jwk_public())


def test_validate_jwk_invalid():
    jwks_data = generate_jwk_public()
    # Missing 'e' value
    jwks_data.pop("e")

    with pytest.raises(DjangoValidationError):
        validate_jwk(jwks_data)
