from typing import List

import pytest
from django.contrib.auth import get_user_model
from pydantic import TypeAdapter

from core.authentication import AuthenticatedClient
from dossier.schemas import FileBasics

User = get_user_model()

pytestmark = pytest.mark.django_db


def test_exporter_filebasics_401(testuser1_client: AuthenticatedClient):
    start = "20200101"
    end = "********"
    result = testuser1_client.get(
        f"/api/internal/exporter/filebasics?start={start}&end={end}"
    )
    assert result.status_code == 401


@pytest.mark.skip(
    reason="authentication for internal api must be done. Or locally disable 'if not request.is_internal'"
)
def test_exporter_filebasics_200(testuser1_client: AuthenticatedClient):
    start = "20200101"
    end = "********"
    result_1 = testuser1_client.get(
        f"/api/internal/exporter/filebasics?start={start}&end={end}"
    )

    assert result_1.status_code == 200

    r1: List[FileBasics] = TypeAdapter(List[FileBasics]).validate_python(
        result_1.json()
    )
    assert len(r1) == 27
    r1_pages = [len(fb.pages) for fb in r1]
    r1_num_pages = sum(r1_pages)
    assert r1_num_pages == 100

    result_2 = testuser1_client.get(
        f"/api/internal/exporter/filebasics?start={start}&end={end}&account_key=default&allow_deleted=False"
    )
    assert result_2.status_code == 200
    r2: List[FileBasics] = TypeAdapter(List[FileBasics]).validate_python(
        result_2.json()
    )
    assert len(r2) == 24
    r2_pages = [len(fb.pages) for fb in r2]
    r2_num_pages = sum(r2_pages)
    assert r2_num_pages == 98
