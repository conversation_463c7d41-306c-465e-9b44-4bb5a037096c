from datetime import <PERSON><PERSON><PERSON>
from enum import Enum
from http import HTT<PERSON>tatus
from pathlib import Path
from typing import List
from unittest.mock import patch
from uuid import uuid4
import uuid

import django
import pytest
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils import timezone
from freezegun import freeze_time
from pydantic import TypeAdapter
from pytest_mock import MockerFixture

import statemgmt.schemas as smgmt_schema
from bekb.models import BEKBDossierProperties, Partner
from bekb.services import set_pers
from core.authentication import AuthenticatedClient
from core.schema import Message
from dossier import schemas
from dossier.api import (
    create_semantic_dossier_simple,
)
from dossier.conftest import prepare_test_dossier_bekb
from dossier.dossier_access_external import (
    create_or_update_access_grant,
    DAP_DEFAULT_ACCESS_GRANT_CHECK,
    install_dossier_access_check_providers,
    DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME,
    DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME,
)
from dossier.helpers import get_dossier_queryset
from dossier.helpers_v2 import (
    check_enable_dossier_permission,
)
from dossier.models import (
    Account,
    AccessDelegation,
    DocumentCategory,
    ConfidenceLevel,
    DossierExport,
    DossierAccessCheckProvider,
    DossierAccessGrant,
    Scope,
)
from dossier.models import (
    Dossier,
    DossierRole,
    OriginalFile,
    annotate_with_calculated_access_mode,
    DossierUser,
)
from dossier.statemachine_types import DOSSIER_READ_ONLY_WORK_STATUS
from dossier.schemas import (
    SemanticDossierName,
    UpdatePageObjectConfidenceRequest,
    SemanticDossierSimple,
    SemanticDocumentBasics,
    AccessMode,
)
from dossier.services import (
    dossier_list,
    check_delegation_exist,
    get_users_on_same_account,
    check_valid_delegation_exists,
    delegation_list,
    create_dossier,
    is_pers,
)
from dossier.tests.common import tax_declaration_max_mustermann
from dossier.tests.data import DATA_PATH
from image_exporter import schemas as dossier_zipper_schemas
from image_exporter import schemas as image_exporter_schemas
from projectconfig.authentication import get_user_or_create
from semantic_document.helpers import get_start_work_status_from_dossier_account
from semantic_document.models import (
    SemanticDocument,
    SemanticPagePageObject,
    SemanticPageUserAnnotations,
)
from semantic_document.schemas_page_annotations import SemanticPageUserAnnotationsSchema
from statemgmt.models import Status, StateMachine
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401

User = get_user_model()


def test_unauthorized_api_access_without_credentials(client: django.test.client.Client):
    response = client.post("/api/dossier/")
    assert response.status_code == HTTPStatus.UNAUTHORIZED


@pytest.mark.django_db
def test_create_dossier_api(testuser1_client: AuthenticatedClient):
    result = testuser1_client.post(
        "/api/dossier/",
        data=schemas.CreateDossier(name="Test Dossier", lang="fr").model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200
    object = schemas.CreatedObjectReference.model_validate_json(result.content)

    assert object.uuid
    dossier = Dossier.objects.get(uuid=object.uuid)
    assert dossier.userinvolvement_set.count() == 1


def test_list_dossier(
    db,
    testuser1_client: AuthenticatedClient,
    django_assert_num_queries,
    django_assert_max_num_queries,
):
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = False
    assert django_assert_num_queries(26)

    with django_assert_max_num_queries(22):
        account.enable_dossier_permission = True
        account.save()
        result = testuser1_client.get(
            "/api/dossier/?number_page=0", content_type="application/json"
        )
        assert result.status_code == 200
        expected = schemas.PaginatedDossier.model_validate_json(
            (DATA_PATH / "simple_list_dossier_original_files.json").read_text()
        )
        result = schemas.PaginatedDossier.model_validate_json(result.content)
        assert result == expected


def test_account_allow_dossier_listing(db, testuser1_client: AuthenticatedClient):
    account = Account.objects.get(key="default")
    account.allow_dossier_listing = False
    account.save()
    result = testuser1_client.get(
        "/api/dossier/?number_page=0", content_type="application/json"
    )
    assert result.status_code == 401


def test_account_allow_dossier_listing_as_dossier_manager(
    db, dossier_manager_client: AuthenticatedClient
):
    account = Account.objects.get(key="default")
    account.allow_dossier_listing = False
    account.save()
    result = dossier_manager_client.get(
        "/api/dossier/?number_page=0", content_type="application/json"
    )
    assert result.status_code == 200


def test_list_dossier_direct(
    db, testuser1_client: AuthenticatedClient, django_assert_num_queries
):
    with django_assert_num_queries(6):
        res = dossier_list(
            username="<EMAIL>", account_key="default", is_manager=True
        )
        print(res)


def test_list_dossier_with_read_only_dossier(db, testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    dossier.work_status = Status.objects.get(
        key=DOSSIER_READ_ONLY_WORK_STATUS[0], state_machine=StateMachine.objects.first()
    )
    dossier.save()
    result = testuser1_client.get(
        "/api/dossier/?number_page=0", content_type="application/json"
    )
    assert result.status_code == 200
    actual = schemas.PaginatedDossier.model_validate_json(result.content)

    assert actual.data[0].access_mode == Dossier.DossierAccessMode.READ_ONLY


def test_list_dossier_delegation_list(
    db, testuser2_client: AuthenticatedClient, django_assert_num_queries
):
    # Users Data frr testng
    User = get_user_model()
    user2, _ = User.objects.get_or_create(username="<EMAIL>")
    user3, _ = User.objects.get_or_create(username="<EMAIL>")
    user4, _ = User.objects.get_or_create(username="<EMAIL>")
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = False
    assert not account.enable_dossier_permission
    assert django_assert_num_queries(26)

    with django_assert_num_queries(13):
        account.enable_dossier_permission = True
        account.save()

        # 1. testuser2 has no dossiers, gets empty list of dossiers
        assert check_enable_dossier_permission(account) is True
        result = testuser2_client.get(
            "/api/dossier/?number_page=0", content_type="application/json"
        )
        assert result.status_code == 200
        result = schemas.PaginatedDossier.model_validate_json(result.content)
        assert result.total_dossiers_count == 0

    # 2. Create one access delegation, get list of 1 dossier
    access_delegation, _ = AccessDelegation.objects.get_or_create(
        account=account, delegator=user3, delegate=user2
    )
    dossier3 = Dossier.objects.get(name="sales pitch mix with errors dossier")
    dossier3.owner = user3
    dossier3.save()
    assert check_valid_delegation_exists(account, user3, user2) is True
    result = testuser2_client.get(
        "/api/dossier/?number_page=1", content_type="application/json"
    )
    assert result.status_code == 200
    result = schemas.PaginatedDossier.model_validate_json(result.content)
    assert result.total_dossiers_count == 1

    # 3. Disable access delegation, get list of 0 dossiers
    account.enable_dossier_permission = False
    account.save()
    assert check_enable_dossier_permission(account) is False
    assert check_valid_delegation_exists(account, user3, user2) is True
    result = testuser2_client.get(
        "/api/dossier/?number_page=1", content_type="application/json"
    )
    assert result.status_code == 200
    result = schemas.PaginatedDossier.model_validate_json(result.content)
    assert result.total_dossiers_count == 0

    # 4. Add second delegation and enable access delegation (no filter_owner_username) -> list of 2 dossiers
    account.enable_dossier_permission = True
    account.save()
    assert check_enable_dossier_permission(account) is True
    access_delegation, _ = AccessDelegation.objects.get_or_create(
        account=account, delegator=user4, delegate=user2
    )
    dossier4 = Dossier.objects.get(name="image export samples")
    dossier4.owner = user4
    dossier4.save()
    assert check_valid_delegation_exists(account, user4, user2) is True
    result = testuser2_client.get(
        "/api/dossier/?number_page=1", content_type="application/json"
    )
    assert result.status_code == 200
    result = schemas.PaginatedDossier.model_validate_json(result.content)
    assert result.total_dossiers_count == 2

    # 5. Single value in filter_owner_username
    result = testuser2_client.get(
        "/api/dossier/?number_page=1&filter_owner_username=<EMAIL>",
        content_type="application/json",
    )
    assert result.status_code == 200
    result = schemas.PaginatedDossier.model_validate_json(result.content)
    assert result.total_dossiers_count == 1
    assert result.data[0].name == "sales pitch mix with errors dossier"

    # 6. Multiple comma separated values in filter_owner_username
    result = testuser2_client.get(
        "/api/dossier/?number_page=1&filter_owner_username=<EMAIL>,<EMAIL>",
        content_type="application/json",
    )
    assert result.status_code == 200
    result = schemas.PaginatedDossier.model_validate_json(result.content)
    assert result.total_dossiers_count == 2


def test_annotate_with_calculated_access_mode(db):
    qs = Dossier.objects
    user = User.objects.get(email="<EMAIL>")
    d = annotate_with_calculated_access_mode(qs, user)

    # This is read only because of the work status (and the user has no effect because no DossierAccessGrant exists)
    dossier1 = d.filter(work_status__key__in=DOSSIER_READ_ONLY_WORK_STATUS).first()
    assert dossier1.calculated_access_mode == Dossier.DossierAccessMode.READ_ONLY

    # This is read write because of the work status (and the user has no effect because no DossierAccessGrant exists)
    dossier2 = d.exclude(work_status__key__in=DOSSIER_READ_ONLY_WORK_STATUS).first()
    assert dossier2.calculated_access_mode == Dossier.DossierAccessMode.READ_WRITE

    DossierAccessGrant.objects.update_or_create(
        user=user,
        dossier=dossier2,
        defaults={
            "expires_at": (timezone.now() + timezone.timedelta(days=1)),
            "scope": Scope.READ_ONLY,
            "has_access": True,
        },
    )

    # This is read only because of user and access grant (despite the work status)
    dossier3 = d.exclude(work_status__key__in=DOSSIER_READ_ONLY_WORK_STATUS).first()
    assert dossier3.calculated_access_mode == Dossier.DossierAccessMode.READ_ONLY

    DossierAccessGrant.objects.update_or_create(
        user=user,
        dossier=dossier2,
        defaults={
            "scope": Scope.READ_WRITE,
        },
    )

    # This is read write because of user and access grant on RW (despite the work status)
    dossier4 = d.exclude(work_status__key__in=DOSSIER_READ_ONLY_WORK_STATUS).first()
    assert dossier4.calculated_access_mode == Dossier.DossierAccessMode.READ_WRITE


@pytest.mark.django_db
def test_dossier_file_upload_api_read_only(
    testuser1_client, django_assert_max_num_queries, mocker: MockerFixture
):
    # Test behaviour of get_writable_dossier_from_request_with_access_check
    # enforcing read-only access mode
    response = testuser1_client.post(
        "/api/dossier/",
        schemas.CreateDossier(lang="fr").model_dump_json(),
        "application/json",
    )
    dossier_uuid = schemas.CreatedObjectReference(**response.json()).uuid

    dossier = Dossier.objects.get(uuid=dossier_uuid)
    dossier.access_mode = Dossier.DossierAccessMode.READ_ONLY
    dossier.save()

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    mocker.patch("dossier.api.process_original_file")

    with django_assert_max_num_queries(11):
        response = testuser1_client.post(
            f"/api/dossier/{dossier_uuid}/original_files",
            data={"file": file},
            format="multipart",
        )

    assert response.status_code == HTTPStatus.FORBIDDEN


@pytest.mark.django_db
def test_dossier_file_upload_api(
    testuser1_client, django_assert_max_num_queries, mocker: MockerFixture
):
    response = testuser1_client.post(
        "/api/dossier/",
        schemas.CreateDossier(lang="fr").model_dump_json(),
        "application/json",
    )
    dossier_uuid = schemas.CreatedObjectReference(**response.json()).uuid

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    process_original_file_mock = mocker.patch("dossier.api.process_original_file")

    # Same amount of queries are done, for get_writable_dossier_from_request_with_access_check and
    # get_dossier_from_request_with_access_check i.e. additional check correctly prefetches
    with django_assert_max_num_queries(14):

        response = testuser1_client.post(
            f"/api/dossier/{dossier_uuid}/original_files",
            data={"file": file},
            format="multipart",
        )

    assert response.status_code == HTTPStatus.OK
    original_file = schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    # same file should not be allowed
    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )
    response = testuser1_client.post(
        f"/api/dossier/{dossier_uuid}/original_files",
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == HTTPStatus.CONFLICT

    dossier = Dossier.objects.get(uuid=dossier_uuid)
    original_files = dossier.original_files.all()
    assert len(original_files) == 1
    original_file = original_files[0]
    assert original_file.file.name == "240_betreibungsauskunft-mt-at.pdf"

    # same file with is_duplicate true, should be allowed
    response = testuser1_client.post(
        f"/api/dossier/{dossier_uuid}/original_files?is_duplicate=true",
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == HTTPStatus.OK
    original_file = schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[1].args[0] == original_file


def test_jwt_authenticated_api(
    db, testuser1_client: AuthenticatedClient, django_assert_max_num_queries
):
    with django_assert_max_num_queries(12):
        result = testuser1_client.get("/api/jwt")
        assert result.status_code == 200
        assert result.json().get("data from token")


def test_authentication_fails_for_inactive_users(
    db, testuser1_client: AuthenticatedClient
):
    User = get_user_model()
    User.objects.filter(username="<EMAIL>").update(is_active=False)
    result = testuser1_client.get("/api/jwt")
    print(result.content)
    assert result.status_code == 401


class Method(Enum):
    GET = "GET"
    POST = "POST"
    DELETE = "DELETE"
    PUT = "PUT"


@pytest.mark.django_db
def test_testuser2_has_no_access_to_the_dossiers_of_testuser1(testuser2_client):
    """test user 2 should not have access to the dossier of testuser1"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    dossier_uuid = str(dossier.uuid)

    # tests with an existing semantic document
    semantic_document = tax_declaration_max_mustermann()
    semantic_document_uuid = semantic_document.uuid

    test_urls = [
        (Method.GET, f"/api/dossier/{dossier_uuid}/data_v2", 404),
        (
            Method.GET,
            f"/api/dossier/{dossier_uuid}/semantic-document/{semantic_document_uuid}/document-category-recommendations",
            404,
        ),
        (
            Method.GET,
            f"/api/dossier/{dossier_uuid}/semantic-document/{semantic_document_uuid}/doctype-recommendation",
            404,
        ),
        (
            Method.GET,
            f"/{dossier_uuid}/semantic-document/{semantic_document_uuid}/title-suffix-recommendations",
            404,
        ),
        (Method.GET, f"/api/dossier/{dossier.uuid}/available-document-categories", 404),
    ]

    for method, url, expected_status_code in test_urls:
        assert (
            testuser2_client.generic(method.value, url).status_code
            == expected_status_code
        )


@pytest.mark.django_db
def test_title_suffix_recommendations_api(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    dossier_uuid = str(dossier.uuid)

    # tests with an existing semantic document
    semantic_document = tax_declaration_max_mustermann()
    semantic_document_uuid = semantic_document.uuid

    result = testuser1_client.get(
        f"/api/dossier/{dossier_uuid}/semantic-document/{semantic_document_uuid}/title-suffix-recommendations"
    )
    assert result.status_code == 200
    recommendations = TypeAdapter(List[str]).validate_python(result.json())
    assert recommendations == ["TAX_max_maria_2019 4.pdf"]


@pytest.mark.django_db
def test_document_category_recommendations_api(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="image export samples")
    semantic_document = SemanticDocument.objects.get(
        title_custom="610 Verkaufsdokumentation", dossier=dossier
    )

    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/semantic-document/{semantic_document.uuid}/document-category-recommendations"
    )
    assert result.status_code == 200

    document_categories_translated = TypeAdapter(
        List[schemas.DocumentCategoryTranslated]
    ).validate_python(result.json())

    # Sometimes under parallel tests we get a recommendation for 670 instead of 615 for middle element
    # added pytest retry flaky flag for this
    #     assert sorted(document_categories_translated, key=lambda u: u.id) == sorted(
    #         [
    #             schemas.DocumentCategoryTranslated(
    #                 name="PLAN_FLOOR", id="604", translation="Grundriss"
    #             ),
    #             # Sometimes under parallel tests we get a recommendation for 670 instead of 615 for middle element
    #             # added pytest retry flaky flag for this
    #             schemas.DocumentCategoryTranslated(
    #                 name="PROPERTY_PHOTOS", id="615", translation="Fotos Liegenschaft"
    #             ),
    #             schemas.DocumentCategoryTranslated(
    #                 name="UNKNOWN_DE", id="981", translation="DOK DE"
    #             ),
    #         ],
    #         key=lambda u: u.id,
    #     )

    # Define both possible middle elements
    possible_middle_elements = [
        schemas.DocumentCategoryTranslated(
            name="PROPERTY_PHOTOS", id="615", translation="Fotos Liegenschaft"
        ),
        schemas.DocumentCategoryTranslated(
            name="ALTERNATIVE_CATEGORY",
            id="670",
            translation="Baubeschrieb",
        ),
    ]

    # Check that the response contains exactly 3 items
    assert len(document_categories_translated) == 3

    # Sort both lists for comparison
    sorted_response = sorted(document_categories_translated, key=lambda u: u.id)

    # Check first and last elements which are always consistent
    assert sorted_response[0] == schemas.DocumentCategoryTranslated(
        name="PLAN_FLOOR", id="604", translation="Grundriss"
    )
    assert sorted_response[2] == schemas.DocumentCategoryTranslated(
        name="UNKNOWN_DE", id="981", translation="DOK DE"
    )

    # Check that middle element is one of the possible values
    assert sorted_response[1] in possible_middle_elements


@pytest.mark.django_db
def test_document_category_available_api(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    tax_declaration_max_mustermann()

    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/available-document-categories"
    )
    assert result.status_code == 200

    document_categories_translated = TypeAdapter(
        List[schemas.DocumentCategoryTranslated]
    ).validate_python(result.json())
    assert len(document_categories_translated) == 271


@pytest.mark.django_db
def test_document_categories_api(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    # Fix the flag for this category as it might not be set properly
    dc: DocumentCategory = DocumentCategory.objects.filter(
        account=dossier.account, name="UNKNOWN_DE"
    ).first()
    dc.exclude_for_recommendation = True
    dc.save()

    result = testuser1_client.get(f"/api/dossier/{dossier.uuid}/document-categories")
    assert result.status_code == 200
    document_categories = TypeAdapter(List[schemas.DocumentCategory]).validate_python(
        result.json()
    )
    assert len(document_categories) == 271

    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/document-categories?filter_names=TAX_DECLARATION,SALARY_CERTIFICATE,UNKNOWN_DE"
    )
    assert result.status_code == 200
    document_categories = TypeAdapter(List[schemas.DocumentCategory]).validate_python(
        result.json()
    )
    assert len(document_categories) == 3

    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/document-categories?filter_names=TAX_DECLARATION,SALARY_CERTIFICATE,UNKNOWN_DE&filter_recommendation=False"
    )
    assert result.status_code == 200
    document_categories = TypeAdapter(List[schemas.DocumentCategory]).validate_python(
        result.json()
    )
    assert len(document_categories) == 3

    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/document-categories?filter_names=TAX_DECLARATION,SALARY_CERTIFICATE,UNKNOWN_DE&filter_recommendation=True"
    )
    assert result.status_code == 200
    document_categories = TypeAdapter(List[schemas.DocumentCategory]).validate_python(
        result.json()
    )
    assert len(document_categories) == 2


@pytest.mark.django_db
def test_empty_dossier_list(testuser2_client: AuthenticatedClient):
    result = testuser2_client.get("/api/dossier/?number_page=1")
    assert result.status_code == 200
    assert schemas.PaginatedDossier.model_validate_json(result.content).data == []


@pytest.mark.django_db
def test_dossier_list_with_new_empty_dossier(testuser2_client: AuthenticatedClient):
    testuser2_client.post(
        "/api/dossier/",
        data=schemas.CreateDossier(name="Test Dossier", lang="it").model_dump_json(),
        content_type="application/json",
    )
    result = testuser2_client.get("/api/dossier/?number_page=1")
    assert result.status_code == 200
    assert len(schemas.PaginatedDossier.model_validate_json(result.content).data) == 1


@pytest.mark.django_db
def test_document_categories_update_for_non_internal_user_is_not_allowed(
    testuser1_client: AuthenticatedClient,
):
    account_key = "somerandomaccount"
    result = testuser1_client.put(
        f"/api/account/{account_key}/document-categories",
        data="[]",
        content_type="application/json",
    )
    assert result.status_code == 401
    message = Message.model_validate_json(result.content)
    assert message.detail == "Unauthorized"


@pytest.mark.django_db
def test_document_categories_update_does_not_work_with_invalid_account_key(
    internal_user_client: AuthenticatedClient,
):
    account_key = "invalidaccountkey"
    result = internal_user_client.put(
        f"/api/account/{account_key}/document-categories",
        data="[]",
        content_type="application/json",
    )
    assert result.status_code == 404
    message = Message.model_validate_json(result.content)
    assert message.detail == "Not Found"


@pytest.mark.django_db
def test_dossiercategory_update_works_with_internal_user_and_valid_account_key(
    internal_user_client: AuthenticatedClient,
):
    account_key = "default"
    result = internal_user_client.put(
        f"/api/account/{account_key}/document-categories",
        data="[]",
        content_type="application/json",
    )
    assert result.status_code == 202
    assert len(result.content) == 0


def test_create_semantic_dossier_simple(db):
    dossier = get_object_or_404(
        annotate_with_calculated_access_mode(
            get_dossier_queryset(), User.objects.get(email="<EMAIL>")
        ),
        name="sales pitch mix with errors dossier",
    )
    res = create_semantic_dossier_simple(dossier, False)

    res.role = []
    res.role_username = []
    expected = schemas.SemanticDossierSimple.model_validate_json(
        (DATA_PATH / "semantic_dossier_simple_1.json").read_text()
    )
    expected.role_username = []
    expected.role = []

    for _uuid, processed_file in res.processed_files.items():
        for number, page in processed_file.pages.items():
            page.image = "https://example.com/image"

    # TODO: disables the aggregated objects comparison because there
    for semantic_document in res.semantic_documents:
        semantic_document.aggregated_objects = []
        for page in semantic_document.semantic_pages:

            page.page_objects = sorted(page.page_objects, key=lambda x: x.uuid)

    for semantic_document in expected.semantic_documents:
        semantic_document.aggregated_objects = []
        for page in semantic_document.semantic_pages:

            page.page_objects = sorted(page.page_objects, key=lambda x: x.uuid)

    # Broken with nonsense diff output
    # assert SemanticDossierSimple.model_validate_json(
    #     expected.json()
    # ) == SemanticDossierSimple.model_validate_json(res.json())


def test_get_work_status_next_possible_states(db, bekbuser1_client):
    account, dossier, dossier.owner = prepare_test_dossier_bekb()

    result = bekbuser1_client.get(f"/api/dossier/{dossier.uuid}/next-possible-states")

    res = TypeAdapter(List[smgmt_schema.PossibleTransition]).validate_json(
        result.content
    )
    actual = smgmt_schema.PossibleTransitions(transitions=res)

    assert result.status_code == 200

    expected = TypeAdapter(List[smgmt_schema.PossibleTransition]).validate_json(
        (DATA_PATH / "next_possible_states_240408.json").read_text(),
    )

    assert sorted(actual.transitions, key=lambda u: u.uuid) == sorted(
        expected, key=lambda u: u.uuid
    )


def test_change_dossier_work_status(db, bekbuser1_client):
    account, dossier, dossier.owner = prepare_test_dossier_bekb()

    old_work_status = dossier.work_status

    result = bekbuser1_client.get(f"/api/dossier/{dossier.uuid}/next-possible-states")

    res: List[smgmt_schema.PossibleTransition] = TypeAdapter(
        List[smgmt_schema.PossibleTransition]
    ).validate_json(result.content)

    next_state = res[0].to_state
    assert next_state

    data = schemas.ChangeDossierWorkStatus(
        work_status_id=next_state.uuid
    ).model_dump_json()
    r2 = bekbuser1_client.patch(
        f"/api/dossier/{dossier.uuid}/change_dossier_work_status",
        data=data,
        content_type="application/json",
    )
    assert r2
    assert r2.status_code == 200

    new_dossier = Dossier.objects.get(name="Dossier von Francesca Eichenberger")
    new_work_state = new_dossier.work_status

    assert old_work_status != new_work_state


def test_check_update(
    db, testuser1_client: AuthenticatedClient, django_assert_max_num_queries
):
    with django_assert_max_num_queries(13):
        result = testuser1_client.post(
            "/api/dossier/check_update",
            data='[{"dossier_uuid":"254e93ec-c0f2-4133-be04-24170c60e650","updated_at":"2022-05-12T06:26:26.977Z","status":"Processed"},{"dossier_uuid":"d37277cf-1940-4f1e-acbc-24fb7d6da593","updated_at":"2022-05-12T06:26:40.187Z","status":"Processed"},{"dossier_uuid":"364adb5b-9e1b-4175-b39a-ce910575d5e0","updated_at":"2022-05-12T06:26:52.598Z","status":"Processed"}]',
            content_type="application/json",
        )
        assert result.status_code == 200
        print(result.content)


# @pytest.mark.skip("better comparison required, max_expiry_date needs fixing in test")
@patch("dossier.api.ENABLE_PAGE_OBJECTS_IN_DATA_V2", True)
def test_dossier_datav2(
    db, testuser1_client: AuthenticatedClient, django_assert_num_queries
):
    # If page objects are not delivered anymore this needs refactoring
    dossier: Dossier = get_dossier_queryset().get(
        name="sales pitch mix with errors dossier"
    )

    # the number of queries here is dynamic , needs more work
    # with django_assert_num_queries(37):
    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
    )

    assert result.status_code == 200

    actual = schemas.SemanticDossierSimple.model_validate_json(result.content)

    assert actual.role_keys == ["ASSIGNEE"]
    assert actual.role_username[0].role_key
    remove_urls(actual)

    # Test that we are actually serialising aggregate page objects and page objects
    assert len(actual.semantic_documents[0].aggregated_objects) > 0
    assert len(actual.semantic_documents[0].semantic_pages[0].page_objects) > 0
    assert actual.semantic_documents[0].created_at

    # expected = schemas.SemanticDossierSimple.parse_file(
    #     DATA_PATH / "simple_dossier_datav2.json"
    # )
    # remove_urls(expected)
    #
    # assert actual == expected


def test_dossier_datav2_empty_dossier(
    db, testuser1_client: AuthenticatedClient, django_assert_num_queries
):
    # Test that API works with empty dossier

    user = User.objects.get(username="<EMAIL>")

    dossier = create_dossier(
        Account.objects.get(key="default"),
        "dossier with case",
        "de",
        user,
    )
    dossier.external_id = "test external id"
    dossier.save()

    # the number of queries here is dynamic , needs more work
    # with django_assert_num_queries(37):
    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
    )

    assert result.status_code == 200

    actual = schemas.SemanticDossierSimple.model_validate_json(result.content)

    assert actual.semantic_documents == []


@patch("dossier.api.ENABLE_PAGE_OBJECTS_IN_DATA_V2", False)
def test_dossier_datav2_disable_page_objects(
    db,
    testuser1_client: AuthenticatedClient,
):
    # Test that the generation of the page objects can be disabled via the .env settings

    dossier: Dossier = get_dossier_queryset().get(
        name="sales pitch mix with errors dossier"
    )

    # the number of queries here is dynamic , needs more work
    # with django_assert_num_queries(37):
    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
    )

    assert result.status_code == 200

    actual = schemas.SemanticDossierSimple.model_validate_json(result.content)

    assert actual.role_keys == ["ASSIGNEE"]
    assert actual.role_username[0].role_key
    remove_urls(actual)

    # Test that we have disabled serialising aggregate page objects and page objects
    assert len(actual.semantic_documents[0].aggregated_objects) == 0
    assert len(actual.semantic_documents[0].semantic_pages[0].page_objects) == 0


def test_get_dossier_by_external_id(db, testuser1_client: AuthenticatedClient):
    dossier: Dossier = get_dossier_queryset().get(
        name="sales pitch mix with errors dossier"
    )

    dossier.external_id = "my_little_pony"
    dossier.save()
    result = testuser1_client.get(
        f"/api/dossier/external_id/{dossier.external_id}",
        content_type="application/json",
    )

    assert result.status_code == 200

    assert result.json()["uuid"] == str(dossier.uuid)


def test_dossier_datav2_access_pers_dossier_with_non_pers_user(
    db, bekbuser1_client, django_assert_num_queries
):
    dossier = Dossier.objects.get(name="Dossier von Francesca Eichenberger")
    User = get_user_model()
    dossier.owner, _ = User.objects.get_or_create(username="<EMAIL>")
    dossier.save()

    # Step 1: Check that the user has no pers privilege
    du, created = DossierUser.objects.get_or_create(
        account=dossier.account, user=dossier.owner
    )
    assert is_pers(du) is False

    # Step 2: Test that the user has access to the dossier
    path = f"/api/dossier/{dossier.uuid}/data_v2"
    # with django_assert_num_queries(75):
    result = bekbuser1_client.get(path, content_type="application/json")
    assert result.status_code == 200
    # TODO: add further testing, e.g. with this
    # actual = schemas.SemanticDossierSimple.model_validate_json(result.content)
    # remove_urls(actual)

    # Step 4: Change the pers flag of the dossier to True
    prop = BEKBDossierProperties.objects.filter(dossier=dossier).first()
    prop.pers = True
    prop.save()

    # Step 5: Test that the user has no access anymore
    result = bekbuser1_client.get(path, content_type="application/json")
    assert result.status_code == 404
    print(result)

    # Step 6: Add the pers privilege to the user
    set_pers(dossier.account, dossier.owner, True)

    # Step 7: Test that the user has access as a pers user
    result = bekbuser1_client.get(path, content_type="application/json")
    assert result.status_code == 200


def test_account_have_access_permission(db, testuser1_client):
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = False
    account.save()
    User = get_user_model()
    user, _ = User.objects.get_or_create(username="<EMAIL>")

    assert check_enable_dossier_permission(account) is False

    path = "/api/access_delegation/have_permission"

    # 1. Test with account that have no permission to access this feature
    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationPermission.model_validate_json(result.content)
    assert result.status_code == 200
    assert actual.account_have_access_delegation_permission is False

    # 2. Test with account that have permission to access this feature
    account.enable_dossier_permission = True
    account.save()
    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationPermission.model_validate_json(result.content)
    assert result.status_code == 200
    assert actual.account_have_access_delegation_permission is True


def test_access_delegation_in_different_accounts(db, testuser1_client):
    """
    Set up delegation user1 -> user2 in account a1.
    Validate that no delegation exists in account a2
    @param db:
    @param testuser1_client:
    @return:
    """
    a1 = Account.objects.get(key="default")
    assert a1
    a2 = Account.objects.get(key="account2")
    assert a2

    User = get_user_model()
    user1, _ = User.objects.get_or_create(username="<EMAIL>")
    assert user1
    user2, _ = User.objects.get_or_create(username="<EMAIL>")
    assert user2

    # 2. Test list empty
    assert len(delegation_list("delegate", user1, a1)) == 0
    assert len(delegation_list("delegator", user1, a1)) == 0

    access_delegation, _ = AccessDelegation.objects.get_or_create(
        account=a1, delegate=user1, delegator=user2
    )

    # Check that the delegation in the correct direction a1 -> a2 exists
    assert len(delegation_list("delegate", user1, a1)) == 1
    assert len(delegation_list("delegator", user2, a1)) == 1

    # Check that the delegation in the wrong direction a2 -> a1 does not exist
    assert len(delegation_list("delegate", user2, a1)) == 0
    assert len(delegation_list("delegator", user1, a1)) == 0

    # Check that no delegation in the other account exists
    assert len(delegation_list("delegate", user1, a2)) == 0
    assert len(delegation_list("delegator", user2, a2)) == 0
    assert len(delegation_list("delegate", user2, a1)) == 0
    assert len(delegation_list("delegator", user1, a1)) == 0

    assert len(AccessDelegation.objects.filter(account=a1).all()) == 1
    assert len(AccessDelegation.objects.filter(account=a2).all()) == 0


def test_get_list_access_delegation(db, testuser1_client):
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = False
    account.save()
    User = get_user_model()
    user1, _ = User.objects.get_or_create(username="<EMAIL>")
    user2, _ = User.objects.get_or_create(username="<EMAIL>")
    user3, _ = User.objects.get_or_create(username="<EMAIL>")

    assert check_enable_dossier_permission(account) is False

    path = "/api/access_delegation/"

    # 1. Test with account that have no permission to access this feature
    result = testuser1_client.get(path, content_type="application/json")
    assert result.status_code == 403

    account.enable_dossier_permission = True
    account.save()

    # 2. Test list empty
    assert len(delegation_list("delegate", user1, account)) == 0
    assert len(delegation_list("delegator", user1, account)) == 0

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegation.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_delegate) == 0
    assert len(actual.list_of_delegator) == 0

    # 3. Test list of delegate only
    access_delegation, _ = AccessDelegation.objects.get_or_create(
        account=account, delegate=user1, delegator=user2
    )

    assert len(delegation_list("delegate", user1, account)) == 1
    assert len(delegation_list("delegator", user1, account)) == 0

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegation.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_delegate) == 1
    assert len(actual.list_of_delegator) == 0

    # 4. Test list of delegator only
    access_delegation.delegate = user2
    access_delegation.delegator = user1
    access_delegation.save()

    assert len(delegation_list("delegate", user1, account)) == 0
    assert len(delegation_list("delegator", user1, account)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegation.model_validate_json(result.content)

    assert result.status_code == 200
    assert len(actual.list_of_delegate) == 0
    assert len(actual.list_of_delegator) == 1

    # 5. Test list containing both delegator and delegate
    access_delegation2, _ = AccessDelegation.objects.get_or_create(
        account=account, delegate=user1, delegator=user3
    )

    assert len(delegation_list("delegate", user1, account)) == 1
    assert len(delegation_list("delegator", user1, account)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegation.model_validate_json(result.content)

    assert result.status_code == 200
    assert len(actual.list_of_delegate) == 1
    assert len(actual.list_of_delegator) == 1

    # 5 Test the list returned is sorted
    access_delegation3, _ = AccessDelegation.objects.get_or_create(
        account=account, delegate=user1, delegator=user2
    )
    access_delegation4, _ = AccessDelegation.objects.get_or_create(
        account=account, delegate=user3, delegator=user1
    )

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegation.model_validate_json(result.content)

    assert result.status_code == 200
    assert len(actual.list_of_delegate) == 2
    assert len(actual.list_of_delegator) == 2

    assert actual.list_of_delegator[0].user.username == "<EMAIL>"
    assert actual.list_of_delegator[1].user.username == "<EMAIL>"
    assert actual.list_of_delegate[0].user.username == "<EMAIL>"
    assert actual.list_of_delegate[1].user.username == "<EMAIL>"


def test_get_list_access_delegation_accounts_emtpy_account(db, testuser1_client):
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = True
    account.save()

    User = get_user_model()
    user1, created = User.objects.get_or_create(username="<EMAIL>")
    assert not created

    search = "test"

    path = f"/api/access_delegation/delegation_accounts/{search}"

    # 2. Test with zero account list
    assert len(get_users_on_same_account(account, user1.username, search)) == 0

    result = testuser1_client.get(path, content_type="application/json")
    print(result.content)
    assert result.status_code == 200
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)

    assert len(actual.list_of_users_on_same_account) == 0


def test_get_list_access_delegation_accounts(db, testuser1_client):
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = False
    account.save()
    User = get_user_model()
    user1, _ = User.objects.get_or_create(username="<EMAIL>")
    user2, _ = User.objects.get_or_create(
        username="<EMAIL>", email="<EMAIL>"
    )

    assert check_enable_dossier_permission(account) is False

    search = "test"

    path = f"/api/access_delegation/delegation_accounts/{search}"

    # 1. Test with account that have no permission to access this feature
    result = testuser1_client.get(path, content_type="application/json")
    assert result.status_code == 403

    account.enable_dossier_permission = True
    account.save()

    # 2. Test with zero account list --> see test_get_list_access_delegation_accounts_emtpy_account

    # 3. Test search with first name
    search = "fit"
    path = f"/api/access_delegation/delegation_accounts/{search}"
    user2.first_name = "Fitsum"
    user2.last_name = "Alemu"
    user2.save()
    dossier_user, _ = DossierUser.objects.get_or_create(user=user2, account=account)

    assert len(get_users_on_same_account(account, user1.username, search)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 1

    # 4. Test search with last name
    search = "ale"
    path = f"/api/access_delegation/delegation_accounts/{search}"

    assert len(get_users_on_same_account(account, user1.username, search)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 1

    # 5. Test search with username, [ANTHING][SEARCHTERM]@domain.ch and
    search = "2"
    path = f"/api/access_delegation/delegation_accounts/{search}"

    assert len(get_users_on_same_account(account, user1.username, search)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 1

    # 5. Test search with username, [SEARCHTERM][ANTHINGELSE]@domain.ch
    search = "te"
    path = f"/api/access_delegation/delegation_accounts/{search}"

    assert len(get_users_on_same_account(account, user1.username, search)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 1

    # 6. Test search with username, [ANYTHING][SEARCHTERM][ANTHINGELSE]@domain.ch
    search = "es"
    path = f"/api/access_delegation/delegation_accounts/{search}"

    assert len(get_users_on_same_account(account, user1.username, search)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 1

    # 7. Test search with username, cas insensitive search
    search = "ES"
    path = f"/api/access_delegation/delegation_accounts/{search}"

    assert len(get_users_on_same_account(account, user1.username, search)) == 1

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 1

    # 8. Test search with username with @, expect 0 result
    search = "@"
    path = f"/api/access_delegation/delegation_accounts/{search}"

    assert len(get_users_on_same_account(account, user1.username, search)) == 0

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 0

    # 9. Test search with username with after @, expect 0 result
    search = "dossier"
    path = f"/api/access_delegation/delegation_accounts/{search}"

    assert len(get_users_on_same_account(account, user1.username, search)) == 0

    result = testuser1_client.get(path, content_type="application/json")
    actual = schemas.AccessDelegationAccounts.model_validate_json(result.content)
    assert result.status_code == 200
    assert len(actual.list_of_users_on_same_account) == 0


def test_add_access_delegation(db, testuser1_client):
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = False
    account.save()
    User = get_user_model()
    user1, _ = User.objects.get_or_create(username="<EMAIL>")
    user2, _ = User.objects.get_or_create(username="<EMAIL>")
    user3, _ = User.objects.get_or_create(username="<EMAIL>")

    assert check_enable_dossier_permission(account) is False

    data_1 = schemas.AccessDelegationNew(
        expiry_date=timezone.now(),
        delegate_username="<EMAIL>",
        delegator_username="<EMAIL>",
    ).model_dump_json()

    path = "/api/access_delegation/"

    # 1. Test with account that have no permission to access this feature
    result = testuser1_client.post(path, data_1, content_type="application/json")
    assert result.status_code == 403

    account.enable_dossier_permission = True
    account.save()

    assert check_enable_dossier_permission(account) is True

    data_2 = schemas.AccessDelegationNew(
        expiry_date=timezone.now(),
        delegate_username="nonexistinguser",
        delegator_username="<EMAIL>",
    ).model_dump_json()

    # 2. Test with delegate username that does not exist
    result = testuser1_client.post(path, data_2, content_type="application/json")
    assert result.status_code == 404

    schemas.AccessDelegationNew(
        expiry_date=timezone.now(),
        delegate_username="<EMAIL>",
        delegator_username="nonexistinguser",
    ).model_dump_json()

    # 3. Test with delegator username that does not exist
    # this case is not relevant anymore because the api uses always the delegator as request.auth.user

    # 4. Test with the same delegate and delegator username
    result = testuser1_client.post(path, data_1, content_type="application/json")
    assert result.status_code == 400

    # 5 Test both  account does not exist
    data_4 = schemas.AccessDelegationNew(
        expiry_date=timezone.now(),
        delegate_username="<EMAIL>",
        delegator_username="<EMAIL>",
    ).model_dump_json()

    result = testuser1_client.post(path, data_4, content_type="application/json")
    assert result.status_code == 400

    # 6 Test delegate account does not exist
    dossier_user, _ = DossierUser.objects.get_or_create(user=user3, account=account)

    result = testuser1_client.post(path, data_4, content_type="application/json")
    assert result.status_code == 400

    # 7 Test delegator account does not exist
    # this case is not relevant anymore because the api uses always the delegator as request.auth.user
    data_5 = schemas.AccessDelegationNew(
        expiry_date=timezone.now(),
        delegate_username="<EMAIL>",
        delegator_username="<EMAIL>",
    ).model_dump_json()

    result = testuser1_client.post(path, data_5, content_type="application/json")
    assert result.status_code == 200

    # 8 Test existing Access Delegation
    dossier_user, _ = DossierUser.objects.get_or_create(user=user2, account=account)
    access_delegation, _ = AccessDelegation.objects.get_or_create(
        account=account, delegate=user3, delegator=user2
    )

    assert access_delegation.expire_time is None

    assert check_delegation_exist(account, user2, user3) is True

    data_5 = schemas.AccessDelegationNew(
        expiry_date=timezone.now(),
        delegate_username="<EMAIL>",
        delegator_username="<EMAIL>",
    ).model_dump_json()

    result = testuser1_client.post(path, data_5, content_type="application/json")
    assert result.status_code == 200

    # 9 Test new  Access Delegation
    assert check_delegation_exist(account, user2, user1) is False

    data_6 = schemas.AccessDelegationNew(
        expiry_date=timezone.now(),
        delegate_username="<EMAIL>",
        delegator_username="<EMAIL>",
    ).model_dump_json()

    result = testuser1_client.post(path, data_6, content_type="application/json")
    assert result.status_code == 200


def test_delete_access_delegation(db, testuser1_client):
    account = Account.objects.get(key="default")
    account.enable_dossier_permission = False
    account.save()
    User = get_user_model()
    user, _ = User.objects.get_or_create(username="<EMAIL>")
    access_delegation, _ = AccessDelegation.objects.get_or_create(
        account=account, delegate=user, delegator=user, expire_time=timezone.now()
    )
    assert check_enable_dossier_permission(account) is False

    path = f"/api/access_delegation/{access_delegation.uuid}"

    # 1. Test with account that have no permission to access this feature
    result = testuser1_client.delete(path, content_type="application/json")
    assert result.status_code == 403

    account = Account.objects.get(key="default")
    account.enable_dossier_permission = True
    account.save()
    access_delegation = AccessDelegation.objects.get(uuid=access_delegation.uuid)

    assert check_enable_dossier_permission(account) is True

    path = f"/api/access_delegation/{access_delegation.uuid}"

    # 2. Test with account that have permission to access this feature
    result = testuser1_client.delete(path, content_type="application/json")
    assert result.status_code == 204

    path = f"/api/access_delegation/{uuid4()}"

    # 3. Test with non-existing access delegation
    result = testuser1_client.delete(path, content_type="application/json")
    assert result.status_code == 404


def test_export_with_access_delegation(db, mocked_get_dossier, testuser1_client):
    account, t1, t2, dossier = prepare_test_setup_with_two_users()
    response = testuser1_client.get(
        path=reverse("api:dossier-export", kwargs={"dossier_uuid": dossier.uuid})
    )

    assert response.status_code == HTTPStatus.SEE_OTHER

    dossier_export = DossierExport.objects.get(dossier=dossier)

    assert dossier_export.done

    parsed = schemas.Redirect.model_validate_json(response.content)

    assert parsed.location == dossier_export.file.fast_url


def test_change_dossier_properties(db, testuser1_client, testuser2_client):
    account, t1, t2, dossier = prepare_test_setup_with_two_users()
    path = f"/api/dossier/{dossier.uuid}/change_dossier_properties"

    data = SemanticDossierName(
        name=dossier.name,
        owner_username=dossier.owner.username,
        expiry_date=timezone.now() + timedelta(days=365),
    ).model_dump_json()

    # Owner is allowed to change
    r1 = testuser1_client.patch(path, data=data, content_type="application/json")
    assert r1.status_code == HTTPStatus.OK.value

    # Other user is not allowed to change
    r2 = testuser2_client.patch(path, data=data, content_type="application/json")
    assert r2.status_code == HTTPStatus.NOT_FOUND.value

    # Other user with access delegation is allowed to change
    AccessDelegation.objects.create(
        account=account,
        delegator=t1,
        delegate=t2,
        expire_time=timezone.now() + timedelta(days=1),
    )
    r3 = testuser2_client.patch(path, data=data, content_type="application/json")
    assert r3.status_code == HTTPStatus.OK.value


def get_page_object_from_doc_in_semantic_dossier(
    semantic_dossier: SemanticDossierSimple, doc_title: str, po_key: str
):
    po = None
    sem_page = None
    for doc in semantic_dossier.semantic_documents:
        if doc.filename == doc_title:
            sem_page = doc.semantic_pages[0]
            for po in sem_page.page_objects:
                if po.key == po_key:
                    break
        else:
            continue

    return doc, sem_page, po


@patch("dossier.api.ENABLE_PAGE_OBJECTS_IN_DATA_V2", True)
def test_update_page_object_confidence(db, testuser1_client, testuser2_client):
    # If page objects are not delivered anymore this needs refactoring as get_page_object_from_doc_in_semantic_dossier
    # will not have a page object returned
    # TODO: strange that UpdatePageObjectConfidenceRequest.confidence_value is int but in dossier/helpers.py, update_page_object_confidence() line 744
    # it looks like it should be a float in the invervall 0..1.... fix this test for other update values than 0.
    account, t1, t2, dossier = prepare_test_setup_with_two_users()

    result = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
    )
    assert result.status_code == 200

    semantic_dossier: SemanticDossierSimple = SemanticDossierSimple.model_validate_json(
        result.content
    )
    assert semantic_dossier
    doc, sem_page, po = get_page_object_from_doc_in_semantic_dossier(
        semantic_dossier,
        "240 Betreibungsauskunft Thiemann Manuel Antonius Keine Betreibungen 2014-04-11.pdf",
        "addressline_fullname",
    )

    path = f"/api/dossier/{dossier.uuid}/{sem_page.uuid}/{po.uuid}/update_confidence"

    data = UpdatePageObjectConfidenceRequest(confidence_value=0).model_dump_json()

    # Owner is allowed to change

    r1 = testuser1_client.patch(path, data=data, content_type="application/json")
    assert r1.status_code == HTTPStatus.OK.value

    # Other user is not allowed to change
    r2 = testuser2_client.patch(path, data=data, content_type="application/json")
    assert r2.status_code == HTTPStatus.NOT_FOUND.value

    # Other user with access delegation is allowed to change
    AccessDelegation.objects.create(
        account=account,
        delegator=t1,
        delegate=t2,
        expire_time=timezone.now() + timedelta(days=1),
    )
    r3 = testuser2_client.patch(path, data=data, content_type="application/json")
    assert r3.status_code == HTTPStatus.OK.value


def test_dossier_apis_with_access_delegation(db, testuser1_client, testuser2_client):
    account, t1, t2, dossier = prepare_test_setup_with_two_users()

    paths = [
        (f"/api/dossier/{dossier.uuid}/next-possible-states", None),
        (f"/api/dossier/{dossier.uuid}/default-document-categories", None),
        (f"/api/dossier/{dossier.uuid}/available-document-categories", None),
        # List[schemas.DocumentCategoryTranslated]),
        (f"/api/dossier/{dossier.uuid}/data_v2", schemas.SemanticDossierSimple),
        ###(f"/api/dossier/{dossier.uuid}/original_file_status", schemas.SemanticDossierSimple),
        (f"/api/dossier/{dossier.uuid}", schemas.Dossier),
        # ## todo check_update
        (f"/api/dossier/{dossier.uuid}", schemas.Dossier),
    ]

    for list_paths in paths:
        path = list_paths[0]
        response_schema = list_paths[1]
        validate_access_delegation_handling(
            account,
            dossier,
            t1,
            t2,
            testuser1_client,
            testuser2_client,
            path,
            response_schema,
        )


def test_export_page_object_images_without_rabbitmq(
    db, testuser1_client, testuser3__default_client, mocker: MockerFixture
):
    client = testuser3__default_client
    account = Account.objects.get(key="default")
    dossier2 = Dossier.objects.get(name="sales pitch mix with errors dossier")
    dossier2.account = account
    User = get_user_model()
    dossier2.owner, _ = User.objects.get_or_create(username="<EMAIL>")
    dossier2.save()

    # Ensure the token is valid and properly set in the AuthenticatedClient
    assert client.token is not None

    dossier = prepare_data("Dossier von Francesca Eichenberger")

    data_1 = dossier_zipper_schemas.ExportPageImageRequestBody(
        export_photos=True,
        export_plans=True,
        export_misc_images=True,
        export_identity_documents=True,
        skip_small_images=True,
    ).model_dump_json()

    # 1.  With Invalid token, should return Unauthorized response
    client_with_wrong_token = testuser1_client
    client_with_wrong_token.token = "Dummy Token"

    response = client_with_wrong_token.post(
        reverse("api:export-page-object-images", kwargs={"dossier_uuid": dossier.uuid}),
        data=data_1,
        content_type="application/json",
    )

    assert response.status_code == 401

    # 2. dossier not found, while checking permission access

    response = client.post(
        reverse("api:export-page-object-images", kwargs={"dossier_uuid": uuid4()}),
        data=data_1,
        content_type="application/json",
    )

    assert response.status_code == 404

    # 3. dossier with no access permission

    response = client.post(
        reverse(
            "api:export-page-object-images", kwargs={"dossier_uuid": dossier2.uuid}
        ),
        data=data_1,
        content_type="application/json",
    )

    assert response.status_code == 404

    # 4.  All conditions met , successful response

    # mock rabbit mq call
    mocker.patch(
        "image_exporter.helpers.call_rabbit",
        return_value=image_exporter_schemas.ImageExportResponseV1(
            http_status_code=200,
            message="success",
            payload=image_exporter_schemas.ImageExportResponsePayload(
                count_page_objects=10
            ),
        ),
    )

    #  Set the account and the owner
    dossier.account = account
    dossier.owner, _ = User.objects.get_or_create(username="<EMAIL>")
    dossier.save()

    response = client.post(
        reverse("api:export-page-object-images", kwargs={"dossier_uuid": dossier.uuid}),
        data=data_1,
        content_type="application/json",
    )

    assert response.status_code == 200


def prepare_test_setup_with_two_users():
    t1, _ = get_user_model().objects.get_or_create(username="<EMAIL>")
    t2, _ = get_user_model().objects.get_or_create(username="<EMAIL>")
    assert t1
    assert t2
    dossier = Dossier.objects.filter(owner=t1).first()
    assert dossier
    account = dossier.account
    assert dossier.account

    AccessDelegation.objects.filter(account=account, delegator=t1, delegate=t2).delete()

    return account, t1, t2, dossier


def validate_access_delegation_handling(
    account, dossier, t1, t2, testuser1_client, testuser2_client, path, response_schema
):
    # Delete all access delegation between t1 amd t2 that might exist from previous tests
    AccessDelegation.objects.filter(account=account, delegator=t1, delegate=t2).delete()

    r1 = testuser1_client.get(path, content_type="application/json")
    assert r1.status_code == 200
    if response_schema:
        d1 = response_schema.model_validate_json(r1.content)
        # depending on schema this is str or uuid
        assert str(d1.uuid) == str(dossier.uuid)
    r2 = testuser2_client.get(path, content_type="application/json")
    assert r2.status_code == 404
    ad = AccessDelegation.objects.create(
        account=account,
        delegator=t1,
        delegate=t2,
        expire_time=timezone.now() + timedelta(days=1),
    )
    r2 = testuser2_client.get(path, content_type="application/json")
    assert r2.status_code == 200
    # Test with expired access delegation
    ad.expire_time = timezone.now() + timedelta(days=-2)
    ad.save()
    r2 = testuser2_client.get(path, content_type="application/json")
    assert r2.status_code == 404


def remove_urls(dossier_simple):
    for processed_file in dossier_simple.processed_files.values():
        for page in processed_file.pages.values():
            page.image = ""


def prepare_data(dossier_name: str):
    print(f"Now prepare data for dossier '{dossier_name}'")
    dossier = Dossier.objects.get(name=dossier_name)
    return dossier


def test_check_dossier_access_no_dossier_access_grant_model(db, testuser1_client):
    # Test access check where there is no DossierAccessGrant model set
    result = testuser1_client.post(
        "/api/dossier/",
        data=schemas.CreateDossier(name="Test Dossier", lang="fr").model_dump_json(),
        content_type="application/json",
    )
    dossier_uuid = schemas.CreatedObjectReference.model_validate_json(
        result.content
    ).uuid

    response = testuser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier_uuid})
    )

    assert response.status_code == 200
    grant = schemas.DossierAccessGrant.model_validate_json(response.content)
    assert grant.dossier_uuid == dossier_uuid
    assert grant.requested <= grant.issued
    # grant is at least valid for 10mins
    assert grant.issued + timedelta(minutes=9) <= grant.expires
    assert grant.has_access is True


@pytest.mark.django_db
def test_check_dossier_access_valid_grant(db, testuser1_client):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    user = User.objects.get(username="<EMAIL>")

    grant_in_db, created = create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timedelta(minutes=30),
        has_access=True,
    )
    assert created

    response = testuser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier.uuid})
    )

    assert response.status_code == 200
    grant = schemas.DossierAccessGrant.model_validate_json(response.content)
    assert grant.dossier_uuid == dossier.uuid
    assert grant.has_access is True

    assert grant_in_db != grant

    # CAUTION: the grant in the db is not used at all because the default account has
    # no access check provider configured. So the grant returned is the default 9 minute grant
    assert grant_in_db.expires_at > grant.expires


@pytest.mark.django_db
@pytest.mark.parametrize(
    ("is_owner", "status_code"),
    [
        (False, 401),
        (True, 200),  # Being a dossier owner overwrites the access grant
    ],
)
def test_check_dossier_access_expired_grant(
    db, testuser1_client, is_owner, status_code
):

    dap, _ = DossierAccessCheckProvider.objects.get_or_create(
        name=DAP_DEFAULT_ACCESS_GRANT_CHECK
    )

    default_account = Account.objects.get(key="default")
    default_account.dossier_access_check_provider = dap
    default_account.save()

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    if is_owner is False:
        dossier_user2 = get_user_or_create(
            account=default_account,
            username="<EMAIL>",
        )

        dossier.owner = dossier_user2.user
        dossier.save()

    user = User.objects.get(username="<EMAIL>")
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() - timedelta(minutes=1),
        has_access=True,
    )

    response = testuser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier.uuid})
    )

    assert response.status_code == status_code


@pytest.mark.django_db
@pytest.mark.parametrize(
    ("is_owner", "status_code"),
    [
        (False, 401),
        (True, 200),  # Being a dossier owner overwrites the access grant
    ],
)
def test_check_dossier_access_no_access_grant(
    db, testuser1_client, is_owner, status_code
):
    dap, _ = DossierAccessCheckProvider.objects.get_or_create(
        name=DAP_DEFAULT_ACCESS_GRANT_CHECK
    )

    default_account = Account.objects.get(key="default")
    default_account.dossier_access_check_provider = dap
    default_account.save()

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    if is_owner is False:
        dossier_user2 = get_user_or_create(
            account=default_account,
            username="<EMAIL>",
        )

        dossier.owner = dossier_user2.user
        dossier.save()

    user = User.objects.get(username="<EMAIL>")
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timedelta(minutes=30),
        has_access=False,
    )

    response = testuser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier.uuid})
    )

    assert response.status_code == status_code


def _assert_grant_response(testuser1_client, dossier, status_code: int):
    response = testuser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier.uuid})
    )
    if status_code == 200:
        assert response.status_code == 200
        grant = schemas.DossierAccessGrant.model_validate_json(response.content)
        assert grant.dossier_uuid == dossier.uuid
        assert grant.has_access is True

    assert response.status_code == status_code


@pytest.mark.django_db
@pytest.mark.parametrize(
    ("provider", "is_owner", "status_code"),
    [
        (None, False, 404),  # Not an owner, so no access
        (None, True, 200),
        (DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME, False, 200),  # Is this what we want?
        (DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME, True, 200),
        (DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME, False, 401),
        (
            DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME,
            True,
            200,
        ),  # Being a dossier owner overwrites the access grant
    ],
)
def test_check_dossier_access_default_providers(
    db, testuser1_client, provider, is_owner, status_code
):
    default_account = Account.objects.get(key="default")

    if provider:

        install_dossier_access_check_providers()

        dap, _ = DossierAccessCheckProvider.objects.get_or_create(name=provider)

        default_account.dossier_access_check_provider = dap
        default_account.save()

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    if is_owner is False:
        dossier_user2 = get_user_or_create(
            account=default_account,
            username="<EMAIL>",
        )

        dossier.owner = dossier_user2.user
        dossier.save()

    user = User.objects.get(username="<EMAIL>")

    _assert_grant_response(testuser1_client, dossier, status_code)

    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timedelta(minutes=30),
        has_access=False,
    )

    _assert_grant_response(testuser1_client, dossier, status_code)

    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timedelta(minutes=30),
        has_access=True,
    )

    _assert_grant_response(testuser1_client, dossier, status_code)

    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() - timedelta(minutes=30),
        has_access=True,
    )

    _assert_grant_response(testuser1_client, dossier, status_code)


@pytest.mark.django_db
def test_check_dossier_access_non_existent_dossier(db, testuser1_client):
    non_existent_uuid = uuid4()

    response = testuser1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": non_existent_uuid})
    )

    assert response.status_code == 404


@pytest.mark.django_db
def test_check_dossier_access_unauthorized(db, bekbe1_client):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    response = bekbe1_client.get(
        reverse("api:check-dossier-access", kwargs={"dossier_uuid": dossier.uuid})
    )

    assert response.status_code == 401


@pytest.mark.django_db
def test_get_semantic_document_basics(db, testuser1_client, django_assert_num_queries):
    """
    Test the `get_semantic_document_basics` function with date range parameters.

    Assertions:
    - The response status code is 200 (OK).
    - The response content matches the expected `SemanticDossiers` schema format.
    - The returned data includes the correct dossier UUID and details of the associated semantic documents.
    - Semantic documents are filtered based on the specified date range.
    """

    now = timezone.now()

    user = User.objects.get(username="<EMAIL>")

    account = Account.objects.get(key="default")

    dossier = create_dossier(
        account=account,
        dossier_name="Test Dossier",
        language="de",
        owner=user,
        external_id="test-external",
    )
    dossier.save()

    document_category = DocumentCategory.objects.get(name="PLAN_FLOOR", account=account)

    semantic_doc = SemanticDocument.objects.create(
        dossier=dossier,
        title_custom="title_custom",
        confidence_level=ConfidenceLevel.HIGH,
        confidence_formatted="100%",
        confidence_value=100,
        title_suffix="suffix",
        document_category=document_category,
        work_status=get_start_work_status_from_dossier_account(dossier=dossier),
    )

    assert semantic_doc.deleted_at is None

    deleted_semantic_doc = SemanticDocument.objects.create(
        dossier=dossier,
        title_custom="title_custom",
        confidence_level=ConfidenceLevel.HIGH,
        confidence_formatted="100%",
        confidence_value=100,
        title_suffix="suffix",
        document_category=document_category,
        work_status=get_start_work_status_from_dossier_account(dossier=dossier),
    )

    deleted_semantic_doc.delete()

    deleted_semantic_doc.refresh_from_db()

    assert deleted_semantic_doc.deleted_at

    result = testuser1_client.get(
        reverse("api:semantic_document_basics", kwargs={"dossier_uuid": dossier.uuid}),
        content_type="application/json",
    )

    assert result.status_code == 200

    actual = schemas.SemanticDocumentBasicsList.model_validate_json(result.content)

    assert actual.dossier_uuid == dossier.uuid
    assert (
        actual.semantic_documents[0].document_category__name
        == semantic_doc.document_category.name
    )
    assert actual.semantic_documents == [
        SemanticDocumentBasics(
            **{
                "uuid": semantic_doc.uuid,
                "title_custom": semantic_doc.title_custom,
                "document_category__name": "PLAN_FLOOR",
                "confidence_level": ConfidenceLevel.HIGH,
                "confidence_formatted": "100%",
                "confidence_value": 100,
                "title_suffix": "suffix",
                "last_page_change_date": None,
                "last_entity_change_date": None,
                "access_mode": AccessMode.READ_WRITE,
                "external_semantic_document_id": None,
            }
        )
    ]

    # Scenario: Invalid date range (from_date > to_date)
    updated_from_date = now
    updated_to_date = now - timedelta(days=1)

    result = testuser1_client.get(
        reverse("api:semantic_document_basics", kwargs={"dossier_uuid": dossier.uuid}),
        content_type="application/json",
        data={
            "updated_from_date": updated_from_date,
            "updated_to_date": updated_to_date,
        },
    )

    assert result.status_code == 400
    assert result.json() == {"detail": "Invalid date range"}

    # Successful scenario with date range filters
    with freeze_time(now - timedelta(days=1)):
        semantic_doc.save()

        updated_from_date = now - timedelta(days=1)
        updated_to_date = now

        result = testuser1_client.get(
            reverse(
                "api:semantic_document_basics", kwargs={"dossier_uuid": dossier.uuid}
            ),
            content_type="application/json",
            data={
                "updated_from_date": updated_from_date,
                "updated_to_date": updated_to_date,
            },
        )

        assert result.status_code == 200

        actual = schemas.SemanticDocumentBasicsList.model_validate_json(result.content)

        assert actual.dossier_uuid == dossier.uuid
        assert (
            len(actual.semantic_documents) == 1
        )  # Only one document should match the date range
        assert (
            actual.semantic_documents[0].document_category__name
            == semantic_doc.document_category.name
        )
        assert actual.semantic_documents[0].uuid == semantic_doc.uuid

    # Scenario: Only from_date is provided
    updated_from_date = now - timedelta(days=5)

    result = testuser1_client.get(
        reverse("api:semantic_document_basics", kwargs={"dossier_uuid": dossier.uuid}),
        content_type="application/json",
        data={"updated_from_date": updated_from_date},
    )

    assert result.status_code == 200

    actual = schemas.SemanticDocumentBasicsList.model_validate_json(result.content)

    assert actual.dossier_uuid == dossier.uuid
    assert (
        len(actual.semantic_documents) == 1
    )  # Only one document should match the date range

    # Scenario: Only to_date is provided
    updated_to_date = now - timedelta(days=1)

    result = testuser1_client.get(
        reverse("api:semantic_document_basics", kwargs={"dossier_uuid": dossier.uuid}),
        content_type="application/json",
        data={"updated_to_date": updated_to_date},
    )

    assert result.status_code == 200

    actual = schemas.SemanticDocumentBasicsList.model_validate_json(result.content)

    assert actual.dossier_uuid == dossier.uuid
    assert len(actual.semantic_documents) == 1  #

    # Filter that result with zero semantic document
    with freeze_time(now - timedelta(days=1)):
        semantic_doc.save()

        updated_from_date = now - timedelta(days=10)
        updated_to_date = now - timedelta(days=9)

        result = testuser1_client.get(
            reverse(
                "api:semantic_document_basics", kwargs={"dossier_uuid": dossier.uuid}
            ),
            content_type="application/json",
            data={
                "updated_from_date": updated_from_date,
                "updated_to_date": updated_to_date,
            },
        )

        assert result.status_code == 200

        actual = schemas.SemanticDocumentBasicsList.model_validate_json(result.content)

        assert actual.dossier_uuid == dossier.uuid
        assert len(actual.semantic_documents) == 0


def test_get_dossier_assignable_users(db, testuser1_client):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
    )

    test_user = User.objects.get(username="<EMAIL>")

    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 1
    assert (
        assignable_users.assignable_users_list[0].account_user_uuid
        == DossierUser.objects.get(
            user__username="<EMAIL>", account__key="default"
        ).uuid
    )
    assert assignable_users.assignable_users_list[0].firstname == test_user.first_name
    assert assignable_users.assignable_users_list[0].lastname == test_user.last_name

    # testing for firstname and lastname filter
    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
        + "?query=testuser testuser",
    )
    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 1

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
        + "?query=testuser",
    )
    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 1

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
        + "?query=<EMAIL>",
    )
    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 1

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
        + "?query=negative",
    )
    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 0

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
        + "?query=testuser2",
    )
    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 0


def test_get_dossier_assignable_users_is_pers(db, testuser1_client, test_user_2):
    # Test specifically bebke case, where if dossier has pers property in BEKBDossierProperties, only return users which
    # have group pers.

    # Default test case, dossier does not have pers property
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
    )

    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 2

    assert (
        assignable_users.assignable_users_list[0].account_user_uuid
        == DossierUser.objects.get(
            user__username="<EMAIL>", account__key="default"
        ).uuid
    )

    assert not BEKBDossierProperties.objects.filter(dossier=dossier).exists()

    # Add a BEKBDossierProperties, with pers = false, expect no change in behaviour
    bekb_property = BEKBDossierProperties.objects.create(
        account=dossier.account,
        dossier=dossier,
        business_partner=Partner.objects.create(
            name="testpartner", account=dossier.account
        ),
        pers=False,
    )

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
    )

    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 2

    # Update to pers = true, expect only users with group pers
    bekb_property.pers = True
    bekb_property.save()

    # Expect 404 as <EMAIL> is not in pers group
    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
    )

    assert result.status_code == 404

    set_pers(
        dossier.account,
        DossierUser.objects.get(
            user__username="<EMAIL>", account__key="default"
        ).user,
        True,
    )

    # Expect return current user
    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
    )

    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 1

    assert (
        assignable_users.assignable_users_list[0].account_user_uuid
        == DossierUser.objects.get(
            user__username="<EMAIL>", account__key="default"
        ).uuid
    )

    # Add a second user to pers group

    set_pers(dossier.account, user=test_user_2.user, pers=True)

    result = testuser1_client.get(
        reverse("api:dossier-assignable-users", kwargs={"dossier_uuid": dossier.uuid})
    )

    assignable_users = schemas.DossierAssignableUsersSchema.model_validate_json(
        result.content
    )

    assert len(assignable_users.assignable_users_list) == 2


def test_get_dossier_aggregate_page_objects_sample_dossier(
    db, testuser1_client, django_assert_max_num_queries
):

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page__dossier=dossier
    ).count()

    assert page_object_count > 0

    with django_assert_max_num_queries(19):

        response = testuser1_client.get(
            reverse(
                "api:dossier-aggregate-page-objects",
                kwargs={"dossier_uuid": dossier.uuid},
            )
        )

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    assert page_object_count > len(parsed)

    assert len(parsed) == 200


def test_get_dossier_all_page_objects_sample_dossier(
    db, testuser1_client, django_assert_max_num_queries
):

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page__dossier=dossier
    ).count()

    assert page_object_count > 0

    with django_assert_max_num_queries(17):

        response = testuser1_client.get(
            reverse(
                "api:dossier-all-page-objects", kwargs={"dossier_uuid": dossier.uuid}
            )
        )

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    assert len(parsed) == page_object_count


def test_get_dossier_account_available_roles(db, testuser1_client):

    result = testuser1_client.get(reverse("api:account-available-roles"))

    available_roles = schemas.DossierAvailableRolesSchema.model_validate_json(
        result.content
    ).available_dossier_roles
    assert len(available_roles) == 1

    assigne_role = available_roles[0]
    assert assigne_role.key == "ASSIGNEE"


def test_get_dossier_account_role_users(db, testuser1_client):
    account = Account.objects.get(key="default")
    roles = DossierRole.objects.filter(account=account, show_separate_filter=True)
    assert len(roles) == 1
    assignee_role = roles[0]
    result = testuser1_client.get(
        reverse(
            "api:account-available-role-users", kwargs={"role_uuid": assignee_role.uuid}
        )
    )
    test_user = User.objects.get(username="<EMAIL>")

    filter_users = schemas.DossierRoleFilterUsersSchema.model_validate_json(
        result.content
    ).filter_users
    assert len(filter_users) == 1

    filter_user = filter_users[0]

    assert filter_user.username == test_user.username
    assert filter_user.first_name == test_user.first_name
    assert filter_user.last_name == test_user.last_name


def test_get_dossier_semantic_pages_sample_dossier(
    db, testuser1_client, django_assert_max_num_queries
):

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    semantic_page_count = dossier.semantic_pages.all().count()

    assert semantic_page_count > 0

    with django_assert_max_num_queries(17):

        response = testuser1_client.get(
            reverse("api:dossier-semantic-pages", kwargs={"dossier_uuid": dossier.uuid})
        )

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.SemanticPageNoPageObjects]).validate_json(
        response.content
    )

    assert len(parsed) == semantic_page_count


@pytest.mark.django_db
def test_data_v2_api_returns_empty_annotations(testuser1_client):
    """
    Test that the data_v2 API returns empty annotations.
    We don't want to serialize annotations as part of data_v2, as its an expensive api call
    We have a seperate API for annotations
    """
    # Get an existing dossier
    dossier = Dossier.objects.get(
        name="sales pitch mix with errors dossier", account__key="default"
    )

    # Add some test annotations to verify they are not serialized
    semantic_page_obj = dossier.semantic_pages.first()
    annotation_group_uuid = uuid.uuid4()

    # Create highlight annotations
    SemanticPageUserAnnotations.objects.create(
        semantic_page=semantic_page_obj,
        annotation_type="highlight",
        annotation_group_uuid=annotation_group_uuid,
        text="Test highlight",
        bbox_left=0.1,
        bbox_top=0.2,
        bbox_width=0.3,
        bbox_height=0.15,
        hexcolor="#FFFF00",
    )

    # Create comment annotations
    SemanticPageUserAnnotations.objects.create(
        semantic_page=semantic_page_obj,
        annotation_type="comment",
        annotation_group_uuid=uuid.uuid4(),  # Different group for comment
        text="Test comment",
        bbox_left=0.4,
        bbox_top=0.5,
        bbox_width=0.2,
        bbox_height=0.1,
        hexcolor="#00FF00",
    )

    # Verify annotations were created
    assert (
        SemanticPageUserAnnotations.objects.filter(
            semantic_page=semantic_page_obj
        ).count()
        == 2
    )

    # Call the data_v2 API
    response = testuser1_client.get(
        f"/api/dossier/{dossier.uuid}/data_v2", content_type="application/json"
    )

    # Verify the response status
    assert response.status_code == 200

    # Parse the response
    dossier_data = schemas.SemanticDossierSimple.model_validate_json(response.content)

    # Ensure there are semantic documents in the test data
    assert len(dossier_data.semantic_documents) > 0

    # Check that all semantic pages have empty annotations
    for semantic_document in dossier_data.semantic_documents:
        for semantic_page in semantic_document.semantic_pages:
            assert (
                semantic_page.annotations is None or len(semantic_page.annotations) == 0
            )

    # Also check page objects which can have annotations
    for semantic_document in dossier_data.semantic_documents:
        for semantic_page in semantic_document.semantic_pages:
            if hasattr(semantic_page, "page_objects") and semantic_page.page_objects:
                for page_object in semantic_page.page_objects:
                    if hasattr(page_object, "annotations"):
                        assert (
                            page_object.annotations is None
                            or len(page_object.annotations) == 0
                        )

    # Verify that annotations are available through the dedicated annotations API
    response = testuser1_client.get(
        reverse(
            "api:semantic-page-user-annotations",
            kwargs={"semantic_page_uuid": semantic_page_obj.uuid},
        )
    )
    assert response.status_code == 200
    annotations = SemanticPageUserAnnotationsSchema.model_validate_json(
        response.content
    )
    assert (
        len(annotations.user_annotations) == 2
    )  # We should get both annotations we created
    # Verify we have both types of annotations
