import pytest
from django.contrib.auth.models import User
from django.urls import reverse_lazy

import dossier.schemas as dossier_schemas
from conftest import ACCOUNT_KEY_DOCCHECK, ACCOUNT_KEY_NO_DOCCHECK
from core.authentication import Authenticated<PERSON><PERSON>
from doccheck import schemas
from doccheck.models import BusinessCaseType as DocCheckBusinessCaseType

from doccheck.services import map_case_detail_out, add_person_to_case
from doccheck.tests.test_services import create_doc_check
from dossier.models import Account, BusinessCaseType, Dossier
from dossier.services import (
    create_dossier,
    add_case_to_dossiers_with_doccheck,
    get_or_create_case_to_dossier_if_active_doccheck,
)


@pytest.fixture
def simple_case(db):
    user = User.objects.get(username="<EMAIL>")
    doc_check = create_doc_check()
    account_with_doccheck = Account.objects.create(
        key=ACCOUNT_KEY_DOCCHECK, name="Test Account", active_doc_check=doc_check
    )
    account_without_doccheck = Account.objects.create(
        key=ACCOUNT_KEY_NO_DOCCHECK, name="Test Account without DocCheck"
    )
    BusinessCaseType.objects.all().delete()

    dossier_business_case_type = BusinessCaseType.objects.create(
        account=account_with_doccheck, key="test business case type"
    )
    assert BusinessCaseType.objects.count() == 1

    DocCheckBusinessCaseType.objects.create(
        doc_check=doc_check, key=dossier_business_case_type.key
    )
    assert DocCheckBusinessCaseType.objects.count() == 2

    Dossier.objects.create(
        account=account_without_doccheck,
        name="dossier without doccheck",
        lang="de",
        owner=user,
    )

    Dossier.objects.create(
        account=account_with_doccheck,
        name="dossier with missing case",
        lang="de",
        owner=user,
        businesscase_type_id=dossier_business_case_type.uuid,
    )

    dossier_with_case = create_dossier(
        account_with_doccheck,
        "dossier with case",
        "de",
        user,
        businesscase_type_id=dossier_business_case_type.uuid,
    )
    dossier_with_case.save()
    return dossier_with_case.doccheck_case


def test_add_cases(db, simple_case):
    dossiers_with_doccheck_and_no_case = Dossier.objects.filter(
        account__active_doc_check__isnull=False, doccheck_case__isnull=True
    )
    assert dossiers_with_doccheck_and_no_case.count() == 1

    d = Dossier.objects.get(doccheck_case=simple_case)

    add_case_to_dossiers_with_doccheck(d.account.key)
    assert (
        Dossier.objects.filter(
            account__active_doc_check__isnull=False, doccheck_case__isnull=True
        ).count()
        == 0
    )


def test_get_doccheck_case_from_dossier(
    db, testuser1_doc_client: AuthenticatedClient, simple_case
):
    # if no active_doccheck is linked via account, no case should be added
    dossier_without_doccheck = Dossier.objects.get(name="dossier without doccheck")
    assert dossier_without_doccheck.account.active_doc_check is None
    assert dossier_without_doccheck.doccheck_case is None
    _ = get_or_create_case_to_dossier_if_active_doccheck(dossier_without_doccheck)
    assert dossier_without_doccheck.doccheck_case is None

    # if active_doccheck is linked but no case present, a case should be added
    dossier_with_doccheck_but_no_case = Dossier.objects.get(
        name="dossier with missing case"
    )
    assert dossier_with_doccheck_but_no_case.account.active_doc_check is not None
    assert dossier_with_doccheck_but_no_case.doccheck_case is None
    _ = get_or_create_case_to_dossier_if_active_doccheck(
        dossier_with_doccheck_but_no_case
    )
    assert dossier_with_doccheck_but_no_case.doccheck_case is not None

    # if a case is already present, it should simply be returned
    dossier_with_case = Dossier.objects.get(name="dossier with case")
    assert dossier_with_case.account.active_doc_check is not None
    assert dossier_with_case.doccheck_case is not None
    returned_case = get_or_create_case_to_dossier_if_active_doccheck(dossier_with_case)
    assert returned_case == dossier_with_case.doccheck_case

    # test API
    dossier = Dossier.objects.get(doccheck_case=simple_case)
    res = testuser1_doc_client.get(f"/api/dossier/{dossier.uuid}")
    res_dossier = dossier_schemas.Dossier.model_validate_json(res.content)
    assert res_dossier.doccheck_case_id == simple_case.uuid


def test_get_case(db, testuser1_doc_client: AuthenticatedClient, simple_case):
    res = testuser1_doc_client.get(
        reverse_lazy("api:case", kwargs=dict(case_uuid=simple_case.uuid))
    )
    res_case = schemas.CaseDetailOut.model_validate_json(res.content)
    assert res_case == map_case_detail_out(simple_case)
    # the case is created by providing the uuid of the dossier_business_case_type but needs to link to the
    # doccheck_business_case_type
    doccheck_business_case_type = DocCheckBusinessCaseType.objects.get(
        doc_check=simple_case.doc_check, key="test business case type"
    )
    assert simple_case.business_case_type.uuid == doccheck_business_case_type.uuid


def test_get_case_no_access(db, testuser2_doc_client: AuthenticatedClient, simple_case):
    res = testuser2_doc_client.get(
        reverse_lazy("api:case", kwargs=dict(case_uuid=simple_case.uuid))
    )
    assert res.status_code == 404


def test_update_case(db, testuser1_doc_client: AuthenticatedClient, simple_case):
    res = testuser1_doc_client.put(
        reverse_lazy("api:case", kwargs=dict(case_uuid=simple_case.uuid)),
        data=schemas.CaseUpdate(business_case_type="UNKNOWN").model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 201


def test_update_case_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case
):
    res = testuser2_doc_client.put(
        reverse_lazy("api:case", kwargs=dict(case_uuid=simple_case.uuid)),
        data=schemas.CaseUpdate(business_case_type="UNKNOWN").model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 404


def test_update_person(db, testuser1_doc_client: AuthenticatedClient, simple_case):
    res = testuser1_doc_client.put(
        reverse_lazy(
            "api:case_person",
            kwargs=dict(
                case_uuid=simple_case.uuid,
                person_uuid=simple_case.person_set.first().uuid,
            ),
        ),
        data=schemas.PersonUpdate().model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 201


def test_update_person_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case
):
    res = testuser2_doc_client.put(
        reverse_lazy(
            "api:case_person",
            kwargs=dict(
                case_uuid=simple_case.uuid,
                person_uuid=simple_case.person_set.first().uuid,
            ),
        ),
        data=schemas.PersonUpdate().model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 404


def test_add_person(db, testuser1_doc_client: AuthenticatedClient, simple_case):
    res = testuser1_doc_client.post(
        reverse_lazy("api:case_persons", kwargs=dict(case_uuid=simple_case.uuid))
    )
    assert res.status_code == 201


def test_add_person_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case
):
    res = testuser2_doc_client.post(
        reverse_lazy("api:case_persons", kwargs=dict(case_uuid=simple_case.uuid))
    )
    assert res.status_code == 404


def test_delete_only_person(db, testuser1_doc_client: AuthenticatedClient, simple_case):
    person_count_initial = simple_case.person_set.count()
    assert person_count_initial == 1
    res = testuser1_doc_client.delete(
        reverse_lazy(
            "api:case_person",
            kwargs=dict(
                case_uuid=simple_case.uuid,
                person_uuid=simple_case.person_set.first().uuid,
            ),
        )
    )
    assert res.status_code == 403


def test_delete_person_allowed(
    db, testuser1_doc_client: AuthenticatedClient, simple_case
):
    person_to_delete = add_person_to_case(
        simple_case, person_to_create=schemas.PersonCreate()
    )
    person_count_initial = simple_case.person_set.count()
    assert person_count_initial > 1
    res = testuser1_doc_client.delete(
        reverse_lazy(
            "api:case_person",
            kwargs=dict(case_uuid=simple_case.uuid, person_uuid=person_to_delete.uuid),
        )
    )
    assert res.status_code == 204


def test_person_no_access(db, testuser2_doc_client: AuthenticatedClient, simple_case):
    res = testuser2_doc_client.delete(
        reverse_lazy(
            "api:case_person",
            kwargs=dict(
                case_uuid=simple_case.uuid,
                person_uuid=simple_case.person_set.first().uuid,
            ),
        )
    )
    assert res.status_code == 404


def test_update_real_estate_property(
    db, testuser1_doc_client: AuthenticatedClient, simple_case
):
    res = testuser1_doc_client.put(
        reverse_lazy(
            "api:case_property_uuid",
            kwargs=dict(
                case_uuid=simple_case.uuid,
                property_uuid=simple_case.realestateproperty_set.first().uuid,
            ),
        ),
        data=schemas.RealEstatePropertyUpdate().model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 201


def test_update_real_estate_property_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case
):
    res = testuser2_doc_client.put(
        reverse_lazy(
            "api:case_property_uuid",
            kwargs=dict(
                case_uuid=simple_case.uuid,
                property_uuid=simple_case.realestateproperty_set.first().uuid,
            ),
        ),
        data=schemas.RealEstatePropertyUpdate().model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 404


def test_get_fields(db, testuser1_doc_client: AuthenticatedClient, simple_case):
    res = testuser1_doc_client.get(
        reverse_lazy("api:case_fields", kwargs=dict(case_uuid=simple_case.uuid))
    )
    assert res.status_code == 200


def test_get_fields_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case
):
    res = testuser2_doc_client.get(
        reverse_lazy("api:case_fields", kwargs=dict(case_uuid=simple_case.uuid))
    )
    assert res.status_code == 404
