import pytest

from dossier.models import Dossier


@pytest.mark.django_db
def test_check_dossiers_exists_in_database():
    dosser_names = [dossier.name for dossier in Dossier.objects.all()]
    assert (
        dosser_names.sort()
        == [
            "sales pitch mix with errors dossier",
            "finhurdle samples",
            "image export samples",
            "Dossier <PERSON>",
            "Dossier von <PERSON>",
            "Dossier von <PERSON>",
        ].sort()
    )


@pytest.mark.django_db
def test_check_demo_dossier():
    assert Dossier.objects.filter(name="sales pitch mix with errors dossier").exists()
