from uuid import uuid4

import pytest
from django.core.files.base import ContentFile
from django.db.utils import IntegrityError

from dossier.helpers import get_hypodossier_exception_from_string
from dossier.models import (
    OriginalFile,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
    Dossier,
    DossierFile,
    Account,
    ExceptionDetails,
)


@pytest.fixture
def dossier():
    account = Account.objects.create(key="test", name="Test")
    dossier = Dossier.objects.create(name="Testdossier", account=account)
    return dossier


@pytest.fixture
def dossier_file(dossier):
    dossier_file = DossierFile.objects.create(
        uuid=uuid4(),
        dossier=dossier,
        bucket="dossier",
        data=ContentFile(b"", "my_filename.txt"),
    )
    return dossier_file


@pytest.fixture
def original_file(dossier, dossier_file):
    return OriginalFile.objects.create(dossier=dossier, file=dossier_file)


@pytest.fixture
def realestate_property(dossier):
    return RealestateProperty.objects.create(dossier=dossier, key="unique_key")


@pytest.mark.django_db
def test_map_exceptions_from_string():
    s = "PASSWORD_PROTECTED"
    ex = get_hypodossier_exception_from_string(s)
    assert ex == ExceptionDetails.HypoDossierException.PASSWORD_PROTECTED.value
    assert (
        get_hypodossier_exception_from_string("hohoho")
        == ExceptionDetails.HypoDossierException.UNMAPPED_EXCEPTION.value
    )
    assert (
        get_hypodossier_exception_from_string("UNKNOWN_EXCEPTION")
        == ExceptionDetails.HypoDossierException.UNKNOWN_EXCEPTION.value
    )
    assert (
        get_hypodossier_exception_from_string("")
        == ExceptionDetails.HypoDossierException.UNKNOWN_EXCEPTION.value
    )


@pytest.mark.django_db
def test_original_file_creation(dossier, dossier_file):
    original = OriginalFile.objects.create(dossier=dossier, file=dossier_file)
    assert original.dossier_file_uuid == dossier_file.uuid
    assert original.download_url == dossier_file.fast_url


@pytest.mark.django_db
def test_realestate_property_unique_constraint(dossier):
    RealestateProperty.objects.create(dossier=dossier, key="unique_key_1")
    with pytest.raises(IntegrityError):
        RealestateProperty.objects.create(dossier=dossier, key="unique_key_1")


@pytest.mark.django_db
def test_assigned_realestate_property_original_file_creation(
    original_file, realestate_property
):
    relationship = AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=original_file, realestate_property=realestate_property
    )
    assert relationship.originalfile == original_file
    assert relationship.realestate_property == realestate_property

    # Test its possible to lookup from the realestate property
    assert (
        RealestateProperty.objects.filter(
            assignedrealestatepropertyoriginalfile__originalfile=original_file
        ).first()
        == realestate_property
    )

    # Test its possible to do a reverse lookup from the original file
    assert (
        OriginalFile.objects.filter(
            assignedrealestatepropertyoriginalfile__realestate_property=realestate_property
        ).first()
        == original_file
    )
