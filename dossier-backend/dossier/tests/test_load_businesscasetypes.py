import structlog
from pathlib import Path

import pytest
from django.core.management import call_command

from bekb.fakes import PATH_CURRENT_BEKB_BUSINESSCASE_TYPES
from core.temporary_path import temporary_path
from dossier.models import BusinessCaseType
from dossier.services import (
    create_businesscase_type_export,
    create_businesscase_type_import,
)

logger = structlog.get_logger()


@pytest.fixture()
def load_dossier(django_db_blocker):
    BusinessCaseType.objects.all().delete()
    with django_db_blocker.unblock():
        call_command("loaddata", "dossier.json", app="dossier")


def test_dump_businesscase_types(db, load_dossier):
    account_key = "bekb"

    assert BusinessCaseType.objects.filter(account__key=account_key).count() == 4
    # to dump the test data reference, execute the following two lines
    # dest_path = Path(__file__).parent / f"data/businesscase_types.json"
    # create_businesscase_type_export(account_key, dest_path)
    with temporary_path() as path:
        dest_path = path / "dump.json"
        create_businesscase_type_export(account_key, dest_path)
        expected_file_path = Path(__file__).parent / "data/businesscase_types.json"
        assert expected_file_path.read_text() == dest_path.read_text()


def test_load_businesscase_types(db, load_dossier):
    BusinessCaseType.objects.all().delete()
    assert BusinessCaseType.objects.count() == 0
    businesscase_type_json_path = PATH_CURRENT_BEKB_BUSINESSCASE_TYPES

    account_key = "bekb"
    create_businesscase_type_import(businesscase_type_json_path, account_key)
    assert BusinessCaseType.objects.count() == 8
