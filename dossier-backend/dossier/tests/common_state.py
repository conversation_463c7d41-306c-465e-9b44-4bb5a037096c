from dossier.models import <PERSON>ssier, annotate_with_calculated_access_mode
from projectconfig.authentication import get_user_or_create


def assert_dossier_access_mode(
    dossier: <PERSON>ssier, expected_access_mode: Dossier.DossierAccessMode
):
    queryset = Dossier.objects.filter(uuid=dossier.uuid)
    account = dossier.account
    username = f"{account.key}<EMAIL>"
    user = get_user_or_create(
        account=account,
        username=username,
        email=username,
        fname=f"JC {account.key}",
        lname="<PERSON>",
    )

    user.is_staff = False
    user.save()
    assert (
        annotate_with_calculated_access_mode(queryset, user.user)
        .first()
        .calculated_access_mode
        == expected_access_mode
    )
