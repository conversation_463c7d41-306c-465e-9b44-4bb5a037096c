import pytest
from datetime import timedelta
from freezegun import freeze_time
from django.utils import timezone

from dossier.models import DossierCloseStrategy
from dossier.services_external import (
    DossierCloseReadyStats,
    evaluate_original_files_in_processing,
)

pytestmark = pytest.mark.django_db


@pytest.fixture
def base_stats():
    """Base stats fixture with default values."""
    return DossierCloseReadyStats(
        num_documents_all=10,
        num_documents_export_not_started=0,
        num_documents_in_export=0,
        num_documents_exported=10,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        allow_unknown_documents=False,
        original_file_processing_timestamp=None,
    )


def test_no_files_in_processing(base_stats):
    """Test when no files are in processing."""
    result = evaluate_original_files_in_processing(
        base_stats, DossierCloseStrategy.DEFAULT
    )
    assert result is None


@freeze_time("2023-01-01 12:00:00")
def test_files_in_processing_within_timeout(base_stats):
    """Test when files are being processed within the timeout period."""
    stats = base_stats
    stats.num_original_files_in_processing = 2
    stats.original_file_processing_timestamp = timezone.now() - timedelta(minutes=30)

    result = evaluate_original_files_in_processing(stats, DossierCloseStrategy.DEFAULT)

    assert result is not None
    assert result.ready_for_close is False
    assert "Files still processing: 2" in result.reason
    assert "2 original files being processed" in result.msg_nok_en
    assert "2 Originaldateien in Verarbeitung" in result.msg_nok_de
    assert "2 fichiers originaux" in result.msg_nok_fr
    assert "2 file originali" in result.msg_nok_it
    assert "Please wait" in result.msg_nok_en
    assert "contact support" not in result.msg_nok_en


@freeze_time("2023-01-01 12:00:00")
def test_files_in_processing_timeout_exceeded(base_stats):
    """Test when files are being processed beyond the timeout period."""
    stats = base_stats
    stats.num_original_files_in_processing = 3
    stats.original_file_processing_timestamp = timezone.now() - timedelta(hours=2)

    result = evaluate_original_files_in_processing(stats, DossierCloseStrategy.DEFAULT)

    assert result is not None
    assert result.ready_for_close is False
    assert "Processing timeout: 3" in result.reason
    assert "contact support" in result.msg_nok_en.lower()
    assert "support kontaktieren" in result.msg_nok_de.lower()
    assert "contacter le support" in result.msg_nok_fr.lower()
    assert "contattare il supporto" in result.msg_nok_it.lower()


@pytest.mark.parametrize(
    "strategy",
    [
        DossierCloseStrategy.DEFAULT,
        DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
        DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
    ],
)
def test_different_strategies(base_stats, strategy):
    """Test that function works with all strategies."""
    stats = base_stats
    stats.num_original_files_in_processing = 1
    stats.original_file_processing_timestamp = timezone.now()

    result = evaluate_original_files_in_processing(stats, strategy)
    assert result is not None
    assert result.strategy == strategy


def test_null_processing_timestamp(base_stats):
    """Test behavior when processing timestamp is None but files are in processing."""
    stats = base_stats
    stats.num_original_files_in_processing = 1
    stats.original_file_processing_timestamp = None

    result = evaluate_original_files_in_processing(stats, DossierCloseStrategy.DEFAULT)

    assert result is not None
    assert result.ready_for_close is False
    assert "Files still processing" in result.reason
