import pytest

from dossier.models import Account
from image_exporter.photo_album_docx import get_photo_album_docx_template_path


@pytest.mark.django_db
def test_check_default_template_exists():
    account = Account.objects.create(
        key="some", name="Some strange financial institute"
    )
    assert get_photo_album_docx_template_path(
        account.photo_album_docx_template, True
    ).exists()


@pytest.mark.django_db
def test_check_instructions_key():
    account = Account.objects.create(
        key="some", name="Some strange financial institute"
    )
    assert account.instructions_menu_key == "default"
