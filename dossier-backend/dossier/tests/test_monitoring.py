import pytest
from datetime import datetime, timedelta
from pathlib import Path
from uuid import uuid4

from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.base import ContentFile
from faker import Faker

from dossier.models import (
    Account,
    DocumentCategory,
    Dossier,
    DossierFile,
    OriginalFile,
    FileStatus,
    ExtractedFile,
)
from processed_file.models import ProcessedFile
from unittest.mock import call

from dossier.monitoring import (
    check_file_statuses,
    _get_unprocessed_extracted_files,
    _get_unprocessed_original_files,
)
from django.utils import timezone
from django.conf import settings


@database_sync_to_async
def gen_processed_files(
    service_account: Account, expires: datetime, status: FileStatus
):

    faker = Faker(locale="de_CH")

    User: AbstractUser = get_user_model()

    user, _ = User.objects.get_or_create(email=faker.email())

    dossier_uuid = uuid4()
    dossier = Dossier.objects.create(
        uuid=dossier_uuid, name="Testdossier", account=service_account, owner=user
    )

    data_directory = Path(__file__).parent.parent / "data"
    pdf_file = data_directory / "lawssimplicity.pdf"
    txt_file = data_directory / "lawsimplicity.txt"
    img_file = data_directory / "lawssimplicity.jpg"
    # generic_page_cat = PageCategory.objects.get(id=1)
    (
        DocumentCategory.objects.filter(account=dossier.account)
        .exclude(exclude_for_recommendation=True)
        .all()
    )

    data = pdf_file.read_bytes()

    searchable_pdf_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=data, name="lawsimplicity.pdf"),
        bucket=dossier.bucket,
        created_at=expires,
        updated_at=expires,
    )
    # Need for forcibly override the created_at and updated_at
    searchable_pdf_file.created_at = expires
    searchable_pdf_file.updated_at = expires
    searchable_pdf_file.save()

    original_file = OriginalFile.objects.create(
        dossier=dossier,
        file=searchable_pdf_file,
        status=status,
        created_at=expires,
        updated_at=expires,
    )

    original_file.created_at = expires
    original_file.updated_at = expires
    original_file.save()

    extracted_file = ExtractedFile.objects.create(
        dossier=dossier,
        original_file=original_file,
        path_from_original="lawssimplicity.pdf",
        status=status,
        file=searchable_pdf_file,
        created_at=expires,
        updated_at=expires,
    )
    extracted_file.created_at = expires
    extracted_file.updated_at = expires
    extracted_file.save()

    # We don't need to create a processed_file, image_file, searchable_txt_file for the status test - however, I'm
    # leaving it in incase we want to expand our monitoring of processing files
    processed_file = ProcessedFile.objects.create(
        dossier=dossier,
        extracted_file=extracted_file,
        created_at=expires,
        updated_at=expires,
    )
    processed_file.created_at = expires
    processed_file.updated_at = expires
    processed_file.save()

    image_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=img_file.read_bytes(), name=img_file.name),
        bucket=dossier.bucket,
        created_at=expires,
        updated_at=expires,
    )

    image_file.created_at = expires
    image_file.updated_at = expires
    image_file.save()

    searchable_txt_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=txt_file.read_bytes(), name=txt_file.name),
        bucket=dossier.bucket,
        created_at=expires,
        updated_at=expires,
    )

    searchable_txt_file.created_at = expires
    searchable_txt_file.updated_at = expires
    searchable_txt_file.save()

    return dossier, original_file, extracted_file, processed_file


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_unprocessed_original_and_extracted_file_log(
    mocker, service_account, load_document_categories
):
    # Mock structlog logger
    mock_logger = mocker.patch(
        "dossier.monitoring.logger.info", return_value=mocker.Mock()
    )

    (
        dossier,
        original_file,
        extracted_file,
        processed_file,
    ) = await gen_processed_files(
        service_account,
        timezone.now()
        - timedelta(minutes=settings.FILE_STATUS_MAX_PROCESSING_TIME + 1),
        FileStatus.PROCESSING,
    )

    # Trigger the check_file_statuses function
    await check_file_statuses(run_once=True)

    extracted_files = await _get_unprocessed_extracted_files()
    assert extracted_files[0] == extracted_file

    original_files = await _get_unprocessed_original_files()
    assert original_files[0] == original_file

    mock_logger.assert_has_calls(
        [
            call(
                "Unprocessed extracted file found",
                account_key=extracted_file.dossier.account.key,
                dossier_uuid=extracted_file.dossier.uuid,
                original_file_uuid=extracted_file.original_file.uuid,
                extracted_file_uuid=extracted_file.uuid,
                tag="unprocessed_extracted_file",
            )
        ]
    )

    mock_logger.assert_has_calls(
        [
            call(
                "Unprocessed original file found",
                account_key=original_file.dossier.account.key,
                dossier_uuid=original_file.dossier.uuid,
                original_file_uuid=original_file.uuid,
                tag="unprocessed_original_file",
            )
        ]
    )


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_all_files_processed_log(
    mocker, service_account, load_document_categories
):
    # Test the case where all documents have previously been processed
    mock_logger = mocker.patch(
        "dossier.monitoring.logger.info", return_value=mocker.Mock()
    )

    (
        dossier,
        original_file,
        extracted_file,
        processed_file,
    ) = await gen_processed_files(
        service_account,
        timezone.now() - timedelta(minutes=60),
        FileStatus.PROCESSED,
    )

    # Trigger the check_file_statuses function
    await check_file_statuses(run_once=True)

    # Check if the expected log was created
    mock_logger.assert_has_calls(
        [
            call(
                "All files are processed within the maximal processing time",
                MAX_PROCESSING_TIME=settings.FILE_STATUS_MAX_PROCESSING_TIME,
            )
        ]
    )


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_all_files_processing_log(
    mocker, service_account, load_document_categories
):
    # Test the case where all documents are currently being processed under the MAX_PROCESSING_TIME
    mock_logger = mocker.patch(
        "dossier.monitoring.logger.info", return_value=mocker.Mock()
    )

    (
        dossier,
        original_file,
        extracted_file,
        processed_file,
    ) = await gen_processed_files(
        service_account, timezone.now(), FileStatus.PROCESSING
    )

    # Trigger the check_file_statuses function
    await check_file_statuses(run_once=True)

    # Check if the expected log was created
    mock_logger.assert_has_calls(
        [
            call(
                "All files are processed within the maximal processing time",
                MAX_PROCESSING_TIME=settings.FILE_STATUS_MAX_PROCESSING_TIME,
            )
        ]
    )
