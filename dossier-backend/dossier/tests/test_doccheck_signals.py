import pytest
from django.contrib.auth.models import User

from doccheck.models import BusinessCaseType as DocCheckBusinessCaseType, Case
from doccheck.tests.test_services import create_doc_check
from dossier.models import Account, BusinessCaseType, Dossier
from dossier.services import create_dossier


@pytest.fixture
def setup_basics(db):
    doc_check = create_doc_check()
    account = Account.objects.create(
        key="test", name="Test Account", active_doc_check=doc_check
    )
    BusinessCaseType.objects.all().delete()
    assert BusinessCaseType.objects.count() == 0
    assert DocCheckBusinessCaseType.objects.count() == 1
    return doc_check, account


def test_update_case_business_case_type_pre_save_dossier_business_case(
    db, setup_basics
):
    doc_check, account = setup_basics
    user = User.objects.get(username="<EMAIL>")
    test_dossier = create_dossier(
        account, "test dossier business case type", "de", user
    )
    assert test_dossier.businesscase_type is None
    case_for_test_dossier = test_dossier.doccheck_case
    assert case_for_test_dossier.business_case_type.key == "UNKNOWN"

    business_case_type_key_1 = "test business case type 1"
    dossier_business_case_type_1 = BusinessCaseType.objects.create(
        account=account, key=business_case_type_key_1
    )
    doccheck_business_case_type_1 = DocCheckBusinessCaseType.objects.create(
        doc_check=doc_check, key=business_case_type_key_1
    )
    test_dossier.businesscase_type = dossier_business_case_type_1
    test_dossier.save()
    assert (
        Dossier.objects.get(uuid=test_dossier.uuid).businesscase_type
        == dossier_business_case_type_1
    )
    assert (
        Case.objects.get(uuid=case_for_test_dossier.uuid).business_case_type
        == doccheck_business_case_type_1
    )

    business_case_type_key_2 = "test business case type 2"
    dossier_business_case_type_2 = BusinessCaseType.objects.create(
        account=account, key=business_case_type_key_2
    )
    doccheck_business_case_type_2 = DocCheckBusinessCaseType.objects.create(
        doc_check=doc_check, key=business_case_type_key_2
    )
    test_dossier.businesscase_type = dossier_business_case_type_2
    test_dossier.save()
    assert (
        Dossier.objects.get(uuid=test_dossier.uuid).businesscase_type
        == dossier_business_case_type_2
    )
    assert (
        Case.objects.get(uuid=case_for_test_dossier.uuid).business_case_type
        == doccheck_business_case_type_2
    )

    test_dossier.businesscase_type = None
    test_dossier.save()
    assert Dossier.objects.get(uuid=test_dossier.uuid).businesscase_type is None
    # Dossier might have no businesscase_type (set as null=True).
    # But Case always needs to have a business_case_type (set as null=False).
    # Use the default record in DocCheck.BusinessCaseType with key='UNKNOWN' in such cases.
    doccheck_business_case_type_unknown = DocCheckBusinessCaseType.objects.get(
        doc_check=doc_check, key="UNKNOWN"
    )
    assert (
        Case.objects.get(uuid=case_for_test_dossier.uuid).business_case_type
        == doccheck_business_case_type_unknown
    )


def test_update_dossier_business_case_type_pre_save_case_business_case(
    db, setup_basics
):
    doc_check, account = setup_basics
    user = User.objects.get(username="<EMAIL>")
    test_dossier = create_dossier(
        account, "test dossier business case type", "de", user
    )
    assert test_dossier.businesscase_type is None
    case_for_test_dossier = test_dossier.doccheck_case
    assert case_for_test_dossier.business_case_type.key == "UNKNOWN"

    business_case_type_key_1 = "test business case type 1"
    doccheck_business_case_type_1 = DocCheckBusinessCaseType.objects.create(
        doc_check=doc_check, key=business_case_type_key_1
    )
    dossier_business_case_type_1 = BusinessCaseType.objects.create(
        account=account, key=business_case_type_key_1
    )
    case_for_test_dossier.business_case_type = doccheck_business_case_type_1
    case_for_test_dossier.save()
    assert (
        Case.objects.get(uuid=case_for_test_dossier.uuid).business_case_type
        == doccheck_business_case_type_1
    )
    assert (
        Dossier.objects.get(uuid=test_dossier.uuid).businesscase_type
        == dossier_business_case_type_1
    )

    business_case_type_key_2 = "test business case type 2"
    doccheck_business_case_type_2 = DocCheckBusinessCaseType.objects.create(
        doc_check=doc_check, key=business_case_type_key_2
    )
    dossier_business_case_type_2 = BusinessCaseType.objects.create(
        account=account, key=business_case_type_key_2
    )
    case_for_test_dossier.business_case_type = doccheck_business_case_type_2
    case_for_test_dossier.save()
    assert (
        Case.objects.get(uuid=case_for_test_dossier.uuid).business_case_type
        == doccheck_business_case_type_2
    )
    assert (
        Dossier.objects.get(uuid=test_dossier.uuid).businesscase_type
        == dossier_business_case_type_2
    )

    # Dossier might have no businesscase_type (set as null=True).
    # But Case always needs to have a business_case_type (set as null=False).
    # Use the default record in DocCheck.BusinessCaseType with key='UNKNOWN' in such cases.
    doccheck_business_case_type_unknown = DocCheckBusinessCaseType.objects.get(
        doc_check=doc_check, key="UNKNOWN"
    )
    case_for_test_dossier.business_case_type = doccheck_business_case_type_unknown
    case_for_test_dossier.save()
    assert (
        Case.objects.get(uuid=case_for_test_dossier.uuid).business_case_type
        == doccheck_business_case_type_unknown
    )
    assert Dossier.objects.get(uuid=test_dossier.uuid).businesscase_type is None
