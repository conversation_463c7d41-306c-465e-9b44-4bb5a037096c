# Generated by Django 3.2.13 on 2022-04-26 10:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0015_documentcategory_exclude_for_recommendation'),
    ]

    operations = [
        migrations.AddField(
            model_name='documentcategory',
            name='de',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='documentcategory',
            name='en',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='documentcategory',
            name='fr',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='documentcategory',
            name='it',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
