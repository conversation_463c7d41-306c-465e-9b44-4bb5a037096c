# Generated by Django 4.2.20 on 2025-05-07 09:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0121_originalfile_file_replacement"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="max_num_pages_allowed_input",
            field=models.IntegerField(
                default=None,
                help_text="Maximum number of pages that are accepted in a single document for processing. If longer documents are received processing will abort. If None, the global setting inside the processing system is used.",
                null=True,
            ),
        ),
    ]
