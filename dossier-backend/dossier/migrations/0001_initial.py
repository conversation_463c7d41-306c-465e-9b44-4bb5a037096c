# Generated by Django 3.2.3 on 2021-06-22 12:23

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('default_bucket_name', models.CharField(max_length=255)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DocumentCategory',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.IntegerField()),
                ('name', models.CharField(max_length=255)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Dossier',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('lang', models.CharField(choices=[('DE', 'German'), ('EN', 'English'), ('FR', 'French'), ('IT', 'Italian')], default='DE', max_length=2)),
                ('bucket', models.CharField(default='dossier', max_length=255)),
                ('name', models.CharField(default='Dossier vom', max_length=255)),
                ('account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='dossier.account')),
                ('owner', models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DossierFile',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('data', models.FileField()),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossier')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ExtractedFile',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('Processing', 'Processing'), ('Processed', 'Processed')], default='Processing', max_length=10)),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossier')),
                ('file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossierfile')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PageCategory',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.IntegerField()),
                ('name', models.CharField(max_length=255)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='OriginalFile',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('Processing', 'Processing'), ('Processed', 'Processed')], default='Processing', max_length=10)),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='original_files', to='dossier.dossier')),
                ('file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossierfile')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FileException',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('en', models.TextField()),
                ('de', models.TextField()),
                ('details', models.TextField(blank=True, null=True)),
                ('type', models.CharField(choices=[('Processed', 'Processed'), ('Extracted', 'Extracted')], max_length=16)),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossier')),
                ('extracted_file', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='dossier.extractedfile')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='extractedfile',
            name='original_file',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.originalfile'),
        ),
    ]
