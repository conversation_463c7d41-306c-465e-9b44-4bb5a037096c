# Generated by Django 4.2.11 on 2024-03-22 12:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0091_account_enable_semantic_page_image_lazy_loading"),
    ]

    operations = [
        migrations.AddField(
            model_name="copydossierhistory",
            name="account",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="dossier.account",
            ),
        ),
        migrations.AddField(
            model_name="copydossierhistory",
            name="duration",
            field=models.DurationField(null=True),
        ),
        migrations.AddField(
            model_name="copydossierhistory",
            name="status",
            field=models.CharField(
                choices=[
                    ("Processing", "Processing"),
                    ("Processed", "Processed"),
                    ("Error", "Error"),
                ],
                default="Processing",
                max_length=10,
            ),
        ),
    ]
