# Generated by Django 3.2.13 on 2022-06-14 12:00

from django.db import migrations

from dossier.models import generate_path


def migrate_dossier_files_from_storages_to_hys3storage_v2(apps, schema_editor):
    DossierFile = apps.get_model("dossier", "DossierFile")
    return migart_from_boto2hystorage(DossierFile)


def migart_from_boto2hystorage(DossierFile):
    dossier_files = DossierFile.objects.all()
    migrated = []
    for dossier_file in dossier_files:
        name: str = dossier_file.data.name
        if name.startswith(dossier_file.bucket):
            continue

        dossier_file.data.name = generate_path(dossier_file, name)
        dossier_file.save()
        migrated.append((name, dossier_file.data.name))
    return migrated


class Migration(migrations.Migration):
    dependencies = [
        ('dossier', '0020_alter_dossierfile_data'),
    ]

    operations = [
        migrations.RunPython(migrate_dossier_files_from_storages_to_hys3storage_v2),
    ]
