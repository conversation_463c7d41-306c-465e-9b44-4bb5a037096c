# Generated by Django 3.2.16 on 2022-10-31 15:45

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0034_documentcategory_description_de_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessCaseType',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=255)),
                ('name_de', models.CharField(blank=True, max_length=255, null=True)),
                ('name_fr', models.CharField(blank=True, max_length=255, null=True)),
                ('name_it', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('name_en', models.CharField(blank=True, max_length=255, null=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='dossier',
            name='businesscase_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dossier.businesscasetype'),
        ),
    ]
