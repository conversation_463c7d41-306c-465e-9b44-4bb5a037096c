# Generated by Django 4.2.16 on 2024-12-03 12:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0113_account_dossier_close_expiry_days_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="account",
            name="dossier_close_strategy",
            field=models.CharField(
                choices=[
                    ("DEFAULT", "DEFAULT: Basic strategy, no requirements"),
                    (
                        "REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE",
                        "REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE",
                    ),
                    (
                        "EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE",
                        "EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE",
                    ),
                ],
                default="DEFAULT",
                help_text="Defines which requirements must be met before a dossier can be closed.",
                max_length=50,
                verbose_name="Dossier Close Strategy",
            ),
        ),
    ]
