# Generated by Django 3.2.20 on 2023-08-25 17:15

from django.db import migrations, models
import django.db.models.deletion
import projectconfig.jwk
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0057_account_enable_button_open_in_new_tab'),
    ]

    operations = [
        migrations.CreateModel(
            name='JWK',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('jwk', models.JSONField(validators=[projectconfig.jwk.validate_jwk])),
                ('enabled', models.BooleanField(default=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jwks', to='dossier.account')),
            ],
            options={
                'verbose_name': 'JWK',
                'verbose_name_plural': 'JWK',
            },
        ),
    ]
