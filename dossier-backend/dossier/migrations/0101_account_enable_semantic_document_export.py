# Generated by Django 4.2.15 on 2024-09-24 13:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0100_account_cdp_field_set_account_enable_form_tab"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_semantic_document_export",
            field=models.BooleanField(
                default=False,
                help_text="If true show an export button 'Transfer to archive' with customized wording based on frontend_theme. Else no export button is shown.",
            ),
        ),
    ]
