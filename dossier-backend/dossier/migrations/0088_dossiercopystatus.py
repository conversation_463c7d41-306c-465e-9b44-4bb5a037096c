# Generated by Django 4.2.9 on 2024-01-30 15:55

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0087_alter_fileexception_exception_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="DossierCopyStatus",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "external_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("done", models.DateTimeField(blank=True, null=True)),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dossier.account",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
