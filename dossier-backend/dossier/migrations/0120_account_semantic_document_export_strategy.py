# Generated by Django 4.2.20 on 2025-04-04 11:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0119_alter_account_enable_semantic_document_annotations"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="semantic_document_export_strategy",
            field=models.CharField(
                choices=[
                    ("DEFAULT", "DEFAULT: export as pdf"),
                    (
                        "SWISSCOM_EDOSSIER_XML_ZIP",
                        "SWISSCOM_EDOSSIER_XML_ZIP: Export as zip with pdf and SwissCom xml",
                    ),
                ],
                default="DEFAULT",
                help_text="Strategy for exporting semantic documents from a dossier. E.g export as a pdf, or as a zip with xml etc.",
                max_length=50,
                verbose_name="Semantic Document Export Strategy",
            ),
        ),
    ]
