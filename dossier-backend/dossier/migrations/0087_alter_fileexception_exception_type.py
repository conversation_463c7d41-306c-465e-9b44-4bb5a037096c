# Generated by Django 4.2.9 on 2024-01-30 13:20

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0086_originalfile_force_document_category_key_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="fileexception",
            name="exception_type",
            field=models.IntegerField(
                choices=[
                    (1, "Unknown Exception"),
                    (2, "Not Readable"),
                    (3, "Password Protected"),
                    (4, "Unsupported Filetype"),
                    (5, "Too Small File"),
                    (6, "Xlsm File Cannot Be Converted"),
                    (7, "Ocr Filetype Processing"),
                    (999, "Unmapped Exception"),
                ],
                default=1,
            ),
        ),
    ]
