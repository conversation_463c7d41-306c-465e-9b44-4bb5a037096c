# Generated by Django 3.2.23 on 2023-11-13 14:50

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0076_auto_20231102_1346'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProcessedPageCount',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('dossier_uuid', models.UUIDField()),
                ('external_dossier_id', models.CharField(blank=True, max_length=255, null=True)),
                ('original_file_uuid', models.UUIDField()),
                ('extracted_file_uuid', models.UUIDField()),
                ('extracted_file_path_from_original', models.Cha<PERSON><PERSON><PERSON>(max_length=32000)),
                ('processed_pages', models.PositiveIntegerField()),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
            ],
        ),
        migrations.AddConstraint(
            model_name='processedpagecount',
            constraint=models.UniqueConstraint(fields=('account', 'dossier_uuid', 'original_file_uuid', 'extracted_file_uuid'), name='unique_processed_page_count'),
        ),
    ]
