# Generated by Django 3.2.16 on 2023-01-31 16:38

from django.db import migrations, transaction


def forwards(apps, schema_editor):
    # get the models as defined at the time of this migration
    Account = apps.get_model("dossier", "Account")
    Dossier = apps.get_model("dossier", "Dossier")
    DossierRole = apps.get_model("dossier", "DossierRole")
    DossierUser = apps.get_model("dossier", "DossierUser")
    UserInvolvement = apps.get_model("dossier", "UserInvolvement")

    with transaction.atomic():
        all_accounts=Account.objects.all()
        all_dossiers=Dossier.objects.all()

        for account in all_accounts:
            dossier_role=DossierRole.objects.filter(account=account, key='ASSIGNEE').first()

            if (dossier_role is None):
                if (account.name == 'BEKB dev'):
                    DossierRole.objects.get_or_create(
                        defaults=dict(name_de='FICO', name_fr='FICO', name_en='FICO', name_it='FICO'),
                        key='FICO', user_selectable=False, show_separate_filter=True, account=account)

                DossierRole.objects.get_or_create(
                    defaults=dict(name_de='Zuständiger', name_fr='Responsable', name_en='Assignee', name_it='Assegnatario'),
                    key='ASSIGNEE', user_selectable=True, show_separate_filter=True, account=account)

        for dossier in all_dossiers:
            dossier_user, _ = DossierUser.objects.get_or_create(account=dossier.account, user=dossier.owner)
            dossier_role = DossierRole.objects.filter(account=dossier.account, key='ASSIGNEE').first()

            user_involvement = UserInvolvement.objects.filter(dossier=dossier, user=dossier_user, role=dossier_role).first()
            if (user_involvement is None):
                UserInvolvement.objects.create(dossier=dossier, user=dossier_user, role=dossier_role)


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0047_auto_20230117_1526'),
    ]

    operations = [
        migrations.RunPython(forwards)
    ]
