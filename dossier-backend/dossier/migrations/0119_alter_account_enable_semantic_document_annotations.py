# Generated by Django 4.2.20 on 2025-04-03 14:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0118_account_enable_semantic_document_annotations"),
    ]

    operations = [
        migrations.AlterField(
            model_name="account",
            name="enable_semantic_document_annotations",
            field=models.BooleanField(
                default=False,
                help_text="Whether the user has the option to add highlights and comments to the semantic documents in the detail view, and whether these annotations get exported with the document.",
            ),
        ),
    ]
