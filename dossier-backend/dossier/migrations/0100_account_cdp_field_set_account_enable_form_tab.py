# Generated by Django 4.2.15 on 2024-09-10 13:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0099_originalfile_exception_fr_originalfile_exception_it"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="cdp_field_set",
            field=models.CharField(
                blank=True,
                help_text="Name of the field set used for the CDP integration.",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="enable_form_tab",
            field=models.BooleanField(
                default=False, help_text="If True show the form tab (cdp) in the DMF."
            ),
        ),
    ]
