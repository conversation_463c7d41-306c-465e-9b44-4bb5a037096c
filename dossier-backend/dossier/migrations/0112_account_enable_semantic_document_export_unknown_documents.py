# Generated by Django 4.2.16 on 2024-11-20 09:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "dossier",
            "0111_rename_enable_document_splitting_account_enable_semantic_document_splitting",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_semantic_document_export_unknown_documents",
            field=models.BooleanField(
                default=False,
                help_text="If true unknown semantic documents (all languages) are exported in the export process. Else only semantic documents classified as not-unknown are exported.",
            ),
        ),
    ]
