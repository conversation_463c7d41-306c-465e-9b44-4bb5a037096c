# Generated by Django 4.2.11 on 2024-03-15 15:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0090_account_processing_strategy"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_semantic_page_image_lazy_loading",
            field=models.BooleanField(
                default=False,
                help_text="If True use the lazy loading feature for page images. Else all images are loaded directly. Applies to dossier view and detail view.",
            ),
        ),
    ]
