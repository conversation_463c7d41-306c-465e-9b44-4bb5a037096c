# Generated by Django 4.2.11 on 2024-04-07 21:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0092_copydossierhistory_account_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_doccheck_in_statemgmt",
            field=models.BooleanField(
                default=True,
                help_text="If True the result of the document completeness check is used in state transitions of the state machine. Else the 'is_doccheck_fulfilled' state condition is always true.",
            ),
        ),
    ]
