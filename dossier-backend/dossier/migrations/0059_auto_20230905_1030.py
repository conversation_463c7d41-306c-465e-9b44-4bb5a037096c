# Generated by Django 3.2.20 on 2023-09-05 08:30

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0058_jwk'),
    ]

    operations = [
        migrations.CreateModel(
            name='RealestateProperty',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=255)),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('floor', models.IntegerField(blank=True, null=True)),
                ('street', models.CharField(blank=True, max_length=255, null=True)),
                ('street_nr', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('zipcode', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('city', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossier')),
            ],
        ),
        migrations.CreateModel(
            name='AssignedRealestatePropertyOriginalFile',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('originalfile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.originalfile')),
                ('realestate_property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.realestateproperty')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='originalfile',
            name='realestate_property',
            field=models.ManyToManyField(through='dossier.AssignedRealestatePropertyOriginalFile', to='dossier.RealestateProperty'),
        ),
        migrations.AddConstraint(
            model_name='realestateproperty',
            constraint=models.UniqueConstraint(fields=('dossier', 'key'), name='unique_realestate_per_dossier'),
        ),
    ]
