# Generated by Django 4.2.17 on 2025-01-09 15:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0114_alter_account_dossier_close_strategy"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_rendering_bekb_mortgage_archiving_tab",
            field=models.BooleanField(
                default=False,
                help_text="If True show the 'Archiving tab' for the BEKB mortgage process, everything related to BEKB collaterals and the BEKB business case selection. Else tab is not visible and BEKB business case also not visible. This is True for BEKB mortgage and False for BEKB Fipla and all non-BEKB accounts.",
            ),
        ),
    ]
