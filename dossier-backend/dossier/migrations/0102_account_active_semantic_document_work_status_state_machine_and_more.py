# Generated by Django 4.2.15 on 2024-09-25 16:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("statemgmt", "0008_alter_transitionprecondition_options"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("dossier", "0101_account_enable_semantic_document_export"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="active_semantic_document_work_status_state_machine",
            field=models.ForeignKey(
                blank=True,
                help_text="State machine for all semantic documents in an account.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="accounts_with_active_semantic_document_status",
                to="statemgmt.statemachine",
            ),
        ),
        migrations.AddField(
            model_name="originalfile",
            name="create_user",
            field=models.ForeignKey(
                default=None,
                help_text="The user who created or altered the original file. Can be a user logged in via the DMF or an API user.",
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="create_user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="originalfile",
            name="source",
            field=models.CharField(
                blank=True,
                choices=[("API", "API"), ("DMF", "Dossier Management Frontend UI")],
                default=None,
                help_text="The source of the original file, i.e. how it was uploaded.",
                max_length=10,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="account",
            name="active_work_status_state_machine",
            field=models.ForeignKey(
                blank=True,
                help_text="State machine for all dossiers in an account.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="accounts_with_active_work_status",
                to="statemgmt.statemachine",
            ),
        ),
        migrations.CreateModel(
            name="DossierAccessGrant",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField()),
                ("has_access", models.BooleanField(default=False)),
                (
                    "dossier",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dossier.dossier",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="dossieraccessgrant",
            constraint=models.UniqueConstraint(
                fields=("user", "dossier"), name="unique_user_dossier_access_grant"
            ),
        ),
    ]
