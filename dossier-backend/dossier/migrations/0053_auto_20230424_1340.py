# Generated by Django 3.2.18 on 2023-04-24 11:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dossier', '0052_account_enable_download_extraction_excel'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='enable_dossier_permission',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='AccessDelegation',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('expire_time', models.DateTimeField(blank=True, null=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
                ('delegate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delegate_user', to=settings.AUTH_USER_MODEL)),
                ('delegator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delegator_user', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddConstraint(
            model_name='accessdelegation',
            constraint=models.UniqueConstraint(fields=('account', 'delegator', 'delegate'), name='unique_access_delegation'),
        ),
    ]
