# Generated by Django 3.2.21 on 2023-09-22 13:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0063_auto_20230921_1301"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="document_download_ui_add_uuid_suffix",
            field=models.BooleanField(
                default=False,
                help_text=(
                    "Whether semantic pdfs generated by dossier zipper worker should have uuid added as a suffix when downloaded via UI.",
                ),
            ),
        ),
    ]
