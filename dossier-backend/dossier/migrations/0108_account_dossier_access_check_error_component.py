# Generated by Django 4.2.16 on 2024-11-04 15:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0107_account_enable_document_splitting"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="dossier_access_check_error_component",
            field=models.CharField(
                choices=[
                    ("default", "default"),
                    (
                        "bcge_access_check_instructions",
                        "bcge_access_check_instructions",
                    ),
                    (
                        "finnova_access_check_instructions",
                        "finnova_access_check_instructions",
                    ),
                ],
                default="default",
                help_text="Custom error message (with instructions) provided by frontend in case of access check error.",
                max_length=64,
            ),
        ),
    ]
