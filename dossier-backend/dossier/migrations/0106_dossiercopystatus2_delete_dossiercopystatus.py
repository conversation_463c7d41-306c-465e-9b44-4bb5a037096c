# Generated by Django 4.2.16 on 2024-10-28 16:38

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0105_dossieraccessgrant_scope"),
    ]

    operations = [
        migrations.DeleteModel(
            name="DossierCopyStatus",
        ),
        migrations.CreateModel(
            name="DossierCopyStatus",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("source_dossier_uuid", models.UUIDField()),
                (
                    "target_external_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("done", models.DateTimeField(blank=True, null=True)),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dossier.account",
                    ),
                ),
                (
                    "target_dossier",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dossier.dossier",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
