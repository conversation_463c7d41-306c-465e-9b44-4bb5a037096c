# Generated by Django 3.2.6 on 2022-01-31 16:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0013_dossier_note'),
    ]

    operations = [
        migrations.AddField(
            model_name='originalfile',
            name='details',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='originalfile',
            name='exception_de',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='originalfile',
            name='exception_details',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='originalfile',
            name='exception_en',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='extractedfile',
            name='status',
            field=models.CharField(choices=[('Processing', 'Processing'), ('Processed', 'Processed'), ('Error', 'Error')], default='Processing', max_length=10),
        ),
        migrations.AlterField(
            model_name='originalfile',
            name='status',
            field=models.CharField(choices=[('Processing', 'Processing'), ('Processed', 'Processed'), ('Error', 'Error')], default='Processing', max_length=10),
        ),
    ]
