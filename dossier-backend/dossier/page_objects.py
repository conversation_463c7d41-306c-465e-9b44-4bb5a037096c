from uuid import <PERSON>UI<PERSON>

from django.shortcuts import get_object_or_404

from dossier import schemas
from dossier.models import <PERSON><PERSON><PERSON>
from processed_file.models import PageObjectTitle, ConfidenceLevel, PageObject
from semantic_document.models import SemanticPagePageObject


def get_semantic_pages_object(dossier_uuid: UUID):
    return (
        SemanticPagePageObject.objects.filter(semantic_page__dossier=dossier_uuid)
        .select_related(
            "page_object",
            "semantic_page",
            "page_object__key",
            "page_object__type",
            "page_object__processed_page",
        )
        .values(
            "uuid",
            "page_object__uuid",
            "page_object__key__key",
            "page_object__key__de",
            "page_object__key__en",
            "page_object__key__fr",
            "page_object__key__it",
            "page_object__value",
            "page_object__visible",
            "page_object__type__name",
            "page_object__ref_width",
            "page_object__ref_height",
            "page_object__top",
            "page_object__left",
            "page_object__right",
            "page_object__bottom",
            "page_object__processed_page__number",
            "semantic_page__number",
            "page_object__confidence_value",
            "page_object__confidence_formatted",
            "page_object__confidence_level",
            "semantic_page__uuid",
        )
    )


def get_or_create_page_object_title(key: str) -> PageObjectTitle:
    page_object_title, created = PageObjectTitle.objects.get_or_create(
        key=key, defaults={"key": key}
    )
    return page_object_title


def check_page_object_title(title, page_object_id):
    args_to_split = [" ", "-"]

    for arg_to_split in args_to_split:
        split_title = title.split(arg_to_split)[0]

        if page_object_id.isnumeric() and str(split_title) == str(page_object_id):
            return split_title, True

    return None, False


def get_serialized_pages_objects(
    semantic_pages_objects, semantic_pages_uuids, is_aggregated: bool = True
):
    filter = "semantic_page__uuid" if is_aggregated else "semantic_page__uuid"

    return [
        serialize_page_object(spo)
        for spo in semantic_pages_objects
        if spo[filter] in semantic_pages_uuids
    ]


def serialize_page_object(page_object: dict, use_semantic_page_no: bool = False):
    return {
        "uuid": str(page_object["page_object__uuid"]),
        "key": page_object["page_object__key__key"],
        "visible": page_object["page_object__visible"],
        "title": page_object["page_object__key__de"],
        "titles": {
            "en": page_object["page_object__key__en"],
            "de": page_object["page_object__key__de"],
            "fr": page_object["page_object__key__fr"],
            "it": page_object["page_object__key__it"],
        },
        "value": page_object["page_object__value"],
        "type": page_object["page_object__type__name"],
        "bbox": {
            "ref_width": page_object["page_object__ref_width"],
            "ref_height": page_object["page_object__ref_height"],
            "top": page_object["page_object__top"],
            "left": page_object["page_object__left"],
            "right": page_object["page_object__right"],
            "bottom": page_object["page_object__bottom"],
        },
        # We theoretically should be using the page number of the semantic page, rather than the processed page, as the
        # semantic page can be re-ordered via the webui and so the page number can change
        # keep it as it is here, as the code for compy semantic document and copy semantic pages uses
        # page_object.processed_page.number for matching
        # Also there maybe logic/dependencies on frontend for using processed_page number vs semantic_page number
        "page_number": (
            page_object["semantic_page__number"]
            if use_semantic_page_no
            else page_object["page_object__processed_page__number"]
        ),
        "confidence": page_object["page_object__confidence_value"],
        "confidence_summary": {
            "value": page_object["page_object__confidence_value"],
            "value_formatted": page_object["page_object__confidence_formatted"],
            "level": page_object["page_object__confidence_level"],
        },
        "semantic_page_uuid": str(page_object["semantic_page__uuid"]),
    }


def update_page_object(
    page_object, edit_page_object_schema: schemas.EditPageObjectSchema
):
    bbox = edit_page_object_schema.bbox
    page_object_value = edit_page_object_schema.page_object_value

    if page_object_value:
        page_object.value = edit_page_object_schema.page_object_value

    if bbox:
        for attr, value in bbox.model_dump.items():
            setattr(page_object, attr, value)

    if page_object_value:
        page_object.confidence_formatted = "100%"
        page_object.confidence_value = 1.0
        page_object.confidence_level = ConfidenceLevel.HIGH
        page_object.save()

    return page_object


def get_page_object_from_api(
    dossier: Dossier, page_object_uuid: UUID, semantic_page_uuid: UUID
) -> PageObject:
    semantic_page = get_object_or_404(
        dossier.semantic_pages,
        semantic_page_page_objects__page_object__uuid=page_object_uuid,
        uuid=semantic_page_uuid,
    )

    return get_object_or_404(
        semantic_page.semantic_page_page_objects.select_related("page_object"),
        page_object__uuid=page_object_uuid,
    ).page_object
