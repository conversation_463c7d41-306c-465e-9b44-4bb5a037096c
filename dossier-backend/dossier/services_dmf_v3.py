import json
from typing import List, Optional

import structlog
from django.db import transaction
from ninja.errors import HttpError

from bekb.models import BEKBDossierProperties
from dossier.helpers_v2 import is_valid_uuid

from dossier.models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>n<PERSON>lve<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from dossier.schemas import PageObjectTit<PERSON>, BoundingBox
from dossier.schemas_dmf_v3 import (
    FinHurdleGroup,
    FinhurdleRef,
    DossierPropertiesResponse,
    DossierRoleSchema,
)
from processed_file.models import PageObjectType
from semantic_document.models import SemanticPagePageObject

# Services here are tightly coupled with frontend views - I suspect we are unlikely to us these functions else where

logger = structlog.get_logger()


def export_page_objects_for_hurdle_view(
    dossier: Dossier,
) -> List[FinHurdleGroup]:
    """
    Retrieve and process a list of FinHurdleGroup objects for use with frontend finhurdle view.

    This function completes the following tasks:
    1. Get page objects with type "FINHURDLE" for dossier
    2. Group by page object title
    3. Organizes data into FinHurdleGroup schema for frontend

    Args:
        dossier (Dossier): Input Dossier instance, from which related page objects are to be retrieved and grouped.

    Returns:
        List[FinHurdleGroup]: A list of processed and organized FinHurdleGroup objects connected to the input Dossier.
    """
    finhurdle_page_object_type = PageObjectType.objects.get(name="FINHURDLE")

    sp_page_objects = (
        SemanticPagePageObject.objects.filter(
            page_object__type=finhurdle_page_object_type
        )
        .filter(
            semantic_page__dossier=dossier,
            page_object__visible=True,
            semantic_page__semantic_document__deleted_at__isnull=True,
        )
        .select_related(
            "page_object",
            "semantic_page",
            "semantic_page__semantic_document",
            "page_object__key",
            "page_object__type",
            "page_object__processed_page",
        )
        .order_by(
            "semantic_page__semantic_document",
            "semantic_page__number",
            "page_object__top",
            "page_object__right",
        )
    )

    arranged_fin_hurdles_dict = {}

    titles_dict = {}

    for spo in sp_page_objects:
        if spo.page_object.key.key not in arranged_fin_hurdles_dict:
            arranged_fin_hurdles_dict[spo.page_object.key.key] = []
            titles_dict[spo.page_object.key.key] = PageObjectTitles(
                en=spo.page_object.key.en,
                de=spo.page_object.key.de,
                fr=spo.page_object.key.fr,
                it=spo.page_object.key.it,
            )

        # add BoundingBox to FinhurdleRef as for PageObject
        bbox = BoundingBox(
            top=spo.page_object.top,
            bottom=spo.page_object.bottom,
            right=spo.page_object.right,
            left=spo.page_object.left,
            ref_height=spo.page_object.ref_height,
            ref_width=spo.page_object.ref_width,
        )

        arranged_fin_hurdles_dict[spo.page_object.key.key].append(
            FinhurdleRef(
                semantic_document_title=spo.semantic_page.semantic_document.title,
                semantic_document_uuid=spo.semantic_page.semantic_document.uuid,
                semantic_page_number=spo.semantic_page.number,
                semantic_page_uuid=spo.semantic_page.uuid,
                page_object_uuid=spo.page_object.uuid,
                page_object_value=json.loads(spo.page_object.value).get("value", ""),
                bbox=bbox,
            )
        )

    filtered_sp_page_objects: List[FinHurdleGroup] = []

    for key, value in arranged_fin_hurdles_dict.items():
        filtered_sp_page_objects.append(
            FinHurdleGroup(key=key, finhurdle_refs=value, titles=titles_dict[key])
        )

    return filtered_sp_page_objects


def assign_user_to_dossier(
    dossier: Dossier, dossier_user: DossierUser, dossier_roles: List[str] = []
):
    # Handle BEKB specific case
    if BEKBDossierProperties.objects.filter(
        dossier=dossier,
        pers=True,
    ).exists():

        if not dossier_user.user.groups.filter(
            name=f"BEKB/{dossier_user.account.key}/PERS"
        ).exists():

            raise HttpError(
                status_code=400,
                message="Can not assign a user without pers group to dossier which as pers enabled",
            )

    # Check to see if provided user is already assigned
    involvement = UserInvolvement.objects.filter(
        dossier=dossier, role__key="ASSIGNEE", user=dossier_user
    ).first()

    # Return early if already assigned
    if (
        involvement
        and len(dossier_roles) == 1
        and str(involvement.uuid) == dossier_roles[0]
    ):
        return dossier

    with transaction.atomic():
        # if no list of roles sent, update assignee by default
        if not dossier_roles:
            # Delete any existing Assignees
            UserInvolvement.objects.filter(
                dossier=dossier, role__key="ASSIGNEE"
            ).delete()

            UserInvolvement.objects.create(
                dossier=dossier,
                role=DossierRole.objects.get(key="ASSIGNEE", account=dossier.account),
                user=dossier_user,
            )
            # Dossier owner and assignee must be the same
            dossier.owner = dossier_user.user
            dossier.save()
        else:
            for role_uuid in dossier_roles:
                if not is_valid_uuid(role_uuid):
                    raise HttpError(
                        status_code=400,
                        message=f"Invalid format for role_uuid. Must be a valid UUID. dossier_user={dossier_user}, invalid role uuid={role_uuid}",
                    )
                role: DossierRole = DossierRole.objects.filter(uuid=role_uuid).first()
                if role is None:
                    logger.error(
                        "Tried to assign role with invalid uuid to user",
                        account=dossier.account,
                        dossier_name=dossier.name,
                        dossier_uuid=dossier.uuid,
                        dossier_user=dossier_user,
                        invalid_role_uuid=role_uuid,
                        dossier_roles=dossier_roles,
                    )
                    raise HttpError(
                        status_code=400,
                        message=f"Can not assign a user {dossier_user} to an invalid role {role_uuid}",
                    )

                # Delete any existing user involvments (should be only one but in inconsistent case it could be more)
                UserInvolvement.objects.filter(dossier=dossier, role=role).delete()

                # Create new involvement
                UserInvolvement.objects.create(
                    dossier=dossier, role=role, user=dossier_user
                )
                logger.info(
                    "Re-assign dossier",
                    account=dossier.account.key,
                    dossier_name=dossier.name,
                    dossier_uuid=str(dossier.uuid),
                    dossier_user=dossier_user,
                    role=role.key,
                    dossier_roles=dossier_roles,
                )

                if role.key == "ASSIGNEE":
                    # Dossier owner and assignee must be the same
                    dossier.owner = dossier_user.user
                    dossier.save()

    return dossier


def format_dossier_properties_schema(
    dossier: Dossier, involvement: Optional[UserInvolvement] = None
) -> DossierPropertiesResponse:
    assignee = None
    if involvement:

        assignee = DossierRoleSchema(
            uuid=involvement.role.uuid,
            account_key=involvement.role.account.key,
            key=involvement.role.key,
            name_de=involvement.role.name_de,
            name_en=involvement.role.name_en,
            name_fr=involvement.role.name_fr,
            name_it=involvement.role.name_it,
            user_selectable=involvement.role.user_selectable,
            show_separate_filter=involvement.role.show_separate_filter,
            user_uuid=involvement.user.uuid,
            user_first_name=involvement.user.user.first_name,
            user_last_name=involvement.user.user.last_name,
            user_username=involvement.user.user.username,
        )

    return DossierPropertiesResponse(
        name=dossier.name,
        businesscase_type_uuid=dossier.businesscase_type_id,
        assignee=assignee,
        expiry_date=dossier.expiry_date,
        max_expiry_date=dossier.max_expiry_date,
        # dossier_language=str(dossier.lang),
    )
