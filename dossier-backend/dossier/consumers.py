import json
from time import time
from typing import Optional
from uuid import UUID

import nanoid
import structlog
from channels.db import database_sync_to_async
from channels.generic.websocket import AsyncWebsocketConsumer
from django.http import Http404
from structlog.contextvars import bound_contextvars

from dossier import jwt_extract
from dossier.helpers_access_check import (
    get_dossier_with_access_check,
    check_account_specific_access_check,
)
from dossier.models import Dossier
from dossier.schemas import DateRange
from dossier_zipper.dossier_zipper_api import generate_offline_dossier

logger = structlog.get_logger(__name__)


class ExportDossierConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(args, kwargs)
        self.date_range = None
        self.dossier_name = None
        self.dossier_uuid = None

    async def connect(self):
        with bound_contextvars(connection_id=nanoid.generate(size=5)):
            self.dossier_uuid = self.scope["url_route"]["kwargs"]["dossier_uuid"]
            self.dossier_name = "dossier_%s" % self.dossier_uuid
            self.date_range = DateRange(
                from_date=self.scope["url_route"]["kwargs"].get("from_date"),
                to_date=self.scope["url_route"]["kwargs"].get("to_date"),
            )

            await logger.ainfo(
                "dossier export requested",
                dossier_uuid=self.dossier_uuid,
                date_range=self.date_range,
            )
            await self.accept()
            await logger.ainfo("connection accepted")
            await self._prepare_download_link()
            await self.close()

    async def disconnect(self, close_code):
        await logger.ainfo("connection closed", close_code=close_code)

    async def _prepare_download_link(self):
        start = time()
        try:
            dossier = await database_sync_to_async(check_account_specific_access_check)(
                self.scope["jwt"],
                self.dossier_uuid,
                dossier_user=self.scope["dossieruser"],
                is_manager=self.scope["is_manager"],
            )

            if dossier is not None:
                await logger.ainfo(
                    "got dossier from account specific access check",
                    dossier_uuid=str(dossier.uuid),
                )
            else:
                dossier = await self.check_user_has_access_to_dossier(self.dossier_uuid)

            if dossier is not None:
                dossier_export = await generate_offline_dossier(
                    dossier_uuid=self.dossier_uuid,
                    add_uuid_suffix=dossier.account.document_download_ui_add_uuid_suffix,
                    add_metadata_json=dossier.account.enable_download_metadata_json,
                    date_range=self.date_range,
                )

                await self.send(
                    text_data=json.dumps({"location": dossier_export.file.fast_url})
                )
                await logger.ainfo("export success")

            else:
                message = {
                    "notification": "Dossier with such ID does not exist, or you're not its owner."
                }
                await logger.ainfo("export failed", message=message)
                await self.send(text_data=json.dumps(message))
        finally:
            await logger.ainfo("dossier export finished", duration=time() - start)

    @database_sync_to_async
    def check_user_has_access_to_dossier(self, dossier_uuid: UUID) -> Optional[Dossier]:
        try:
            dossier = get_dossier_with_access_check(
                dossier_user=self.scope["dossieruser"],
                is_manager=self.scope["is_manager"],
                dossier_uuid=dossier_uuid,
                dossier_qs=Dossier.objects.select_related("account"),
                external_dossier_id=jwt_extract.get_external_dossier_id(
                    self.scope["jwt"]
                ),
            )
            return dossier
        except Http404:
            # We assume this is a HttpException 404 because access not allowed
            return None
