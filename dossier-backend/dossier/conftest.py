import pytest
from django.apps import apps


from django.contrib.auth.models import AbstractUser
from django.contrib.auth import get_user_model
from django.core.files.base import ContentFile

from dossier.doc_cat_helpers import load_document_categories_from_path


from doccheck.tests.test_services import create_doc_check
from dossier.fakes import (
    get_law_files,
    add_some_fake_semantic_documents,
    load_initial_document_categories,
)

from dossier.models import (
    Account,
    BusinessCaseType,
    Dossier,
    DossierFile,
    OriginalFile,
    ExtractedFile,
    UserInvolvement,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
    DocumentCategory,
)
from dossier.services import (
    create_dossier,
)
from doccheck.models import BusinessCaseType as DocCheckBusinessCaseType
from processed_file.models import ProcessedPage, PageObject, ProcessedFile
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    SemanticDocumentPageObject,
    AssignedRealEstatePropertySemanticDocument,
)
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)
from statemgmt.models import Status

User: AbstractUser = get_user_model()

models_for_comparison_with_dossier_FK = [
    (DossierFile, "dossier__uuid"),
    (OriginalFile, "dossier__uuid"),
    (ExtractedFile, "dossier__uuid"),
    (ProcessedFile, "dossier__uuid"),
    (ProcessedPage, "dossier__uuid"),
    (SemanticDocument, "dossier__uuid"),
    (SemanticPage, "dossier__uuid"),
    (PageObject, "processed_page__dossier__uuid"),
    (SemanticDocumentPageObject, "semantic_document__dossier__uuid"),
    (UserInvolvement, "dossier__uuid"),
    (
        RealestateProperty,
        "dossier__uuid",
    ),
    (
        AssignedRealestatePropertyOriginalFile,
        "originalfile__dossier__uuid",
    ),
    (
        AssignedRealEstatePropertySemanticDocument,
        "semantic_document__dossier__uuid",
    ),
]


@pytest.fixture
def service_account():
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="service development account",
            default_bucket_name="dms-default-bucket",
            key="serviced",
        ),
        dmf_endpoint="https://www.localhost",
    )
    return account


@pytest.fixture
def load_document_categories(service_account):
    return load_document_categories_from_path(service_account)


def get_models_from_app(app_label):
    app_config = apps.get_app_config(app_label)
    return app_config.get_models()


def create_synthetic_dossier(
    external_id="test external id", account: Account = None, user=None
):
    # Factor this out of a fixture, as we want to call it inside freezegun

    # Create a dossier with all relevant fields set,
    # including things specific to BEKB, like business case type
    # and swissfex like external_id
    if user is None:
        user = User.objects.get(username="<EMAIL>")
    doc_check = create_doc_check()

    if account is None:
        account = Account.objects.get(key="default")

        account.active_doc_check = doc_check
        account.save()

    # account = Account.objects.create(
    #     key=ACCOUNT_KEY_DOCCHECK, name="Test Account", active_doc_check=doc_check
    # )
    dossier_business_case_type, _ = BusinessCaseType.objects.get_or_create(
        account=account, key="test business case type"
    )
    DocCheckBusinessCaseType.objects.get_or_create(
        doc_check=doc_check, key=dossier_business_case_type.key
    )
    dossier = create_dossier(
        account,
        "dossier with case",
        "de",
        user,
        businesscase_type_id=dossier_business_case_type.uuid,
    )
    dossier.external_id = external_id

    dossier.save()

    return dossier


@pytest.fixture
def synthetic_dossier():
    return create_synthetic_dossier()


@pytest.fixture
def test_user_1():
    return User.objects.get(username="<EMAIL>")


@pytest.fixture
def dossier_for_export(synthetic_dossier):
    account = synthetic_dossier.account

    load_initial_document_categories(account=synthetic_dossier.account)

    # Ensure that a state machine is created
    created_objects = create_semantic_document_state_machine()

    state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = state_machine
    account.save()
    account.refresh_from_db()
    return synthetic_dossier


def set_intermediate_models(dossier):
    # Set intermediate models used to map real estate properties
    property_entity_mansion = RealestateProperty.objects.create(
        dossier=dossier, key="mansion"
    )

    original_file = OriginalFile.objects.filter(dossier=dossier).first()

    AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=original_file, realestate_property=property_entity_mansion
    )

    AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=OriginalFile.objects.filter(dossier=dossier).last(),
        realestate_property=property_entity_mansion,
    )

    semantic_document = SemanticDocument.objects.filter(dossier=dossier).first()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=semantic_document, realestate_property=property_entity_mansion
    )


def assert_dossier_fields(new_dossier: Dossier, dossier: Dossier):
    # Test identical fields
    assert new_dossier.name == dossier.name
    assert new_dossier.work_status == dossier.work_status
    assert new_dossier.account == dossier.account
    assert new_dossier.owner == dossier.owner
    assert new_dossier.access_mode == dossier.access_mode
    assert new_dossier.work_status == dossier.work_status

    assert new_dossier.lang == dossier.lang
    assert new_dossier.bucket == dossier.bucket
    assert new_dossier.note == dossier.note
    assert new_dossier.photos_sorting == dossier.photos_sorting
    assert new_dossier.businesscase_type == dossier.businesscase_type

    # Test non identical fields
    assert new_dossier.uuid != dossier.uuid
    assert new_dossier.created_at >= dossier.created_at
    assert new_dossier.updated_at >= dossier.updated_at

    if dossier.doccheck_case:
        assert new_dossier.doccheck_case != dossier.doccheck_case
    else:
        assert new_dossier.doccheck_case is None


def dossier_file_mock(dossier_uuid, bucket, file_url):
    img_file, pdf_file, txt_file = get_law_files()

    content = pdf_file.read_bytes()
    data = ContentFile(content=content, name="dummy_vbv.pdf")

    d = DossierFile(dossier_id=dossier_uuid, bucket=bucket, data=data)
    return d


@pytest.fixture(scope="session")
def mock_jwks_public():
    # JWKS with only public keys
    return {
        "e": "AQAB",
        "n": "32ZC0fZ2JvbYQ-XCkDRt-xZqmxEbkjq4j9iSBUThreBm1ZmokBPq-I9cgmBKcARvLS3XBSluUnbUuYH3E1-DC9XRWS9SCK65INDiPLJPSVN7XhBE1bgPiWsCqbS37zoF3lO-QjjWaWI_3gTtJx1elbGC3JF6slWLhSpx-3EyszGSqCxHV_zbjNGYwVWSlPHnl4zJhvt4ft0YPipDWxmA9vq1qchoXJj1yIuVmfskiYx-MLsU-kL83YabT_dJPvLbX6K8_h49lBJiEog8DGSwanDreGH-UvhtyOrb-1wZM_FpWoBKVf1ymQFXco1VtToBPdUfnPry3Gxb_UL1eF-FGw",
        "alg": "RS256",
        "kid": "swissfexd",
        "kty": "RSA",
        "use": "sig",
    }


def prepare_test_dossier_bekb():
    dossier = Dossier.objects.get(name="Dossier von Francesca Eichenberger")
    dossier.work_status = Status.objects.get(
        state_machine=dossier.account.active_work_status_state_machine,
        key="IN_FRONT_OFFICE",
    )
    User = get_user_model()
    dossier.owner, _ = User.objects.get_or_create(username="<EMAIL>")
    dossier.save()
    return dossier.account, dossier, dossier.owner


def create_synthetic_dossier_performance_testing():

    user = User.objects.get(username="<EMAIL>")

    dossier, created = Dossier.objects.get_or_create(
        name="Test Dossier for Profiling",
        defaults=dict(account=Account.objects.get(key="default"), owner=user),
    )

    if created:

        document_category = DocumentCategory.objects.filter(
            account=dossier.account
        ).first()

        sem_docs = add_some_fake_semantic_documents(
            dossier,
            num_docs=50,
            allow_empty_docs=False,
            valid_document_category_keys=[document_category.name],
            max_pages=10,
            min_num_pages=10,
            no_page_objects_per_page=10,
        )

    else:
        sem_docs = list(dossier.semantic_documents.all())

    return dossier, sem_docs
