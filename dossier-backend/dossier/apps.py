from django.apps import AppConfig


class DossierConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "dossier"

    def ready(self):
        super().ready()
        # Implicitly connect signal handlers decorated with @receiver.
        from . import signals  # noqa: F401

        # Do lazy imports here to avoid the error AppRegistryNotReady("Apps aren't loaded yet.")
        from dossier.dossier_access_external import (
            initialize_default_dossier_access_check_providers,
        )

        initialize_default_dossier_access_check_providers()
