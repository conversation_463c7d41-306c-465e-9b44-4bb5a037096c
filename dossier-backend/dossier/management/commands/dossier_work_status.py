import djclick as click
import structlog

from dossier.dossier_work_status import (
    DOSSIER_STATE_MACHINE_NAME,
    PATH_DOSSIER_STATE_MACHINE_EXPORT,
    initialize_dossier_work_status,
)
from dossier.models import Account
from statemgmt.export import update_state_machine

logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key", type=click.STRING)
def update_dossier_state_machine(account_key: str):
    """
    Load / update state machine for dossiers. This updates an existing state machine without
    breaking its existing states and dossiers referencing those states.

    python manage.py dossier_work_status update-dossier-state-machine bcgeevo

    @param account_key:
    @return:
    """
    logger.info("create state machine for dosssiers")

    if account_key:
        account = Account.objects.get(key=account_key)

        state_machine_name = DOSSIER_STATE_MACHINE_NAME
        p = PATH_DOSSIER_STATE_MACHINE_EXPORT
        assert p.exists(), f"Could not find state machine export file with path={p}"
        state_machine = update_state_machine(p, state_machine_name)
        assert state_machine is not None
        account.active_work_status_state_machine = state_machine
        account.save()
        logger.info(
            "activate semantic document state machine for account",
            account=account,
            state_machine=state_machine.name,
        )
        initialize_dossier_work_status(account)
    else:
        logger.error("Could not find account", account_key=account_key)


@grp.command()
@click.argument("account_key", type=click.STRING)
def initialize(account_key: str):
    """
    Initialize dossier.work_status to the initial state of the dossier state machine for all dossiers
    that do not have a work_status set.

    python manage.py dossier_work_status initialize --account_key default

    python manage.py dossier_work_status initialize --account_key bcgeevo
    """

    # Get account
    account = Account.objects.get(key=account_key)
    initialize_dossier_work_status(account)
