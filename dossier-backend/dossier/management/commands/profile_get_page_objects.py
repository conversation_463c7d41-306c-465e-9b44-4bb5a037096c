import structlog

import djc<PERSON> as click
from django.conf import settings
from django.urls import reverse
from django.utils import timezone

from core.authentication import create_token, AuthenticatedClient
from dossier.conftest import create_synthetic_dossier_performance_testing

logger = structlog.get_logger()

KEYCLOAK_PRIVATE_KEY = """**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""


@click.command()
def profile_get_page_objects():
    dossier, semantic_documents = create_synthetic_dossier_performance_testing()

    settings.KEYCLOAK_PRIVATE_KEY = KEYCLOAK_PRIVATE_KEY
    settings.ALLOWED_HOSTS = ["*"]

    token = create_token(
        "testuser", "testuser", "<EMAIL>", account_key="default"
    )

    testuser1_client = AuthenticatedClient(token)

    start_time = timezone.now()
    # with silk_profile(name="Dossier Get all Page Objects Profile"):
    testuser1_client.get(
        reverse("api:dossier-all-page-objects", kwargs={"dossier_uuid": dossier.uuid})
    )
    elapsed_time = timezone.now() - start_time
    logger.info("Time taken for dossier-all-page-objects", duration=elapsed_time)

    # start_time = timezone.now()
    # with silk_profile(name="Dossier Get Aggregate Page Objects Profile"):
    #     response = testuser1_client.get(
    #         reverse(
    #             "api:dossier-aggregate-page-objects",
    #             kwargs={"dossier_uuid": dossier.uuid},
    #         )
    #     )
    # elapsed_time = timezone.now() - start_time
    # print(f"Time taken for dossier-aggregate-page-objects: {elapsed_time} seconds")
    #
    # with silk_profile(name="Dossier Get Data V2 Profile"):
    #     start_time = timezone.now()
    #     response = testuser1_client.get(
    #         reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    #     )
    #     elapsed_time = timezone.now() - start_time
    #     print(f"Time taken for get-dossier-data-v2: {elapsed_time} seconds")
