import structlog

import djclick as click

from dossier.models import <PERSON><PERSON><PERSON>, Account
from statemgmt.services import (
    log_work_status_transitions,
    check_work_status_transition_consistency,
)

logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.option("--dry-run", is_flag=True)
def make_assignee_unique(dry_run):
    for dossier in Dossier.objects.all():
        user_involvments = list(
            dossier.userinvolvement_set.filter(role__key="ASSIGNEE")
            .order_by("-created_at")
            .all()
        )
        if len(user_involvments) > 1:
            logger.error(
                "dossier has multiple ASSIGNEE",
                dossier={dossier.name},
                user_involvements=user_involvments,
            )
            for user_involvemnt in user_involvments[1:]:
                print(
                    user_involvemnt.role.key,
                    user_involvemnt.user.user.username,
                    user_involvemnt.created_at,
                )
                if not dry_run:
                    user_involvemnt.delete()
                    print("deleted")


@grp.command()
@click.argument("dossier_uuid", type=click.STRING, default="")
def show_work_status_transitions(dossier_uuid: str):
    """
    Show all the transitions of the work_status of a dossier over time
    Example:

    python manage.py dossier show-work-status-transitions 14f9faa8-e149-4c22-9315-3a66b984855a

    @param dossier_uuid:
    @return:
    """
    log_work_status_transitions(dossier_uuid, context="show_work_status_transitions")


@grp.command()
@click.argument("account_key", type=click.STRING, default="")
def check_work_status_consistency(account_key: str):
    """
    Check if the transitions are consistent for all dossiers
    Example:

    python manage.py dossier check-work-status-consistency bekbp

    @param account_key:
    @return:
    """

    account = Account.objects.get(key=account_key)
    assert account
    dossiers = Dossier._base_manager.filter(account=account)

    for index, d in enumerate(dossiers):
        list_of_transitions, events, inconsistent_event = (
            check_work_status_transition_consistency(d.uuid)
        )
        if inconsistent_event:
            logger.error(
                "Found inconsistent transitions:",
                list_of_transitions=list_of_transitions,
                dossier_uuid=d.uuid,
            )
        if index % 100 == 0:
            logger.info(f"{index}/{len(dossiers)}...")
