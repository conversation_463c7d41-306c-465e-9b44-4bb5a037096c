import logging
import structlog
import urllib.parse
from email.utils import parseaddr
from typing import Optional

import djclick as click
import pika
import pika.exceptions
import requests
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.core.files.base import ContentFile
from django.db import transaction
from pika.channel import Channel
from pydantic import BaseModel

from dossier.helpers import update_dossier_expired_date_by_account
from dossier.models import Dossier, Languages, Account, DossierFile, OriginalFile
from dossier.tasks import process_original_file
from django.conf import settings
from projectconfig.settings import HARDCODED_NAME_OF_HYPPODOSSIER_ACCOUNT

logger = structlog.get_logger()


class S3Mail(BaseModel):
    imap_host: str
    imap_username: str

    mail_from: str
    mail_bucket: str
    mail_object_name: str

    mail_subject: Optional[str] = None
    mail_url: Optional[str] = None


def on_message(
    channel: Channel,
    method: pika.spec.Basic.Deliver,
    properties: pika.spec.BasicProperties,
    body: bytes,
):
    mail_request = S3Mail.model_validate_json(body)

    logger.info(f"getting mail request {mail_request}")

    assert mail_request.mail_subject
    assert mail_request.mail_url

    try:
        with transaction.atomic():
            email = parseaddr(mail_request.mail_from)[1]
            owner = get_user_model().objects.get(email=email)
            account = Account.objects.get(name=HARDCODED_NAME_OF_HYPPODOSSIER_ACCOUNT)

            dossier = Dossier.objects.create(
                name=mail_request.mail_subject,
                lang=Languages.GERMAN,
                owner=owner,
                account=account,
                bucket=account.default_bucket_name,
            )

            update_dossier_expired_date_by_account(dossier)

            filename = urllib.parse.urlparse(mail_request.mail_url).path.rpartition(
                "/"
            )[-1]
            response = requests.get(mail_request.mail_url)
            response.raise_for_status()

            dossier_file = DossierFile.objects.create(
                dossier=dossier,
                data=ContentFile(response.content, filename),
                bucket=dossier.bucket,
            )
            original_file = OriginalFile.objects.create(
                dossier=dossier, file=dossier_file
            )
            process_original_file(original_file)
    except ObjectDoesNotExist:
        logger.exception("warning user does not exists")
    channel.basic_ack(delivery_tag=method.delivery_tag)


def consume():
    connection = pika.BlockingConnection(pika.URLParameters(settings.RABBIT_URL))
    channel = connection.channel()
    channel.basic_qos(prefetch_count=1)

    channel.queue_declare(
        settings.ASYNC_MAIL_CONSUMER_QUEUE_NAME,
        durable=True,
        arguments={"x-queue-type": "quorum"},
    )
    channel.basic_consume(settings.ASYNC_MAIL_CONSUMER_QUEUE_NAME, on_message)

    try:
        channel.start_consuming()
    except KeyboardInterrupt:
        channel.stop_consuming()
        connection.close()
    except pika.exceptions.ConnectionClosedByBroker:
        # Uncomment this to make the example not attempt recovery
        # from server-initiated connection closure, including
        # when the node is stopped cleanly
        # except pika.exceptions.ConnectionClosedByBroker:
        #     pass
        pass


@click.command()
def start():
    logging.basicConfig(level=logging.INFO)
    consume()
