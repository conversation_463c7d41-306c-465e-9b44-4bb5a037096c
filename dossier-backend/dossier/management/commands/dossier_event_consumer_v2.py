import asyncio
import json
import uuid
import platform
from multiprocessing.pool import Thr<PERSON><PERSON><PERSON>
from pathlib import Path
from time import time
from typing import Optional, Dict, Any

import aio_pika
import djclick as click
import structlog
from actorizer import Broker as PersistentBrokerConnection
from channels.db import database_sync_to_async
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from django.utils.timezone import now
from pydantic import BaseModel
from structlog.contextvars import bound_contextvars

from dossier.doc_cat_helpers import (
    get_or_create_document_category_by_name_from_processing,
    get_or_create_page_category_by_name_from_processing,
)
from dossier.helpers import get_hypodossier_exception_from_string
from dossier.helpers_model_copier import copy_dossier_models
from dossier.helpers_timezone import log_timezone_info
from dossier.models import (
    Dossier,
    DossierFile,
    OriginalFile,
    ExtractedFile,
    FileStatus,
    FileException,
    ProcessedPageCount,
    DocumentCategory,
    Account,
    ExceptionDetails,
    DossierCopyStatus,
    DossierExport,
)
from dossier.monitoring import check_file_statuses
from dossier.schemas import (
    DocumentProcessingResult,
    DossierCopyRequest,
    DossierCopyContentsIntoExistingDossierRequest,
)
from dossier.services import assign_real_estate_property_to_semantic_document
from dossier.services_event_consumer import create_dossier_file_from_url
from dossier_zipper.schemas import DossierZipResponseV1
from processed_file.models import (
    PageObjectTitle,
    PageObjectType,
    PageObject,
    ProcessedFile,
    ProcessedPage,
)
from projectconfig.settings import (
    DOSSIER_EVENT_CONSUMER_PREFETCH,
    DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD,
    DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE,
    ZKB_VBV_DOCUMENT_CATEGORY_KEY,
    ADD_NEW_DOCUMENT_CATEGORIES_FROM_PROCESSING,
)
from semantic_document.helpers import get_start_work_status_from_dossier_account
from semantic_document.models import (
    SemanticPagePageObject,
    SemanticDocumentPageObject,
    SemanticDocument,
    SemanticPage,
)
from semantic_document.schemas import SemanticDocumentPDFResponseV1
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
    create_context_for_semantic_document_state_transition,
)
from statemgmt.models import Status, StateMachine
from statemgmt.services import validate_state_transition, StateTransitionError
from workers.models import SemanticDocumentExport

logger = structlog.get_logger()


class FileExtractedV1(BaseModel):
    dossier_uuid: uuid.UUID
    original_file_uuid: uuid.UUID
    file_url: str
    path_from_original: str
    extracted_file_uuid: uuid.UUID


class OriginalFileProcessingErrorSchema(BaseModel):
    original_file_uuid: uuid.UUID
    exception_de: Optional[str] = None
    exception_en: Optional[str] = None
    exception_fr: Optional[str] = None
    exception_it: Optional[str] = None
    exception_details: Optional[str] = None


class FileExtractedExceptionV1Schema(BaseModel):
    dossier_uuid: uuid.UUID
    extracted_file_uuid: uuid.UUID
    type: str
    de: str
    en: str
    fr: Optional[str] = None
    it: Optional[str] = None
    exception_type: Optional[str] = None
    details: Optional[str] = None


def add_extracted_file(body):
    """
    Create DossierFile and ExtractedFile in DB and copy file from processing zone to backend.
    ExtractedFile is afterward PROCESSING.
    """

    file_extracted = FileExtractedV1.model_validate_json(body)

    dossier_uuid = file_extracted.dossier_uuid

    with bound_contextvars(
        dossier_uuid=str(dossier_uuid),
        extracted_file_uuid=str(file_extracted.extracted_file_uuid),
    ):
        if ExtractedFile.objects.filter(
            uuid=file_extracted.extracted_file_uuid
        ).exists():
            logger.warning(
                f"The extracted file {file_extracted.path_from_original} with uuid {file_extracted.extracted_file_uuid}"
                f" already exists in the dossier {dossier_uuid}"
            )
            return

        file_url = file_extracted.file_url
        dossier = Dossier.get_by_uuid(uuid=dossier_uuid)

        dossier_file = create_dossier_file_from_url(
            dossier.uuid, dossier.bucket, file_url
        )

        logger.info("saving result")
        with transaction.atomic():
            dossier_file.save()
            logger.info("saved dossier file", dossier_file_uuid=str(dossier_file.uuid))

            original_file = OriginalFile.objects.get(
                uuid=file_extracted.original_file_uuid, dossier=dossier
            )

            ExtractedFile.objects.create(
                uuid=file_extracted.extracted_file_uuid,
                dossier=dossier,
                original_file=original_file,
                file=dossier_file,
                path_from_original=file_extracted.path_from_original,
            )

            logger.info("created extracted file")


def add_file_exception(body):
    """
    Creates or updates FileException  and ExtractedFile in DB and copy file from processing zone to backend.
    ExtractedFile is afterward PROCESSED.
    """

    data = FileExtractedExceptionV1Schema.model_validate_json(body).model_dump()

    with transaction.atomic():
        dossier = Dossier.get_by_uuid(uuid=data.get("dossier_uuid"))
        extracted_file = ExtractedFile.objects.get(uuid=data.get("extracted_file_uuid"))

        file_exception, _ = FileException.objects.get_or_create(
            dossier=dossier,
            extracted_file=extracted_file,
        )

        file_exception.type = data.get("type")
        file_exception.de = data.get("de")
        file_exception.en = data.get("en")
        file_exception.fr = data.get("fr") if data.get("fr") else data.get("de")
        file_exception.it = data.get("it") if data.get("it") else data.get("de")

        ex_type = data.get("exception_type", None)
        if ex_type:
            file_exception.exception_type = get_hypodossier_exception_from_string(
                ex_type
            )
            if (
                file_exception.exception_type
                == ExceptionDetails.HypoDossierException.UNMAPPED_EXCEPTION.value
            ):
                logger.warning(
                    "Unmapped exception type found. Use UNKNOWN_EXCEPTION instead.",
                    dossier_uuid=dossier.uuid,
                    extracted_file=extracted_file.uuid,
                    ex_type=ex_type,
                )
        file_exception.details = data.get("details")
        file_exception.save()

        extracted_file.status = FileStatus.ERROR
        extracted_file.save()


def handle_unknown_document_category(
    found_doc_cat,
    sddc: DocumentCategory,
    account: Account,
    semantic_document: SemanticDocument,
    extracted_file: ExtractedFile,
) -> bool:
    """
        If the account does not know the document category of the semantic document set doc cat to UNKNOWN
        and use title suffix from extracted file.
        Else do nothing.

    @param found_doc_cat:
    @param sddc:
    @param account:
    @param semantic_document:
    @param extracted_file:
    @return: True if semantic document has been changed
    """
    changed = False

    if not found_doc_cat:
        logger.warning(
            "Processing returned unknown document category for semantic document... map it to UNKNOWN",
            document_category=sddc.name,
            account=account.key,
            semantic_document_uuid=semantic_document.uuid,
        )

        extracted_file_filename = Path(extracted_file.file.name)
        title_suffix = extracted_file_filename.stem
        semantic_document.title_suffix = title_suffix
        changed = True

    return changed


def store_page_object_in_cache(
    page_objects: Dict[Any, PageObject],
    po_id: uuid.UUID,
    alternative_key: Any,
    page_object: PageObject,
):
    if po_id:
        page_objects[po_id] = page_object
    else:
        page_objects[alternative_key] = page_object


def get_page_object_from_cache(
    page_objects: Dict[Any, PageObject], po_id: uuid.UUID, alternative_key: Any
):
    if po_id:
        return page_objects[po_id]
    else:
        return page_objects[alternative_key]


def add_processing_result(
    body,
    zkb_vbv_use_extracted_file: bool = settings.ZKB_VBV_USE_EXTRACTED_FILE,
    allow_create_doc_cat: bool = ADD_NEW_DOCUMENT_CATEGORIES_FROM_PROCESSING,
    allow_create_page_cat: bool = True,
):
    document_processing_result = DocumentProcessingResult.model_validate_json(body)

    with bound_contextvars(dossier_uuid=str(document_processing_result.dossier_uuid)):
        dossier = Dossier.get_by_uuid(uuid=document_processing_result.dossier_uuid)

        extracted_file = ExtractedFile.objects.get(
            uuid=document_processing_result.processed_file.extracted_file_uuid,
            dossier=dossier,
        )

        if extracted_file.status in [FileStatus.PROCESSED, FileStatus.ERROR]:
            logger.warning("extracted file already processed")
            return

        start = time()
        logger.info(
            "Create dossier_files for processing result...", dossier_uuid=dossier.uuid
        )
        urls = []
        for (
            processed_page_number,
            processed_page_request,
        ) in document_processing_result.processed_file.pages.items():
            urls.append(processed_page_request.image_file_url)
            urls.append(processed_page_request.searchable_pdf_file_url)
            urls.append(processed_page_request.searchable_txt_file_url)

        with ThreadPool(
            processes=DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD
        ) as pool:

            def _create_dossier_file(file_url):
                start_f = now()
                dossier_file = create_dossier_file_from_url(
                    dossier.uuid, dossier.bucket, file_url
                )
                end_f = now()
                logger.info(
                    "Created dossier_file",
                    filename=dossier_file.data.name,
                    duration=(end_f - start_f).total_seconds(),
                )
                return file_url, dossier_file

            result = list(pool.imap(_create_dossier_file, urls))

        dossier_files = {url: dossier_file for url, dossier_file in result}

        with transaction.atomic():
            # 221213 mt: There could be multiple accounts. But the dossier has been assigned to a specific account
            # already. Use that account to select the correct document category
            account = dossier.account

            end = time()
            logger.info(
                "created dossier_files for processing result",
                count=len(urls),
                duration=end - start,
            )

            start = now()
            DossierFile.objects.bulk_create(
                [dossier_file for _, dossier_file in result]
            )
            end = now()
            logger.info(
                "saved result to database", duration=(end - start).total_seconds()
            )

            processed_file = ProcessedFile.objects.create(
                dossier_id=extracted_file.dossier_id, extracted_file=extracted_file
            )

            processed_pages = {}
            page_objects = {}

            logger.info("start adding processed pages")

            for (
                processed_page_number,
                processed_page_request,
            ) in document_processing_result.processed_file.pages.items():
                image = dossier_files[processed_page_request.image_file_url]
                searchable_pdf = dossier_files[
                    processed_page_request.searchable_pdf_file_url
                ]
                searchable_txt = dossier_files[
                    processed_page_request.searchable_txt_file_url
                ]

                # Processed page doc cat
                ppdc = processed_page_request.document_category
                (
                    document_category,
                    _,
                ) = get_or_create_document_category_by_name_from_processing(
                    ppdc.name, ppdc, account, allow_create=allow_create_doc_cat
                )

                (
                    page_category,
                    found_pc,
                ) = get_or_create_page_category_by_name_from_processing(
                    page_category_id=processed_page_request.page_category.id,
                    page_category_name=processed_page_request.page_category.name,
                    allow_create=allow_create_page_cat,
                )

                # TODO:add confidence value
                processed_page = ProcessedPage.objects.create(
                    uuid=processed_page_request.uuid,
                    processed_file=processed_file,
                    dossier=dossier,
                    number=processed_page_request.number,
                    lang=processed_page_request.lang,
                    document_category=document_category,
                    page_category=page_category,
                    image=image,
                    searchable_pdf=searchable_pdf,
                    searchable_txt=searchable_txt,
                )
                processed_pages[processed_page.uuid] = processed_page

                for page_object_request in processed_page_request.page_objects:
                    page_object = add_page_object_processed_page(
                        page_object_request, processed_page
                    )
                    store_page_object_in_cache(
                        page_objects,
                        page_object_request.uuid,
                        page_object_request,
                        page_object,
                    )

            end = now()
            logger.info(
                "processed pages",
                duration=(end - start).total_seconds(),
                count=len(document_processing_result.processed_file.pages),
            )

            start_work_status = get_start_work_status_from_dossier_account(
                dossier=dossier
            )
            for (
                semantic_document_request
            ) in document_processing_result.semantic_documents:
                # Semantic document doc cat
                sddc = semantic_document_request.document_category

                # name must be unique, we do not rely on id as that may be different in different environments
                (
                    document_category,
                    found_doc_cat,
                ) = get_or_create_document_category_by_name_from_processing(
                    sddc.name, sddc, account, allow_create=allow_create_doc_cat
                )

                semantic_document: SemanticDocument = SemanticDocument.objects.create(
                    dossier=dossier,
                    document_category=document_category,
                    confidence_level=semantic_document_request.confidence_level,
                    confidence_formatted=semantic_document_request.confidence_formatted,
                    confidence_value=semantic_document_request.confidence_value,
                    # 240521 mt: remove legacy feature title_custom. It does not help if document exists in doc
                    # catalogue and it causes problems if the document category does not exist and the document is
                    # re-assigned to UNKNOWN
                    # title_custom=semantic_document_request.title,
                    title_suffix=semantic_document_request.title_suffix,
                    work_status=start_work_status,
                )

                for semantic_page_request in semantic_document_request.semantic_pages:
                    # Semantic page doc cat
                    spdc = semantic_page_request.document_category
                    (
                        document_category,
                        _,
                    ) = get_or_create_document_category_by_name_from_processing(
                        spdc.name, spdc, account, allow_create=allow_create_doc_cat
                    )

                    (
                        page_category,
                        found_pc,
                    ) = get_or_create_page_category_by_name_from_processing(
                        page_category_id=semantic_page_request.page_category.id,
                        page_category_name=semantic_page_request.page_category.name,
                        allow_create=allow_create_page_cat,
                    )

                    processed_page = processed_pages[
                        semantic_page_request.processed_page_uuid
                    ]
                    semantic_page = SemanticPage.objects.create(
                        dossier=dossier,
                        semantic_document=semantic_document,
                        number=semantic_page_request.number,
                        page_category=page_category,
                        document_category=document_category,
                        lang=semantic_page_request.lang,
                        processed_page=processed_page,
                        confidence_level=semantic_page_request.confidence_level,
                        confidence_formatted=semantic_page_request.confidence_formatted,
                        confidence_value=semantic_page_request.confidence_value,
                    )

                    # All page objects in semantic pages should aready exist in a corresponding processed page
                    for (
                        semantic_page_object_request
                    ) in semantic_page_request.page_objects:
                        try:
                            page_object = get_page_object_from_cache(
                                page_objects,
                                semantic_page_object_request.uuid,
                                semantic_page_object_request,
                            )
                        except:
                            # This case should never happen as all page objects on
                            # semantic pages should exist on a processed page
                            # TODO: better throw exception here?
                            logger.warning(
                                "Could not find page object for semantic page -> recreate",
                                sem_page_object_request=semantic_page_object_request,
                            )
                            page_object_request = semantic_page_object_request
                            page_object = add_page_object_processed_page(
                                page_object_request, processed_page
                            )
                            logger.warning(
                                "Recreated page object", page_object=page_object
                            )
                            store_page_object_in_cache(
                                page_objects,
                                page_object_request.uuid,
                                page_object_request,
                                page_object,
                            )

                        SemanticPagePageObject.objects.create(
                            semantic_page=semantic_page, page_object=page_object
                        )

                for (
                    aggregated_page_object
                ) in semantic_document_request.aggregated_objects:
                    po = get_page_object_from_cache(
                        page_objects,
                        aggregated_page_object.uuid,
                        aggregated_page_object,
                    )
                    SemanticDocumentPageObject.objects.create(
                        semantic_document=semantic_document,
                        page_object=po,
                    )

                # Handle assignment of real estate properties from original file to semantic documents
                # Real estate properties can be added to the original file when uploaded via the API
                assign_real_estate_property_to_semantic_document(semantic_document)

                last_page = semantic_page
                extracted_file = last_page.processed_page.processed_file.extracted_file

                save_original = handle_original_file_force_parameters(
                    account, dossier, semantic_document, extracted_file
                )

                save_unknown = handle_unknown_document_category(
                    found_doc_cat, sddc, account, semantic_document, extracted_file
                )

                save_zkb_vbv = handle_zkb_vbv(
                    zkb_vbv_use_extracted_file, semantic_document, extracted_file
                )

                if save_original or save_unknown or save_zkb_vbv:
                    semantic_document.save()

            extracted_file.status = FileStatus.PROCESSED
            extracted_file.save()

            ProcessedPageCount.objects.create(
                account=account,
                dossier_uuid=dossier.uuid,
                external_dossier_id=dossier.external_id,
                original_file_uuid=extracted_file.original_file.uuid,
                extracted_file_uuid=extracted_file.uuid,
                extracted_file_path_from_original=extracted_file.path_from_original,
                processed_pages=len(document_processing_result.processed_file.pages),
                owner=dossier.owner.username,  # TODO: change to file upload owner
            )


def handle_zkb_vbv(
    zkb_vbv_use_extracted_file: bool,
    semantic_document: SemanticDocument,
    extracted_file,
) -> bool:
    save = False
    if zkb_vbv_use_extracted_file:
        if semantic_document.document_category.name == ZKB_VBV_DOCUMENT_CATEGORY_KEY:
            extension = Path(extracted_file.file.name).suffix.lower()
            # This special type of document should be read only
            # Also when downloading we want to download the original file
            # instead of the semantic document
            if extension.lower() == ".pdf":
                semantic_document.access_mode = (
                    SemanticDocument.SemanticDocumentAccessMode.READ_ONLY
                )
                save = True
    return save


def handle_original_file_force_parameters(
    account: Account,
    dossier: Dossier,
    semantic_document: SemanticDocument,
    extracted_file,
) -> bool:
    save = False
    original_file = extracted_file.original_file
    if original_file.force_external_semantic_document_id:
        # Optionally assign external_semantic_document_id
        exists = SemanticDocument.objects.filter(
            dossier=dossier,
            external_semantic_document_id=original_file.force_external_semantic_document_id,
        ).count()
        if exists:
            logger.error(
                "Tried to force assignment of unique id but id already exists in dossier. Ignore it.",
                account=account,
                dossier_name=dossier.name,
                original_file_uuid=original_file.uuid,
                of_force_external_semantic_document_id=original_file.force_external_semantic_document_id,
            )
        else:
            semantic_document.external_semantic_document_id = (
                original_file.force_external_semantic_document_id
            )
            save = True
            logger.info(
                "Force for semantic_document: external_semantic_document_id",
                semantic_document_id=semantic_document.external_semantic_document_id,
                of_force_external_semantic_document_id=original_file.force_external_semantic_document_id,
                original_file_uuid=original_file.uuid,
            )
    if original_file.force_access_mode:
        semantic_document.access_mode = original_file.force_access_mode
        save = True
        logger.info(
            "Force for semantic_document: access_mode",
            semantic_document_uuid=semantic_document.uuid,
            access_mode=semantic_document.access_mode,
        )
    if original_file.force_semantic_document_custom_attribute:
        semantic_document.custom_attribute = (
            original_file.force_semantic_document_custom_attribute
        )
        save = True
        logger.info(
            "Force for semantic_document: custom_attribute",
            semantic_document_uuid=semantic_document.uuid,
            custom_attribute=semantic_document.custom_attribute,
        )
    return save


def add_page_object_processed_page(page_object_request, processed_page):
    page_object_title = PageObjectTitle.objects.get_or_create(
        key=page_object_request.key
    )[0]

    for lang in ["de", "fr", "it", "en"]:
        if getattr(page_object_title, lang) != getattr(
            page_object_request.titles, lang
        ):
            setattr(page_object_title, lang, getattr(page_object_request.titles, lang))
            page_object_title.save()

    type = PageObjectType.objects.get_or_create(name=page_object_request.type)[0]

    return PageObject.objects.create(
        processed_page=processed_page,
        value=page_object_request.value,
        key=page_object_title,
        type=type,
        confidence_value=page_object_request.confidence_value,
        confidence_formatted=page_object_request.confidence_formatted,
        confidence_level=page_object_request.confidence_level,
        ref_height=page_object_request.bbox.ref_height,
        ref_width=page_object_request.bbox.ref_width,
        top=page_object_request.bbox.top,
        left=page_object_request.bbox.left,
        right=page_object_request.bbox.right,
        bottom=page_object_request.bbox.bottom,
        visible=page_object_request.visible,
    )


def backoff_hdlr(details):
    logger.info(
        "Backing off",
        wait=details["wait"],
        tries=details["tries"],
        args=details["args"],
        kwargs=details["kwargs"],
    )


# @backoff.on_exception(backoff.expo, exception=ValueError, on_backoff=backoff_hdlr, max_tries=DOSSIER_EVENT_CONSUMER_FINALIZE_PROCESS_BACKOFF_MAX_TRIES)
def finalize_process(body):
    """
    Message DossierEvent.ProcessOriginalFileFinished. Set status of original file to processed
    """
    data = json.loads(body.decode())
    original_file_uuid = data.get("original_file_uuid")
    has_extracted_files_still_processing = ExtractedFile.objects.filter(
        original_file__uuid=original_file_uuid, status=FileStatus.PROCESSING
    ).exists()
    if has_extracted_files_still_processing:
        logger.info("there are still extracted files with status FileStatus.PROCESSING")
        raise ValueError(
            "there are still extracted files with status FileStatus.PROCESSING"
        )
    with transaction.atomic():
        original_file = OriginalFile.objects.get(uuid=original_file_uuid)
        original_file.status = FileStatus.PROCESSED
        original_file.save()


def set_semantic_document_export_done(body):
    """
    Set the done field of the SemanticDocumentExport model to the current time
    """
    response = SemanticDocumentPDFResponseV1.model_validate_json(body)
    with transaction.atomic():
        semantic_document_export = SemanticDocumentExport.objects.get(
            uuid=response.semantic_document_pdf_request_uuid
        )
        semantic_document_export.done = timezone.now()
        semantic_document_export.save()

        semantic_document: SemanticDocument = semantic_document_export.semantic_document

        semdoc_state_machine: StateMachine = (
            semantic_document.dossier.account.active_semantic_document_work_status_state_machine
        )
        # TODO: move creation and setting of DossierFile here
        # dossier_file: dossier_models.DossierFile = create_dossier_file_without_saving(
        #     dossier, filename
        # )
        # # IMPORTANT: save dossier_file as the create function does not save it to the database
        # dossier_file.save()
        # semantic_document_export.file = dossier_file
        # semantic_document_export.save()

        # If we have enabled the state machine for the account, we should validate the
        # state transition from existing status (Ready for export) to EXPORT_AVAILABLE
        # Else we do NOTHING, also not raise an error as it can be a valid setup to have exports
        # but no state machine
        if semdoc_state_machine:
            try:
                initial_work_state = semantic_document.work_status
                export_available_state = Status.objects.get(
                    key=SemanticDocumentState.EXPORT_AVAILABLE.value,
                    state_machine=semdoc_state_machine,
                )

                validate_state_transition(
                    context=create_context_for_semantic_document_state_transition(),
                    current_status=semantic_document.work_status,
                    next_status=export_available_state,
                )

                # If the validation passes, we can set the state to EXPORT_AVAILABLE
                semantic_document.work_status = export_available_state
                semantic_document.save()

                logger.info(
                    f"Set semantic document {semantic_document.uuid} work status from "
                    f"{initial_work_state.key} to {export_available_state.key}"
                )
            except StateTransitionError as e:
                logger.error(
                    "Could not validate state transition",
                    error=e,
                    semantic_document_export=semantic_document_export,
                    semdoc=semantic_document.uuid,
                    previous_work_status=semantic_document.work_status,
                    target_work_status=export_available_state,
                )
        else:
            # Raise NO but just an info
            msg = f"In set_semantic_document_export_done for semdoc {semantic_document.uuid} no state machine found for account {semantic_document.dossier.account.name}"
            logger.info(msg)


def set_dossier_zipper_export_done(body):
    response = DossierZipResponseV1.model_validate_json(body)
    """Set the done field of the DossierExport model to the current time"""

    with transaction.atomic():
        semantic_document_export = DossierExport.objects.get(
            uuid=response.zip_request_uuid
        )
        semantic_document_export.done = timezone.now()
        semantic_document_export.save()


def copy_dossier(body):
    # Note this will not copy a soft deleted dossier, as Dossier.objects will not find them
    request: DossierCopyRequest = DossierCopyRequest.model_validate_json(body)

    source_dossier: Dossier = Dossier.objects.get(uuid=request.source_dossier_uuid)

    if Dossier.objects.filter(
        account=source_dossier.account, external_id=request.new_external_dossier_id
    ).exists():
        return

    new_instances = copy_dossier_models(
        dossier=source_dossier,
        external_id=request.new_external_dossier_id,
    )

    target_dossier: Dossier = next(iter(new_instances.values()))

    copy_status, _ = DossierCopyStatus.objects.get_or_create(
        defaults={
            "source_dossier_uuid": source_dossier.uuid,
            "target_dossier": target_dossier,
        },
        account=source_dossier.account,
        target_external_id=request.new_external_dossier_id,
    )

    copy_status.done = timezone.now()
    copy_status.save()


def copy_dossier_into_existing_dossier(body):
    # Note this will not copy a soft deleted dossier, as Dossier.objects will not find them
    request: DossierCopyContentsIntoExistingDossierRequest = (
        DossierCopyContentsIntoExistingDossierRequest.model_validate_json(body)
    )

    source_dossier: Dossier = Dossier.objects.get(uuid=request.source_dossier_uuid)

    target_dossier: Dossier = Dossier.objects.get(uuid=request.target_dossier_uuid)

    copy_dossier_models(
        dossier=source_dossier,
        target_dossier=target_dossier,
    )

    if request.include_deleted is False:
        SemanticPage._base_manager.filter(
            dossier=target_dossier, deleted_at__lte=timezone.now()
        ).delete()
        SemanticDocument._base_manager.filter(
            dossier=target_dossier, deleted_at__lte=timezone.now()
        ).delete()

    if request.access_mode:
        SemanticDocument._base_manager.filter(dossier=target_dossier).update(
            access_mode=request.access_mode
        )

    copy_status, _ = DossierCopyStatus.objects.get_or_create(
        defaults={
            "source_dossier_uuid": source_dossier.uuid,
            "target_dossier": target_dossier,
        },
        account=source_dossier.account,
        target_external_id=target_dossier.external_id,
    )

    copy_status.done = timezone.now()
    copy_status.save()


def add_processing_error(body):
    with transaction.atomic():
        ofpe: OriginalFileProcessingErrorSchema = (
            OriginalFileProcessingErrorSchema.model_validate_json(body)
        )

        original_file = OriginalFile.objects.get(uuid=ofpe.original_file_uuid)
        original_file.status = FileStatus.ERROR
        original_file.exception_de = ofpe.exception_de
        original_file.exception_en = ofpe.exception_en
        original_file.exception_fr = ofpe.exception_fr
        original_file.exception_it = ofpe.exception_it
        original_file.exception_details = ofpe.exception_details
        original_file.extractedfile_set.all().delete()
        original_file.save()


dossier_event_handlers = {
    "DossierEvent.FileExtractedV1": database_sync_to_async(
        add_extracted_file, thread_sensitive=False
    ),
    "DossierEvent.FileExtractedExceptionV1": database_sync_to_async(
        add_file_exception, thread_sensitive=False
    ),
    "DossierEvent.FileProcessingExceptionV1": database_sync_to_async(
        add_file_exception, thread_sensitive=False
    ),
    "DossierEvent.ProcessingResult": database_sync_to_async(
        add_processing_result, thread_sensitive=False
    ),
    "DossierEvent.OriginalFileProcessingError": database_sync_to_async(
        add_processing_error, thread_sensitive=False
    ),
    "DossierEvent.ProcessOriginalFileFinished": database_sync_to_async(
        finalize_process, thread_sensitive=False
    ),
    settings.ASYNC_DOSSIER_CONSUMER_SEMANTIC_DOCUMENT_EVENT_MESSAGE_TYPE: database_sync_to_async(
        set_semantic_document_export_done, thread_sensitive=False
    ),
    settings.ASYNC_DOSSIER_CONSUMER_DOSSIER_ZIPPER_EVENT_MESSAGE_TYPE: database_sync_to_async(
        set_dossier_zipper_export_done, thread_sensitive=False
    ),
    settings.ASYNC_DOSSIER_COPY_V1_QUEUE_NAME: database_sync_to_async(
        copy_dossier, thread_sensitive=False
    ),
    settings.ASYNC_DOSSIER_COPY_INTO_EXISTING_DOSSIER_V1_QUEUE_NAME: database_sync_to_async(
        copy_dossier_into_existing_dossier, thread_sensitive=False
    ),
}


async def run_dossier_event_consumer(message: aio_pika.IncomingMessage):
    async with message.process(reject_on_redelivered=True, requeue=True):
        start = time()
        with bound_contextvars(message_type=message.type):
            handler = dossier_event_handlers.get(message.type, None)
            if handler:
                try:
                    logger.info("handling message")
                    await handler(message.body)
                except Exception as e:
                    logger.exception("could not successfully handle message")
                    raise e
            else:
                logger.error("no handler")
            end = time()
            logger.info("message processed", duration=end - start)


@click.command()
def start():

    logger.info("[*] Service started. To exit press CTRL+C")

    asyncio.run(main())


async def main():
    nice = settings.PRETTY_PRINT_LOG
    shared_processors = [
        # Processors that have nothing to do with output,
        # e.g., add timestamps or log level names.
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
    ]
    if nice:
        # Pretty printing when we run in a terminal session.
        # Automatically prints pretty tracebacks when "rich" is installed
        processors = shared_processors + [
            structlog.processors.TimeStamper(fmt="iso", utc=False),
            structlog.dev.ConsoleRenderer(),
        ]
    else:
        # Print JSON when we run, e.g., in a Docker container.
        # Also print structured tracebacks.
        processors = shared_processors + [
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer(),
        ]
    structlog.configure(processors)

    logger.info(
        "Start up DossierEventConsumerV2",
        THREAD_POOL_FILE_UPLOAD=DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD,
        PREFETCH=DOSSIER_EVENT_CONSUMER_PREFETCH,
        DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE=DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE,
    )

    log_timezone_info(logger)

    # Use existing event loop to monitor processed files https://gitlab.com/hypodossier/document-universe/-/issues/314

    check_file_status_task = asyncio.create_task(check_file_statuses())

    broker_connection = PersistentBrokerConnection(
        url=f"{settings.RABBIT_URL}?name=DEC {platform.node()}",
    )

    # await broker_connection.register_basemodel_consumer(
    #     fn=run_dossier_event_consumer,
    #     queue_name=settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME,
    #     prefetch_count=DOSSIER_EVENT_CONSUMER_PREFETCH,
    # )

    # PG: broker_connection.register_basemodel_consumer is the new way of registering consumers,
    # which expect messages derived from
    # pydantic.BaseModel and then attempt to derive the correct pydantic model handler from the message type
    # currently hyextract does not support his and instead sends messages of type
    # aio_pika.message.IncomingMessage, which causes get_first_parameter_by_class to fail.
    # This is why we need to use the old way of registering consumers (broker_connection.register)

    await broker_connection.register(
        run_dossier_event_consumer, settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME
    )

    await broker_connection.start()

    try:

        await asyncio.gather(check_file_status_task)

        # Let the code run forever
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        # If user presses Ctrl+C stop the broker connection
        await broker_connection.stop()
        check_file_status_task.cancel()
        try:
            await check_file_status_task
        except asyncio.CancelledError:
            pass
