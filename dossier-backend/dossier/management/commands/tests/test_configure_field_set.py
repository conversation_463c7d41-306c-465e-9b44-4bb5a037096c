import pytest

from dossier.management.commands.configure_default_field_set import (
    configure_default_field_set_for_account,
    configure_form_tab_for_account,
)
from dossier.models import Account


@pytest.mark.django_db
def test_configure_default_field_set_for_account_success():
    assert Account.objects.get(key="default").cdp_field_set is None
    default_account = configure_default_field_set_for_account(
        account_key="default", field_set_name="hd_internal"
    )
    default_account.save()
    del default_account
    default_account = Account.objects.get(key="default")
    assert default_account.cdp_field_set == "hd_internal"


@pytest.mark.django_db
def test_configure_default_field_set_for_account_fails():
    default_account = configure_default_field_set_for_account(
        account_key="default_does_not_exist", field_set_name="hd_internal"
    )
    assert default_account is None


@pytest.mark.django_db
def test_enable_form_tab_success():
    assert Account.objects.get(key="default").enable_form_tab is False
    default_account = configure_form_tab_for_account(
        account_key="default", enable_form_tab=True
    )
    default_account.save()
    del default_account
    default_account = Account.objects.get(key="default")
    assert default_account.enable_form_tab is True


@pytest.mark.django_db
def test_enable_form_tab_fails():
    default_account = configure_form_tab_for_account(
        account_key="default_does_not_exist", enable_form_tab=True
    )
    assert default_account is None
