import logging
import structlog
import zipfile
from pathlib import Path
from tempfile import TemporaryDirectory
from urllib.parse import urlparse
from uuid import UUID

import djclick as click
import requests

from dossier.helpers_v2 import prepare_semantic_dossier
from dossier.models import Dossier
from dossier.schemas import SemanticDossier

logger = structlog.get_logger()


@click.command()
@click.argument("dossier_uuid", type=click.UUID)
@click.argument(
    "dest_file", type=click.Path(exists=False, dir_okay=False, writable=True)
)
def export(dossier_uuid: UUID, dest_file: str):
    logging.basicConfig(level=logging.INFO)
    logger.info(f"exporting dossier {dossier_uuid}")

    dossier = Dossier.objects.get(uuid=dossier_uuid)
    semantic_dossier = prepare_semantic_dossier(
        dossier=dossier, include_annotations=True
    )
    dest_path = Path(dest_file)
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        new_semantic_dossier = migrate_semantic_dossier(semantic_dossier, temp_path)
        (temp_path / "semantic-dossier.json").write_text(
            new_semantic_dossier.model_dump_json()
        )

        with zipfile.ZipFile(dest_path, "w") as zf:
            for file in temp_path.glob("**/*"):
                zf.write(file, file.relative_to(temp_path))


def download_dossier_file(
    semantic_dossier: SemanticDossier, dossier_file_uuid, dest_folder: Path
):
    dossier_file = semantic_dossier.dossier_files[str(dossier_file_uuid)]
    filename = Path(urlparse(dossier_file.url).path).name
    res = requests.get(dossier_file.url)
    dest_file = dest_folder / f"{dossier_file_uuid}/{filename}"
    dest_file.parent.mkdir(exist_ok=True)
    with dest_file.open("wb") as fp:
        fp.write(res.content)
    return dest_file.relative_to(dest_folder)


def migrate_semantic_dossier(old_semantic_dossier: SemanticDossier, dest_folder: Path):
    """takes the semantic dossier, creates a copy, downloads all the file into the dest folder and returns the new
    semantic dossier file"""
    semantic_dossier: SemanticDossier = old_semantic_dossier.model_copy(deep=True)

    # download all dossier files
    new_dossier_files = {}
    for dossier_file_uuid, dossier_file in semantic_dossier.dossier_files.items():
        dossier_file.url = download_dossier_file(
            semantic_dossier, dossier_file_uuid, dest_folder
        )
        new_dossier_files[dossier_file_uuid] = dossier_file

    ## fix the links
    for semantic_document in semantic_dossier.semantic_documents:
        for semantic_page in semantic_document.semantic_pages:
            processed_page = semantic_dossier.processed_files[
                semantic_page.source_file_path
            ].pages[str(semantic_page.source_page_number)]

            image_dossier_file_uuid = processed_page.image_dossier_file_uuid
            processed_page.image = new_dossier_files[str(image_dossier_file_uuid)].url

            searchable_pdf_dossier_file_uuid = (
                processed_page.searchable_pdf_dossier_file_uuid
            )
            processed_page.searchable_pdf = new_dossier_files[
                str(searchable_pdf_dossier_file_uuid)
            ].url

            searchable_txt_file_uuid = processed_page.searchable_txt_dossier_file_uuid
            processed_page.searchable_pdf = new_dossier_files[
                str(searchable_txt_file_uuid)
            ].url

    semantic_dossier.dossier_files = new_dossier_files
    return semantic_dossier
