from time import time

import djclick as click
import structlog
from django.utils import timezone

from core.helpers import get_model_stats
from dossier.models import Dossier
from dossier.services import dossier_hard_delete
from django.db import transaction

logger = structlog.getLogger(__name__)


@click.command()
@click.option("--force-delete", is_flag=True, default=False)
@click.option("--limit", type=int, default=None)
@click.option("--account-key", type=str, default=None)
@click.option("--order-by", type=str, default="account_id,-expiry_date")
def delete_expired_dossier(
    force_delete: bool, limit: int, account_key: str, order_by: str
):
    start = time()
    try:
        logger.info("Started deletion of expired dossiers")
        if not force_delete:
            logger.info(
                "Dry run, no models will be deleted. Use --force-delete to delete models"
            )
        # filter_q = Q(
        #     created_at__lt=(
        #         timedelta(days=1) * F("account__default_dossier_expiry_duration_days")
        #     )
        #     + timezone.now()
        # )
        #
        # filter_q |= Q(expiry_date__lt=timezone.now())

        dossiers_to_delete = Dossier._base_manager.filter(
            expiry_date__isnull=False
        ).filter(expiry_date__lt=timezone.now())

        if account_key:
            dossiers_to_delete = dossiers_to_delete.filter(account__key=account_key)

        if order_by:
            dossiers_to_delete = dossiers_to_delete.order_by(*order_by.split(","))

        if limit:
            dossiers_to_delete = dossiers_to_delete[:limit]

        count_deleted_dossiers = dossiers_to_delete.count()

        model_stats = get_model_stats(list(dossiers_to_delete))

        if not force_delete:
            for dossier in dossiers_to_delete.all():
                logger.info(
                    "Dossier to Delete",
                    account_key=dossier.account.key,
                    dossier_uuid=str(dossier.uuid),
                    expiry_date=(
                        dossier.expiry_date.isoformat() if dossier.expiry_date else None
                    ),
                )
            for stats in model_stats.__root__:
                logger.info(
                    "Model Stats",
                    model_type=stats.model_type,
                    count=stats.count,
                )
            logger.info("Dry run, no models deleted")
            return

        logger.info(
            "Models to Delete",
            dossier_count=count_deleted_dossiers,
            model_stats=model_stats.model_dump_json(),
        )

        with transaction.atomic():
            for dossier in dossiers_to_delete.all():
                dossier_hard_delete(dossier.uuid)

        logger.info("Deleted Models", deleted_models=model_stats.model_dump_json())
    finally:
        end = time()
        logger.info("Finished deletion of expired dossiers", duration=end - start)
