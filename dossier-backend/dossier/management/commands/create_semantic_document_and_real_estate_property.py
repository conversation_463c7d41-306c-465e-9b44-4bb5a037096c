import structlog
import random

import djclick as click
from faker import Faker
from uuid import UUI<PERSON>

from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import (
    Account,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
    Dossier,
    DocumentCategory,
)
from semantic_document.models import (
    AssignedRealEstatePropertySemanticDocument,
)


logger = structlog.get_logger()


@click.command()
@click.argument("dossier_uuid", type=click.UUID)
def create_semantic_document_real_estate_property_account(dossier_uuid: UUID):
    # Creates a semantic document and real estate property
    # Used to test updating of UI when we create a semantic document and a real estate property
    # view external API
    default_account = Account.objects.get(key="default")

    default_account.enable_real_estate_properties = True

    default_account.save()

    faker = Faker(locale="de_CH")

    faker.seed_instance(random.random())

    dossier = Dossier.objects.get(uuid=dossier_uuid, account=default_account)

    semantic_documents = add_some_fake_semantic_documents(
        dossier, num_docs=1, allow_empty_docs=False
    )

    property_document_category = DocumentCategory.objects.filter(
        id__startswith="6"
    ).first()

    for semantic_document in semantic_documents:
        semantic_document.document_category = property_document_category
        semantic_document.save()

        realestate_property, _ = RealestateProperty.objects.update_or_create(
            defaults=dict(
                title=faker.sentence(nb_words=3),
                floor=(
                    random.randint(1, 20) if random.random() > 0.8 else None
                ),  # Generating random floor number
                street=faker.street_name(),
                street_nr=faker.building_number() if random.random() > 0.8 else None,
                zipcode=faker.postcode(),
                city=faker.city(),
            ),
            dossier=semantic_document.dossier,
            key=faker.bothify("PrN######"),
        )

        AssignedRealEstatePropertySemanticDocument.objects.create(
            semantic_document=semantic_document,
            realestate_property=realestate_property,
        )

        original_files = semantic_document.dossier.original_files.all()

        for original_file in original_files:
            AssignedRealestatePropertyOriginalFile.objects.create(
                originalfile=original_file,
                realestate_property=realestate_property,
            )

        print("real_estate_property", realestate_property.key)
        print("semantic_document", semantic_document.title)
