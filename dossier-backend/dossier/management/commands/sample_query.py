from pprint import pprint

from django.core.management.base import BaseCommand
from django.db import reset_queries, connection
from django.utils import timezone

from dossier.models import Dossier, DossierFile


class Command(BaseCommand):
    help = "Test the query"

    def handle(self, *args, **kwargs):
        reset_queries()
        dossier = (
            Dossier.objects.prefetch_related(
                "dossierfile_set",
            )
            .filter(uuid="b9ef9588-e6a8-4de4-a8cb-cfa75ebf7112")
            .first()
        )

        dossier_file: DossierFile
        start = timezone.now()
        for dossier_file in dossier.dossierfile_set.all():
            print(dossier_file.data.fast_url)
        end = timezone.now()
        print("duration", end - start)
        for row in connection.queries:
            pprint(row)

        print(len(connection.queries))
