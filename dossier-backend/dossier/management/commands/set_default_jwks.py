import structlog
import os

import djclick as click
from django.core.cache import cache

from dossier.models import Account, JWK

from projectconfig.authentication import get_keycloak_jwks
from projectconfig.jwk import load_jwk_from_env
from dossier.tests.data import DATA_PATH

logger = structlog.get_logger()


@click.command()
def run():
    # Sets JWK from keycloak and also default JWK from dossier.tests.data
    # so we can generate a token with
    # python manage.py generate_default_account_jwt

    # Sometimes on docker compose dev startup keycloak takes a while to load and this command can fail (i.e. get from
    # keycloak)
    # so patch a default value in
    try:
        jwks = get_keycloak_jwks()
    except Exception:
        jwks = {
            "keys": [
                {
                    "kid": "rm-e5WEVEX9eXTgRvHoYMjGAmCP5lFwgTDVC00iSYQQ",
                    "kty": "RSA",
                    "alg": "RS256",
                    "use": "sig",
                    "n": "kCmvvqDTcjFI-Xphm_cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv-ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27-CC1p_lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2-VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj-XqcZXvKAo_v1U2A9c5CztadZSa7TL8TZa_TOByxbYCaElxWKs-TWVmnj-TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITw",
                    "e": "AQAB",
                    "x5c": [
                        "MIICrTCCAZUCBgF5qD0C+DANBgkqhkiG9w0BAQsFADAaMRgwFgYDVQQDDA9kZXYtaHlwb2Rvc3NpZXIwHhcNMjEwNTI2MTAzMjIxWhcNMzEwNTI2MTAzNDAxWjAaMRgwFgYDVQQDDA9kZXYtaHlwb2Rvc3NpZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCQKa++oNNyMUj5emGb9wiILg6/3ULcFyoRE0soOdvtyrmU4uU8EwQGBbtUsq/5nJ1CTzoJyjBEzkKTyax7DQ3AGd+c22nxeMtGqWx0QN6tHNa3q8BOACWFc124byN8KgI2KoNPbv4ILWn+U9wg07HeFaemm/xdWuzisNQRJJfRFEuGCdBrOvWo92r2GiFjZ04E/g03xovP3z1gjb5WNOvdIkSlm8lfAg1y53lYmylS24ELIu3WUg4/dKP5epxle8oCj+/VTYD1zkLO1p1lJrtMvxNlr9M4HLFtgJoSXFYqz5NZWaeP5M8CgAE9xXoiNK9GDnuIW4AylqI988PkKEhPAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAI4kPHlNw9vRxXJyiwQjeRRg1IvdokqOVu/fbo70v6R+YsrpeX8PElWFkAHjN3YFX4HE5uaCefjFL+fNIHnmUYLlNVxCEDyOADAmwvu7L4MCZdRrO8ihyZUf5pUMo0WhW+8+uXQX8kyfOaC0W3xcbbJjZ2znQGWdHzWNL17YYV8ywC/+UoGfxpr0EpgIbXEa3+DIAZHvijxrwS1SGIjckCmE62myt7BM3CrfNgOGm9P5rNDYgLT/GhUWQZ1DjyC7BX5/KaPUv2aTSpHZg/PaBed/0S7RzLiAbzEKWH3uO3Vxx/lzHcYyw5Z8v+X13QLrueS76Trwpfd9Suv0JbjTZxU="
                    ],
                    "x5t": "ZgPdjvTvk6GxD72kltcX7IMWmRc",
                    "x5t#S256": "llNBMfKaveRZKsurpX-qqpxqDYzawRbidCHGmeR3qV8",
                }
            ]
        }
    key = jwks["keys"][0]
    JWK.objects.create(jwk=key, account=Account.objects.get(key="default"))

    jwks_public = load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    ).model_dump()

    key = jwks_public["keys"][0]
    JWK.objects.create(jwk=key, account=Account.objects.get(key="default"))

    cache.delete("jwkdefault")
