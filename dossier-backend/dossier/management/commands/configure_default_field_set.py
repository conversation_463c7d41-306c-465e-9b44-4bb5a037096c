import structlog

import djclick as click
from django.core.management import call_command

from dossier.models import Account

logger = structlog.get_logger()


@click.command()
def command():
    """
    For the default account_key, configure the default field set as "hd_internal" and enable the form tab
    Load the field set data : we call the command "cdp_init" to load the field set data
    """
    field_set_name = "hd_internal"
    account = configure_default_field_set_for_account(
        account_key="default", field_set_name=field_set_name
    )
    if account:
        account.save()
        click.secho(
            f"Default field set configured successfully: {field_set_name}", fg="green"
        )

    account = configure_form_tab_for_account(
        account_key="default", enable_form_tab=True
    )
    if account:
        account.save()
        click.secho("Form tab enabled successfully", fg="green")
    call_command("cdp_init")


def configure_default_field_set_for_account(
    account_key="default", field_set_name="hd_internal"
):
    try:
        default_account = Account.objects.get(key=account_key)
        default_account.cdp_field_set = field_set_name
        return default_account
    except Exception as e:
        logger.error("Failed to configure default field set", error=str(e))
        return None


def configure_form_tab_for_account(account_key="default", enable_form_tab=True):
    try:
        default_account = Account.objects.get(key=account_key)
        default_account.enable_form_tab = enable_form_tab
        return default_account
    except Exception as e:
        logger.error("Failed to configure form tab for account", error=str(e))
        return None
