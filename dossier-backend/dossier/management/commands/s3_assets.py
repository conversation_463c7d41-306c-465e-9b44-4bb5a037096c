import logging
import structlog
import shutil
from pathlib import Path
from tempfile import TemporaryDirectory

import djclick as click
import minio
from django.conf import settings

from assets import ASSETS_PATH
from core.temporary_path import temporary_path

logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("bucket")
def load(bucket):
    logging.basicConfig(level=logging.INFO)

    dossier_files = (ASSETS_PATH / "sample_dossiers").glob("*.zip")
    for dossier_file in dossier_files:
        logger.info(f"uploading dossier {dossier_file}")
        with TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            shutil.unpack_archive(dossier_file, temp_path)

            minio_client = minio.Minio(
                settings.S3_ENDPOINT,
                settings.S3_ACCESS_KEY,
                settings.S3_SECRET_KEY,
                secure=settings.S3_SECURE,
                region=settings.S3_REGION,
            )
            for file in Path(temp_dir).glob("**/*"):
                if file.is_file():
                    object_name = str(file.relative_to(temp_path))
                    minio_client.fput_object(bucket, object_name, str(file))


@grp.command()
@click.argument("bucket")
def store(bucket):
    logging.basicConfig(level=logging.INFO)
    dossier_files = ASSETS_PATH / "sample_dossiers"
    minio_client = minio.Minio(
        settings.S3_ENDPOINT,
        settings.S3_ACCESS_KEY,
        settings.S3_SECRET_KEY,
        secure=settings.S3_SECURE,
        region=settings.S3_REGION,
    )
    objects = minio_client.list_objects(bucket, recursive=True)
    with temporary_path() as temp_path:
        for object in objects:
            dest_file = temp_path / object.object_name
            # dest_file.parent.mkdir(exist_ok=True, parents=True)
            minio_client.fget_object(bucket, object.object_name, str(dest_file))

        for dossier_path in temp_path.iterdir():
            print(dossier_path)
            shutil.make_archive(dossier_files / dossier_path.name, "zip", dossier_path)
