import structlog
from pathlib import Path
from typing import Dict, List, <PERSON>ple
from uuid import UUID

from django.forms import model_to_dict
from pydantic import TypeAdapter

from assets import ASSETS_PATH
from dossier import schemas
from dossier.models import DocumentCategory, Account, PageCategory
from dossier.schemas import DocumentCategoryLoad

logger = structlog.get_logger()

DOCUMENT_CATEGORY_UNKNOWN = DocumentCategory(
    id="001",
    name="UNKNOWN",
    de="DOK",
    en="DOC",
    fr="DOC",
    it="DOC",
    description_de="Unbekanntes Dokument (Sprache nicht bekannt)",
)

UNKNOWN_DOCUMENT_CATEGORY_KEYS = [
    "UNKNOWN_DE",
    "UNKNOWN_EN",
    "UNKNOWN_FR",
    "UNKNOWN_IT",
    "UNKNOWN",
]


def get_or_create_document_category_by_name_from_processing(
    name: str, dc: DocumentCategory, account: Account, allow_create: bool
) -> <PERSON><PERSON>[DocumentCategory, bool]:
    """
    @param name:
    @param dc:
    @param account:
    @param allow_create:
    @return: Tuple of document category and flag (True if document category existed or was created; False
    if it did not exist and creating new documents is not allowed. Then document category is UNKNOWN
    """

    # Categories on the left are no longer used and should be mapped to the catgory on the right if that exists
    disabled_legacy_category_mapping = {
        "TAX_ATTACHMENTS": "UNKNOWN",
        "FOREIGN_NATIONAL_ID": "RESIDENCE_PERMIT",
        "SALARY_CONFIRMATION_FORM": "SALARY_CONFIRMATION",
        "ZEK_INFO": "ZEK_CHECK",
    }
    dc_used = None
    if dc.name in disabled_legacy_category_mapping.keys():
        fallback_name = disabled_legacy_category_mapping[dc.name]
        fallback_dc = DocumentCategory.objects.filter(
            name=fallback_name, account=account
        ).first()
        if fallback_dc:
            dc_used = fallback_dc

    if not dc_used:
        dc_used = get_or_create_document_category_by_name(
            name, dc, account, allow_create=allow_create
        )
    if dc_used:
        found = True
    else:
        # This document category does not exist in the current account and creation of new
        # document categories is not allowed. Instead, return UNKNOWN
        dc_used = get_or_create_document_category_by_name(
            DOCUMENT_CATEGORY_UNKNOWN.name,
            DOCUMENT_CATEGORY_UNKNOWN,
            account,
            allow_create=True,
        )
        found = False
    return dc_used, found


def get_or_create_page_category_by_name_from_processing(
    page_category_id: str, page_category_name: str, allow_create: bool
) -> Tuple[PageCategory, bool]:
    """

    @param page_category_id:
    @param page_category_name:
    @param allow_create:
    @return: Tuple of page category and flag (True if page category existed or was created;
    False if it did not exist and allow_create==False)
    """
    if allow_create:
        return (
            PageCategory.objects.get_or_create(
                id=page_category_id,
                name=page_category_name,
            )[0],
            True,
        )
    else:
        try:
            return PageCategory.objects.get(name=page_category_name), True
        except PageCategory.DoesNotExist:
            return (
                PageCategory.objects.get_or_create(id="999", name="UNKNOWN_XX")[0],
                False,
            )


def get_or_create_document_category_by_name(
    name: str, dc: DocumentCategory, account: Account, allow_create: bool = True
) -> DocumentCategory:
    """
    Get the document category that matches 'name' for the given account. If it does not exist, create a new one
    and copy all the properties from dc.
    Name must be unique per account, we do not rely on id as that may be different in different
    environments
    """
    document_categories = DocumentCategory.objects.filter(name=name, account=account)
    if document_categories:
        document_category = document_categories[0]
    elif allow_create:
        document_category = DocumentCategory.objects.create(
            id=dc.id,
            account=account,
            name=dc.name,
            de=dc.de,
            en=dc.en,
            fr=dc.fr,
            it=dc.it,
            de_external=dc.de_external,
            en_external=dc.en_external,
            fr_external=dc.fr_external,
            it_external=dc.it_external,
            additional_search_terms_de=dc.additional_search_terms_de,
            additional_search_terms_en=dc.additional_search_terms_en,
            additional_search_terms_fr=dc.additional_search_terms_fr,
            additional_search_terms_it=dc.additional_search_terms_it,
        )
    else:
        document_category = None

    return document_category


def get_document_categories_by_uuid(account: Account) -> Dict[UUID, DocumentCategory]:
    return {
        document_category.uuid: document_category
        for document_category in DocumentCategory.objects.filter(account=account).only(
            "uuid"
        )
    }


# 64ms operation
def get_document_categories_by_name() -> Dict[str, DocumentCategory]:

    return {
        document_category.name: document_category
        for document_category in DocumentCategory.objects.all().only(
            "uuid", "name", "de", "en", "fr", "it"
        )
    }


DEFAULT_CATEGORIES = "default_categories"


def load_document_categories_from_path(
    account: Account,
    document_categories_json_path: str = None,
    info_logging: bool = False,
    update_id: bool = True,
    update_exclude_for_recommendation: bool = True,
):
    if (
        not document_categories_json_path
        or document_categories_json_path == DEFAULT_CATEGORIES
    ):
        # Update the current default set of categories here if there is a new version.
        # This is expected to change when new categories are added / changed
        document_categories_json_path = (
            ASSETS_PATH / "document_category/default/DocumentCategory-2025-03-10.json"
        )
    else:
        document_categories_json_path = Path(document_categories_json_path)

    logger.info(
        f"updating account '{account.key}' with document categories from '{document_categories_json_path}'..."
    )

    assert (
        document_categories_json_path
    ), f"Path '{document_categories_json_path}' is not a valid path"
    assert (
        document_categories_json_path.exists()
    ), f"Path '{document_categories_json_path}' does not exist"

    text = Path(document_categories_json_path).read_text()

    # TODO change the names of these fields in classifier for consistency
    text = text.replace('"docid"', '"id"')
    text = text.replace('"desc_de"', '"description_de"')
    text = text.replace('"desc_en"', '"description_en"')
    text = text.replace('"desc_fr"', '"description_fr"')
    text = text.replace('"desc_it"', '"description_it"')

    document_categories: List[DocumentCategoryLoad] = TypeAdapter(
        List[schemas.DocumentCategory]
    ).validate_json(text)

    logger.info(f"Found {len(document_categories)} document categories to be loaded.")

    return load_document_categories(
        account,
        document_categories,
        info_logging=info_logging,
        update_id=update_id,
        update_exclude_for_recommendation=update_exclude_for_recommendation,
    )


def load_document_categories(
    account: Account,
    document_categories: List[DocumentCategoryLoad],
    info_logging: bool = False,
    update_id: bool = True,
    update_exclude_for_recommendation: bool = True,
):
    """

    @param account:
    @param document_categories:
    @param info_logging:
    @return: 4 lists of DocumentCategory: "all doc cat", "newly created doc cat", "updated doc cat",
    "skipped (unchanged) doc cat"
    """ ""
    l_created = []
    l_updated_id = []
    l_updated_name_de = []
    l_updated_minor_change = []
    l_skipped = []

    old_categories = list(DocumentCategory.objects.filter(account=account).all())
    old_categories_map = {}
    for c in old_categories:
        old_categories_map[c.name] = c

    for document_category in document_categories:
        # Removing the name is necessary as otherwise the update_or_create()
        # might not work properly and create when an update should be done
        # (same key must not be in defaults and filter criteria)
        new_defaults = document_category.model_dump().copy()
        new_defaults.pop("name")
        dc_already_exists = document_category.name in old_categories_map
        has_id_already = (
            dc_already_exists and old_categories_map[document_category.name].id
        )
        has_exclude_already = (
            dc_already_exists
            and old_categories_map[document_category.name].exclude_for_recommendation
        )
        if not update_id and has_id_already:
            # if there is already an id and it should not be updated, then pop
            new_defaults.pop("id")
        if not update_exclude_for_recommendation and has_exclude_already:
            new_defaults.pop("exclude_for_recommendation")
        new, created = DocumentCategory.objects.update_or_create(
            defaults=new_defaults,
            name=document_category.name,
            account=account,
        )

        if created:
            # logger.info(f"Create new category {document_category.name}")
            l_created.append(new)
        else:
            old: DocumentCategoryLoad = old_categories_map.get(document_category.name)
            same = (
                (old.id == new.id or not update_id)
                and old.de == new.de
                and old.en == new.en
                and old.fr == new.fr
                and old.it == new.it
                and old.de_external == new.de_external
                and old.en_external == new.en_external
                and old.fr_external == new.fr_external
                and old.it_external == new.it_external
                and old.description_de == new.description_de
                and old.description_en == new.description_en
                and old.description_fr == new.description_fr
                and old.description_it == new.description_it
                and old.additional_search_terms_de == new.additional_search_terms_de
                and old.additional_search_terms_en == new.additional_search_terms_en
                and old.additional_search_terms_fr == new.additional_search_terms_fr
                and old.additional_search_terms_it == new.additional_search_terms_it
                and (
                    old.exclude_for_recommendation == new.exclude_for_recommendation
                    or not update_exclude_for_recommendation
                )
            )

            updated_id = old.id != new.id

            updated_name_de = old.de != new.de

            if same:
                # logger.info(f"Skip category {document_category.name}")
                l_skipped.append(new)
            elif updated_id:
                l_updated_id.append(new)
            elif updated_name_de:
                l_updated_name_de.append(new)
            else:
                logger.info(f"Update category {document_category.name}...")
                logger.info(f"  OLD={model_to_dict(old)}")
                logger.info(f"  NEW={model_to_dict(new)}")
                l_updated_minor_change.append(new)

    logger.info(
        f"DocumentCategories loaded for account {account.key}. {len(l_created)} created, {len(l_updated_minor_change)} updated (minor change), {len(l_updated_id)} updated (ID has changed), {len(l_updated_name_de)} updated (name de has changed), {len(l_skipped)} skipped (because unchanged)"
    )

    updates = [
        (l_updated_id, "ID update"),
        (l_updated_name_de, "Name DE update"),
        (l_updated_minor_change, "minor change"),
    ]

    for update in updates:
        list_updates, desc = update
        if list_updates:
            keys = [f"{doccat.id} {doccat.name} {doccat.de}" for doccat in list_updates]
            t = "\n".join(keys)
            if info_logging:
                logger.info(f"These categories have been updated ({desc}):\n{t}")

    if l_created:
        keys = [
            f"{doccat.id},{doccat.name},{doccat.de},{doccat.en},{doccat.fr},{doccat.it}"
            for doccat in l_created
        ]
        t = "\n".join(keys)
        if info_logging:
            logger.info(f"These categories have been newly created:\n{t}")

    return (
        l_created + l_updated_minor_change + l_skipped,
        l_created,
        l_updated_minor_change + l_updated_id + l_updated_name_de,
        l_skipped,
    )
