from django.db.models.signals import pre_save
from django.dispatch import receiver
from events.services import publish

from doccheck.models import BusinessCaseType as DocCheckBusinessCaseType, Case
from dossier.models import BusinessCaseType, Dossier
from dossier.schemas import DossierWorkStatusChangedEvent


@receiver(pre_save, sender=Dossier)
def dossier_status_changed_handler(sender, instance: Dossier, **kwargs):
    old_dossier = Dossier.objects.filter(uuid=instance.uuid).first()

    from_state_key = None
    if old_dossier is not None and old_dossier.work_status is not None:
        from_state_key = old_dossier.work_status.key

    to_state_key = None
    if instance.work_status is not None:
        to_state_key = instance.work_status.key

    if from_state_key != to_state_key:
        username = try_to_get_username()
        publish(
            DossierWorkStatusChangedEvent(
                dossier_uuid=instance.uuid,
                username=username,
                from_state_key=from_state_key,
                to_state_key=to_state_key,
            )
        )


def try_to_get_username():
    try:
        import inspect

        for fr in inspect.stack():
            if "request" in fr[0].f_locals:
                request = fr[0].f_locals["request"]
                if hasattr(request, "auth"):
                    print(request.auth)
                    return request.auth.user.username

    except:
        pass
    return "system"


@receiver(pre_save, sender=Dossier)
def update_case_business_case_type_pre_save_dossier_business_case(
    sender, instance: Dossier, **kwargs
):
    case_to_dossier = (
        Case.objects.filter(uuid=instance.doccheck_case.uuid).first()
        if instance.doccheck_case
        else None
    )
    old_dossier = Dossier.objects.filter(uuid=instance.uuid).first()
    if case_to_dossier and old_dossier:
        if instance.businesscase_type != old_dossier.businesscase_type:
            if instance.businesscase_type:
                case_business_case_type = DocCheckBusinessCaseType.objects.get(
                    doc_check=instance.account.active_doc_check,
                    key=instance.businesscase_type.key,
                )
                Case.objects.filter(uuid=instance.doccheck_case.uuid).update(
                    business_case_type=case_business_case_type
                )
            else:
                # Dossier might have no businesscase_type (set as null=True).
                # But Case always needs to have a business_case_type (set as null=False).
                # Use the default record in DocCheck.BusinessCaseType with key='UNKNOWN' in such cases.
                doccheck_business_case_type = DocCheckBusinessCaseType.objects.get(
                    doc_check=instance.account.active_doc_check, key="UNKNOWN"
                )
                Case.objects.filter(uuid=instance.doccheck_case.uuid).update(
                    business_case_type=doccheck_business_case_type
                )


@receiver(pre_save, sender=Case)
def update_dossier_business_case_type_pre_save_case_business_case(
    sender, instance: Case, **kwargs
):
    dossier_to_case = Dossier.objects.filter(doccheck_case=instance).first()
    old_case = Case.objects.filter(uuid=instance.uuid).first()
    if dossier_to_case and old_case:
        if instance.business_case_type != old_case.business_case_type:
            # Dossier might have no businesscase_type (set as null=True).
            # But Case always needs to have a business_case_type (set as null=False).
            # Use the default record in DocCheck.BusinessCaseType with key='UNKNOWN' in such cases.
            doccheck_business_case_type_unknown = DocCheckBusinessCaseType.objects.get(
                doc_check=instance.doc_check, key="UNKNOWN"
            )
            if instance.business_case_type != doccheck_business_case_type_unknown:
                account = Dossier.objects.get(doccheck_case=instance).account
                dossier_business_case_type = BusinessCaseType.objects.get(
                    account=account, key=instance.business_case_type.key
                )
                Dossier.objects.filter(doccheck_case=instance).update(
                    businesscase_type=dossier_business_case_type
                )
            else:
                Dossier.objects.filter(doccheck_case=instance).update(
                    businesscase_type=None
                )
