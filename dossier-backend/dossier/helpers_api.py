from django.http import HttpResponse
from ninja import NinjaAPI
from typing import Any
from structlog.stdlib import <PERSON><PERSON><PERSON>og<PERSON>


def handle_api_validation_error(
    api: NinjaAPI, logger: BoundLogger, exc: Any, request: Any
) -> HttpResponse:
    logger.error(
        "API validation error 422 occurred on API call",
        content_type=request.content_type,
        method=request.method,
        path=request.path,
        body=request.body,
        meta=request.META,
        errors=exc.errors,
        status_code=422,
    )
    return api.create_response(request, {"detail": exc.errors}, status=422)
