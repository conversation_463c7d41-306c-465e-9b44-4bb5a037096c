from typing import Optional, Annotated

from pydantic import BaseModel, StringConstraints, Field, ConfigDict

from dossier.schemas import Language

ExternalDossierID = Annotated[
    str, StringConstraints(max_length=255, pattern="[A-Za-z0-9-]{1,255}")
]
DossierName = Annotated[str, StringConstraints(max_length=255)]


class CreateDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: DossierName
    lang: Optional[Language] = Language.de


class ChangeDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: Optional[DossierName] = None
    lang: Optional[Language] = None


class DossierCloseReadyResponse(BaseModel):
    model_config = ConfigDict(extra="forbid")  # Disallow extra fields

    num_documents_all: int = Field(
        ..., description="Total number of semantic documents in the dossier"
    )
    num_documents_exported: int = Field(
        ..., description="Number of exported semantic documents in the dossier"
    )
    num_documents_export_not_started: int = Field(
        ..., description="Number of semantic documents in the dossier not yet exported"
    )
    num_documents_in_export: int = Field(
        ...,
        description="Number of semantic documents in the dossier that are currently being exported",
    )
    num_documents_unknown: int = Field(
        ...,
        description="Number of semantic documents in the dossier that are classified as unknown",
    )
    num_original_files_in_processing: int = Field(
        ...,
        description="Number of original files in the dossier that currently have status Processing",
    )
    ready_for_close: bool = Field(
        ...,
        description="If True it indicates if the dossier is ready for closing. Else the user needs to do something",
    )
    msg_nok_de: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in German",
    )
    msg_nok_en: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in English",
    )
    msg_nok_fr: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in French",
    )
    msg_nok_it: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in Italian",
    )


class DossierCloseResponse(BaseModel):
    model_config = ConfigDict(extra="forbid")  # Disallow extra fields

    success: bool = Field(
        ..., description="If True the dossier was closed successfully."
    )

    msg_nok_de: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in German",
    )
    msg_nok_en: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in English",
    )
    msg_nok_fr: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in French",
    )
    msg_nok_it: Optional[str] = Field(
        None,
        description="If ready_for_close is False, instructions what user needs to do in Italian",
    )


class PrepareCloseDossierResponse(BaseModel):
    """Response schema for prepare-close-dossier endpoint"""

    success: bool
    exports_triggered_count: int
    detail: str
    # Only populated if dossier was actually closed
    close_result: Optional[DossierCloseResponse] = None
