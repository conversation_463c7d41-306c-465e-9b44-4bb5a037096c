
# General Python timezone stuff

```
# When creating a datetime, make it always timezone aware. Timezone can be utc if it does not matter
# or Zurich time if you need a specific time but it must be timezone aware.

# CORRECT: 
from datetime import timezone as datetime_timezone
dt = datetime(2023, 1, 2, 14, 30, tzinfo=datetime_timezone.utc)

# WRONG: 
dt = datetime(2023, 1, 2, 14, 30)
```

# Timestamps

- Timestamps are by definition always in UTC (POSIX timestamp (seconds since epoch UTC))
- If you create a .timestamp() on a datetime with timezone UTC or timezone Zurich this will return the same result
- Preferred way to create timestamps:
```
from django.utils import timezone
# This works correctly out to a UTC timestamp even though we are in Zurich timezone - because all timestamps are UTC.
print("UTC timestamp:", int(timezone.now().timestamp()))
```

# Timezone in Django

We have this setting in settings.py:
```
USE_TZ = True  # Enable timezone-aware datetimes, datetimes are stored in UTC in DB
TIME_ZONE = "Europe/Zurich" # Django converts to/from UTC when writing/reading the DB
```
- This ensures that all datetimes are stored in UTC in the DB and Django takes care of transforming them. 
- This also ensures that times are correctly shown in the admin for timezone Zurich.
- As a consequence when using .now() it must ALWAYS be the django.utils.timezone.now() and never the datetime.timezone.now() because only Django knows about the timezone that we have set.
- So e.g. to initialize a date field for a Django model or a JWT or anything that needs the current time always use django.utils.timezone and NOT datetime.timezone. 
```
from django.utils import timezone as django_timezone
some_field = django_timezone.now()
```

# Timezone in Faker

When you create a timestamp in faker it must be created timezone aware and NOT as a naive datetime.

```
# CORRECT: 
from datetime import datetime 
dt = faker.past_datetime(start_datetime='-30d', tzinfo=timezone.utc)

# WRONG as this is not timezone aware: 
dt = faker.date_time_between(start_date='-30d', end_date='now')
```

# Timezone in API

- All timestamps in the API must be timezone aware AND with timezone UTC. So we use timezone-aware ISO 8601 (Z suffix)
- So all schemas must use datetime.datetime to create a timezone if needed for the API.

CORRECT: 
```
from datetime import datetime
last_change: datetime = datetime.now(tz=tz.tzutc())  # Default to now in UTC
```
When serialized this will be shown with the "Z" at the end:
- "updated_at": "2025-04-07T09:30:00Z"

# On import conflicts between django and datetime
If you need both, django and datetime stuff in the same file then do the imports for both like this
to be sure that nobody uses the wrong one unintentionally
```
from datetime import datetime as datetime_datetime, timedelta
from datetime import timezone as datetime_timezone
```

Example for this is in the helpers_timezone.py