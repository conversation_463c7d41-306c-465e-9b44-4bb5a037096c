import pytest
import structlog
from django.core.management import call_command

from clientis.factories import update_or_create_semantic_document_workflow
from clientis.schemas.schemas import AccountName
from dossier.models import Account, DossierCloseStrategy

logger = structlog.get_logger()


@pytest.mark.django_db
def test_load_semantic_document_workflow_command():
    """
    Test the load_semantic_document_workflow command by running it for the clientistest account
    and verifying the account has the correct semantic document workflow configuration.
    """
    # First, load the account using the load_account command
    account_key = AccountName.clientistest.value
    call_command("load_clientis_data", "load-account", account_key)

    # Get the account from the database
    account = Account.objects.get(key=account_key)

    # Run the semantic document workflow command
    call_command("load_clientis_data", "load-semantic-document-workflow", account_key)

    # Refresh the account from the database
    account.refresh_from_db()

    # Verify the account has the correct semantic document workflow configuration
    assert account.enable_semantic_document_export is True
    assert account.frontend_theme == "FINNOVA"
    assert account.enable_semantic_document_export_unknown_documents is True
    assert (
        account.dossier_close_strategy
        == DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE
    )
    assert account.dossier_close_expiry_days == 30
    assert account.active_semantic_document_work_status_state_machine is not None


@pytest.mark.django_db
def test_update_or_create_semantic_document_workflow_function():
    """
    Test the update_or_create_semantic_document_workflow function directly.
    """
    # First, load the account using the load_account command
    account_key = AccountName.clientistest.value
    call_command("load_clientis_data", "load-account", account_key)

    # Get the account from the database
    account = Account.objects.get(key=account_key)

    # Call the function directly
    updated_account = update_or_create_semantic_document_workflow(account)

    # Verify the account has the correct semantic document workflow configuration
    assert updated_account.enable_semantic_document_export is True
    assert updated_account.frontend_theme == "FINNOVA"
    assert updated_account.enable_semantic_document_export_unknown_documents is True
    assert (
        updated_account.dossier_close_strategy
        == DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE
    )
    assert updated_account.dossier_close_expiry_days == 30
    assert (
        updated_account.active_semantic_document_work_status_state_machine is not None
    )
