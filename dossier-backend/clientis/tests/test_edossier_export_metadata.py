import time

import pytest
import structlog

from clientis.edossier_export_metadata import (
    load_export_metadata,
    get_external_ids_with_account_number,
    get_external_ids_allow_framecontract_id,
)
from clientis.factories import ClientisAccountFactoryFaker

logger = structlog.get_logger()


def test_load_export_metadata():
    """Test that the export metadata file is loaded correctly."""
    # Load the metadata
    metadata_dict = load_export_metadata()

    # Assert that we have entries
    assert len(metadata_dict) > 0, "No entries found in the metadata file"

    # Assert that all entries have the required fields
    for external_id, metadata in metadata_dict.items():
        # Basic field presence checks
        assert isinstance(
            metadata.external_id, int
        ), f"external_id should be integer for entry {external_id}"
        assert metadata.title_de, f"Missing title_de for entry {external_id}"
        assert metadata.title_fr, f"Missing title_fr for entry {external_id}"

        # Type checks
        assert isinstance(
            metadata.relevant_for_hd, bool
        ), f"relevant_for_hd should be boolean for entry {external_id}"
        assert isinstance(
            metadata.required_fields, str
        ), f"required_fields should be string for entry {external_id}"
        assert isinstance(
            metadata.not_allowed_fields, str
        ), f"not_allowed_fields should be string for entry {external_id}"
        assert isinstance(
            metadata.allow_framecontract_id, bool
        ), f"allow_framecontract_id should be boolean for entry {external_id}"
        assert isinstance(
            metadata.require_account_number, bool
        ), f"require_account_number should be boolean for entry {external_id}"

        # Field format checks
        assert (
            metadata.external_id > 0
        ), f"external_id should be positive for entry {external_id}"

        # Verify titles contain document category numbers (they start with digits followed by dot)
        assert any(
            c.isdigit() for c in metadata.title_de
        ), f"title_de should contain numeric category for entry {external_id}"
        assert any(
            c.isdigit() for c in metadata.title_fr
        ), f"title_fr should contain numeric category for entry {external_id}"
        assert (
            "." in metadata.title_de
        ), f"title_de should contain category separator for entry {external_id}"
        assert (
            "." in metadata.title_fr
        ), f"title_fr should contain category separator for entry {external_id}"

        # Field content checks - only verify format if fields are not empty
        assert (
            ";" in metadata.required_fields
        ), f"required_fields should not be empty and contain semicolon-separated values for entry {external_id}"

        # Verify allow_framecontract_id is correctly set based on not_allowed_fields
        expected_allow_framecontract = "CreditNr" not in metadata.not_allowed_fields
        assert (
            metadata.allow_framecontract_id == expected_allow_framecontract
        ), f"allow_framecontract_id should be {expected_allow_framecontract} for entry {external_id} with not_allowed_fields={metadata.not_allowed_fields}"

        # Verify require_account_number is correctly set based on required_fields
        expected_require_account = "accountNr" in metadata.required_fields
        assert (
            metadata.require_account_number == expected_require_account
        ), f"require_account_number should be {expected_require_account} for entry {external_id} with required_fields={metadata.required_fields}"

        # not_allowed_fields can be empty, no need for additional validation


def test_get_external_ids_with_account_number():
    """Test that we get the correct list of external IDs requiring account numbers."""
    external_ids = get_external_ids_with_account_number()

    # List of expected external IDs that require account numbers
    expected_ids = [
        201070,
        201090,
        100530,
        200350,
        100430,
        100450,
        100440,
        200580,
        200590,
        100930,
        200280,
        100700,
        100710,
        100410,
        100420,
        200240,
        200230,
        200250,
        200260,
    ]

    # Sort both lists before comparison since we don't care about the order
    assert sorted(external_ids) == sorted(
        expected_ids
    ), f"Expected external IDs {sorted(expected_ids)}, but got {sorted(external_ids)}"


@pytest.mark.django_db
def test_document_categories_with_account_number():
    """Test that document categories match the external IDs requiring account numbers."""
    # Initialize the factory
    clientis_fac = ClientisAccountFactoryFaker(account_key="clientistest")

    # Load document categories
    document_categories, _, _, _ = clientis_fac.load_initial_document_categories()

    # Get the list of external IDs that require account numbers
    external_ids_with_account = get_external_ids_with_account_number()

    # Get document categories that should require account numbers
    account_required_categories = [
        doc_cat
        for doc_cat in clientis_fac.document_categories
        if int(doc_cat.id_external) in external_ids_with_account
    ]

    # Log document categories requiring account numbers
    for cat in account_required_categories:
        logger.info(
            "Document category requiring account number",
            external_id=cat.id_external,
            name=cat.name,
            title_de=cat.de,
        )

    # 250416 mt: changed as agreed on by René Kurz, therefore no categories must be in that list
    # We fixed the mapping of MORTGAGE_PRODUCT_CONFIRMATION to 200580 (MORTGAGE_CONTRACT) instead of 200570
    # We fixed the mapping of POWER_OF_ATTORNEY to 200490 (3.06.05_Finanzen - Andere Dokumente (200490)) instead of 201070
    assert (
        len(account_required_categories) == 0
    ), f"Expected no document categories requiring account numbers, but got {account_required_categories}"


@pytest.mark.django_db
def test_document_categories_with_framecontract_id():
    """Test that document categories match the external IDs allowing framecontract IDs."""
    # Initialize the factory
    clientis_fac = ClientisAccountFactoryFaker(account_key="clientistest")

    # Load document categories
    document_categories, _, _, _ = clientis_fac.load_initial_document_categories()

    # Get the list of external IDs that allow framecontract IDs
    external_ids_with_framecontract = get_external_ids_allow_framecontract_id()

    # Get document categories that should allow framecontract IDs
    framecontract_allowed_categories = [
        doc_cat
        for doc_cat in clientis_fac.document_categories
        if int(doc_cat.id_external) in external_ids_with_framecontract
    ]

    # Log document categories allowing framecontract IDs
    for cat in framecontract_allowed_categories:
        logger.info(
            "Document category allowing framecontract ID",
            external_id=cat.id_external,
            name=cat.name,
            title_de=cat.de,
        )

    # Expected list of external IDs that should allow framecontract IDs
    expected_ids = [
        100660,
        200090,
        200110,
        200480,
        200570,
        200580,
        200590,
        200600,
        200610,
        200620,
        200630,
        200640,
        200650,
        200660,
        200670,
        200680,
        200690,
        200700,
        200710,
        200720,
        200730,
        200740,
        200750,
        200760,
        200770,
        200780,
        200790,
        200800,
        200810,
        200970,
        200980,
        200990,
        201010,
        201020,
        201030,
        201230,
        201240,
        201250,
        201260,
        201270,
        201280,
        201290,
        201300,
        201310,
        201320,
        201330,
        201340,
        201350,
        201360,
        201370,
        201380,
        201390,
    ]

    assert len(expected_ids) == 52

    # Sort both lists before comparison
    assert sorted(external_ids_with_framecontract) == sorted(
        expected_ids
    ), f"Expected external IDs {sorted(expected_ids)}, but got {sorted(external_ids_with_framecontract)}"


@pytest.mark.django_db
def test_performance_get_external_ids_allow_framecontract_id():
    """Test the performance of get_external_ids_allow_framecontract_id over 100 iterations."""
    iterations = 10

    start_time = time.time()

    for _ in range(iterations):
        external_ids = get_external_ids_allow_framecontract_id()
        assert (
            len(external_ids) == 52
        )  # Verify we get the expected number of IDs each time

    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / iterations

    logger.info(
        "Performance test results",
        iterations=iterations,
        total_time_seconds=total_time,
        average_time_seconds=avg_time,
    )


@pytest.mark.django_db
def test_load_export_metadata_caching():
    """Test that the use_cache parameter in load_export_metadata works correctly."""
    # First load - should populate cache
    first_result = load_export_metadata()

    # Second load with cache - should be fast and use cached result
    start_time = time.time()
    second_result = load_export_metadata()
    cached_time = time.time() - start_time

    # Third load without cache - should reload from file
    start_time = time.time()
    third_result = load_export_metadata(use_cache=False)
    no_cache_time = time.time() - start_time

    # Verify results are the same
    assert first_result.keys() == second_result.keys() == third_result.keys()

    # Verify cache was actually used (cached call should be much faster)
    logger.info(
        "Cache timing comparison",
        cached_call_time=cached_time,
        no_cache_call_time=no_cache_time,
        time_difference=no_cache_time - cached_time,
    )

    assert (
        cached_time < no_cache_time
    ), "Cached call should be faster than non-cached call"
