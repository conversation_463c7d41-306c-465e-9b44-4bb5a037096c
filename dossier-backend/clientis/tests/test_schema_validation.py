import pytest
from pydantic import ValidationError

from clientis.schemas.schemas import ClientisExternalId


def test_valid_external_id():
    """Test that valid external IDs are accepted"""
    valid_ids = [
        "949.401678.001.110794",  # Standard example
        "123.456.789.000",  # All numeric
        "A1B.C2D.E3F.G4H",  # Alphanumeric mix
        "123-4.567-8.90.12",  # With hyphens
    ]

    for valid_id in valid_ids:
        external_id = ClientisExternalId(external_id=valid_id)
        assert external_id.external_id == valid_id


def test_invalid_length():
    """Test that external IDs exceeding 255 characters are rejected"""
    too_long_id = "123." + "4" * 247 + ".567.890"  # 256 characters

    with pytest.raises(ValidationError) as exc_info:
        ClientisExternalId(external_id=too_long_id)

    assert "String should have at most 255 characters" in str(exc_info.value)


def test_invalid_parts_count():
    """Test that external IDs with incorrect number of parts are rejected"""
    invalid_ids = [
        "123.456.789",  # Too few parts
        "123.456.789.000.111",  # Too many parts
        "123..456.789",  # Empty part
    ]

    for invalid_id in invalid_ids:
        with pytest.raises(ValidationError) as exc_info:
            ClientisExternalId(external_id=invalid_id)
        assert "String should match pattern" in str(exc_info.value)


def test_empty_parts():
    """Test that external IDs with empty parts are rejected"""
    invalid_ids = [
        ".456.789.000",  # First part empty
        "123..789.000",  # Second part empty
        "123.456..000",  # Third part empty
        "123.456.789.",  # Fourth part empty
    ]

    for invalid_id in invalid_ids:
        with pytest.raises(ValidationError) as exc_info:
            ClientisExternalId(external_id=invalid_id)
        assert "String should match pattern" in str(exc_info.value)


def test_invalid_characters():
    """Test that external IDs with invalid characters are rejected"""
    invalid_ids = [
        "12#.456.789.000",  # Special character #
        "123.45$.789.000",  # Special character $
        "123.456.7@9.000",  # Special character @
        "123.456.789.00!",  # Special character !
        "123 456.789.000",  # Space
    ]

    for invalid_id in invalid_ids:
        with pytest.raises(ValidationError) as exc_info:
            ClientisExternalId(external_id=invalid_id)
        assert "String should match pattern" in str(exc_info.value)


def test_pattern_validation():
    """Test that the regex pattern validation works"""
    invalid_ids = [
        "123:456:789:000",  # Wrong separator
        "123/456/789/000",  # Wrong separator
        "123+456+789+000",  # Wrong separator
    ]

    for invalid_id in invalid_ids:
        with pytest.raises(ValidationError):
            ClientisExternalId(external_id=invalid_id)
