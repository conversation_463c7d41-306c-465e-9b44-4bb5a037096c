import structlog
from faker import Faker

from clientis.factories import ClientisAccountFactoryFaker
from clientis.schemas.schemas import Account<PERSON>ame
from dossier.models import DocumentCategory

logger = structlog.get_logger()


def test_update_external_dcb_id_to_document_categories_clientis(db):
    clientis_fac = ClientisAccountFactoryFaker(
        account_key=AccountName.dcb.value,
    )
    document_categories, success_mapping, updates, not_found = (
        clientis_fac.load_initial_document_categories()
    )

    clientis_fac.account.save()

    if len(not_found) > 0:
        logger.info("Not found", not_found=not_found)
        assert not_found == []


def test_update_external_id_to_document_categories_clientis(db):
    clientis_fac = ClientisAccountFactoryFaker(
        account_key="clientistest",
    )
    document_categories, success_mapping, updates, not_found = (
        clientis_fac.load_initial_document_categories()
    )

    clientis_fac.account.save()

    if len(not_found) > 0:
        logger.info("Not found", not_found=not_found)
        assert not_found == []


def test_clientis_document_categories(db):
    account_key = AccountName.clientistest.value
    Faker.seed(234777)
    clientis_fac = ClientisAccountFactoryFaker(
        account_key=account_key,
    )
    doccats, _, _, _ = clientis_fac.load_initial_document_categories()
    clientis_fac.account.save()

    # 262 + 3 custom titre de sejour + CORRESPONDENCE_NOTARY + LETTER_COMMITMENT_NOTARY
    assert len(doccats) == 264

    # d1 = DocumentCategory.objects.get(name="RESIDENCE_PERMIT", account=bfac.account)
    # assert d1.fr == "Titre séjour UE/AELE"

    d2 = DocumentCategory.objects.get(
        name="AUTHORIZATION_EMAIL", account=clientis_fac.account
    )
    assert d2.fr == "Autorisation E-Mail"  # Internal doc cat - unchanged
    assert (
        d2.fr_external == "1.40.20_Ordre par Fax/e-Mail"
    )  # Additional external doccat

    d3 = DocumentCategory.objects.get(
        name="PROOF_OF_INCOME", account=clientis_fac.account
    )
    assert d3.exclude_for_recommendation is False

    d_passport = DocumentCategory.objects.get(
        name="PASSPORT_CH", account=clientis_fac.account
    )
    assert d_passport.id_external == "200560"

    # All doc cats need to be mapped
    for doccat in doccats:
        assert (
            doccat.id_external is not None
        ), f"No mapping for external_id for {doccat}"
        assert int(doccat.id_external) > 0
