import json
import os
import tempfile
import uuid
import zipfile
from datetime import datetime
from pathlib import Path
from typing import List, Optional

import pytest
import requests
import structlog
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from lxml import etree
from pydantic import TypeAdapter
from pytest_mock import MockerFixture

from clientis.schemas.schemas import ClientisExportMetadata
from dossier.fakes import (
    add_some_fake_semantic_documents,
)
from dossier.management.commands.dossier_event_consumer_v2 import (
    set_semantic_document_export_done,
)
from dossier.models import (
    Dossier,
    OriginalFile,
    OriginalFileSource,
    FileStatus,
    SemanticDocumentExportStrategy,
)
from dossier.schemas import Message
from dossier.schemas_external import DossierCloseResponse
from dossier.services_external import add_original_file
from dossier.statemachine_types import DossierState
from dossier.tests.common_state import assert_dossier_access_mode
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401
from semantic_document.models import (
    SemanticDocument,
)
from semantic_document.services import (
    reset_semantic_document_work_status,
    reset_semantic_document_export,
)
from statemgmt.configurations.semantic_document_state_machine import (
    STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    SemanticDocumentState,
)
from statemgmt.models import StateMachine, Status
from workers.models import SemanticDocumentExport
from workers import schemas as worker_schemas
from workers.workers import process_semantic_dossier_pdf_request
from clientis.schemas import schemas

User: AbstractUser = get_user_model()


logger = structlog.get_logger(__name__)


pytestmark = pytest.mark.django_db


def test_ping(clientis_authenticated_client, clientis_account, set_clientis_JWK):
    res = clientis_authenticated_client.get(reverse("clientis-api:ping"))
    assert res.status_code == 200
    assert Message.model_validate_json(res.content) == Message(detail="pong")


def mock_publish_side_effect_services_rabbit_mq_publish(*args, **kwargs):
    request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
        kwargs["message"]
    )

    # Process the pdf generation
    # Returns json dump in format SemanticDocumentPDFResponseV1
    process_semantic_document_response = process_semantic_dossier_pdf_request(
        semantic_document_pdf_request=request
    )

    # which is then collected by dossier events consumer
    # and sets event as done
    set_semantic_document_export_done(process_semantic_document_response)


@pytest.mark.django_db(transaction=True)
def test_get_semantic_documents_api_success(
    clientis_authenticated_client,
    clientis_account,
    document_categories,
    set_clientis_JWK,
    create_clientis_dossier,
):
    """
    1. Create a dossier
    2. Add some random files
    3. Call the API to list the semantic documents and make sure they are the same
    @param clientis_authenticated_client:
    @param clientis_account:
    @param document_categories:
    @param set_clientis_JWK:
    @return:
    """
    dossier, external_dossier_id = create_clientis_dossier

    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False
    )

    response, parsed = api_clientis_get_semantic_documents(
        clientis_authenticated_client, external_dossier_id, 200
    )
    assert ("null" in str(response.content)) is False

    assert len(semantic_documents) == len(parsed)

    semdoc_uuids = {doc.uuid for doc in parsed}
    for semantic_document in semantic_documents:
        assert semantic_document.uuid in semdoc_uuids

    response = clientis_authenticated_client.get(
        path=f"{reverse('clientis-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
    )
    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)
    semdoc_uuids = {doc.uuid for doc in parsed}
    page_uuids = {page.uuid for doc in parsed for page in doc.semantic_pages}

    for semantic_document in semantic_documents:
        assert semantic_document.uuid in semdoc_uuids
        assert semantic_document.access_mode.name == "READ_WRITE"
        for page in semantic_document.semantic_pages.all():
            assert page.uuid in page_uuids


# This test is periodically flaky and I'm not sure why. If it gets annoying disable it
def api_clientis_get_semantic_documents(
    clientis_authenticated_client,
    external_dossier_id: str,
    expected_response_code: Optional[int] = None,
    url_suffix: str = "",
):
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:semantic-documents",
            kwargs={"external_dossier_id": external_dossier_id},
        )
        + url_suffix
    )

    if expected_response_code:
        if not response.status_code == expected_response_code:
            raise Exception(
                f"wrong response code. Expected {expected_response_code} but got {response.status_code}"
            )
    if expected_response_code is None or expected_response_code == 200:
        parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(
            response.content
        )
    else:
        parsed = None
    return response, parsed


def test_get_semantic_documents_api_failure(
    clientis_authenticated_client,
    clientis_account,
    document_categories,
    set_clientis_JWK,
    create_clientis_dossier,
):
    # Test the case where the dossier does not exist
    non_existant_external_uuid = str(uuid.uuid4())
    response, parsed = api_clientis_get_semantic_documents(
        clientis_authenticated_client, non_existant_external_uuid, 404
    )
    assert "does not exist" in str(response.content)
    assert parsed is None

    dossier, external_dossier_id = create_clientis_dossier

    # Validate it has no documents
    response = clientis_authenticated_client.get(
        path=f"{reverse('clientis-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
    )
    assert (
        response.status_code == 200
    ), f"Expected 200 ok. Instead got response={response}"
    assert response.json() == []


@pytest.mark.parametrize(
    "api_url_name",
    [
        "dossier-set-semantic-documents-state-ready-for-export",
        "semantic-document-set-state-ready-for-export",
    ],
)
def test_set_dossier_ready_for_export(
    prepare_data_export,
    clientis_authenticated_client,
    api_url_name,
):
    """
    Perform 2 tests:
    1. Set all semantic documents in dossier as ready for export
    2. Set a single semantic document in dossier as ready for export
    @param prepare_data_export:
    @param clientis_authenticated_client:
    @param api_url_name:
    @return:
    """
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    # Request to generate an export
    # Test two different APIs, one that generates just for the semantic document
    # and one that generates for the whole dossier
    if api_url_name == "dossier-set-semantic-documents-state-ready-for-export":
        response = clientis_authenticated_client.post(
            path=reverse(
                "clientis-api:dossier-set-semantic-documents-state-ready-for-export",
                kwargs={"external_dossier_id": external_dossier_id},
            ),
        )
    else:
        response = clientis_authenticated_client.post(
            path=reverse(
                "clientis-api:semantic-document-set-state-ready-for-export",
                kwargs={
                    "semantic_document_uuid": str(semantic_document.uuid),
                    "external_dossier_id": external_dossier_id,
                },
            ),
        )

    assert response.status_code == 200

    mock_dispatch_publish_request.assert_called()

    assert response.status_code == 200

    list(SemanticDocumentExport.objects.all())

    if api_url_name == "dossier-set-semantic-documents-state-ready-for-export":
        expected_semdoc_export_uuid = response.json()[0]
    else:
        expected_semdoc_export_uuid = response.json()

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=expected_semdoc_export_uuid
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Check that DEC has moved state from IN_FRONT_OFFICE to EXPORT_AVAILABLE
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )

    # Check we can poll for dossier status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:dossier-semantic-document-exports-available",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)

    export = parse[0]

    assert export.semantic_document_export_request_uuid == semantic_document_export.uuid

    assert (
        export.semantic_document_uuid == semantic_document_export.semantic_document.uuid
    )
    assert export.semantic_document_url
    assert (
        export.document_category_id_external
        == semantic_document.document_category.id_external
    )  # used to be this in test mapping "PASSPORT_clientis_CUSTOM"
    assert export.document_category_key == "PASSPORT_CH"

    # Test we can also get the same via all-semantic-document-export-status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:all-semantic-document-export-status",
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 1  # One export waiting (one doc)
    assert (
        parse[0].semantic_document_export_request_uuid == semantic_document_export.uuid
    )

    # Test resting the semantic document to IN_FRONT_OFFICE
    # e.g. if export fails and we need to trigger a reset
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )

    # Remove external_dossier_id and try export again -> should return nothing
    backup_external_id = semantic_document.dossier.external_id
    semantic_document.refresh_from_db()
    semantic_document.dossier.external_id = None
    semantic_document.dossier.save()
    semantic_document.refresh_from_db()
    assert semantic_document.dossier.external_id is None
    response_without_external_id = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:all-semantic-document-export-status",
        ),
    )
    parse_without_external_id: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response_without_external_id.content)
    assert len(parse_without_external_id) == 0
    # Now set the external ID again for the next test
    semantic_document.dossier.external_id = backup_external_id
    semantic_document.dossier.save()
    semantic_document.refresh_from_db()
    assert semantic_document.dossier.external_id is not None

    reset_semantic_document_work_status(semantic_document)
    assert (
        semantic_document.work_status.key
        == semantic_document.dossier.account.active_semantic_document_work_status_state_machine.start_status.key
    )
    response_after_reset = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:all-semantic-document-export-status",
        ),
    )
    parse_after_reset: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response_after_reset.content)
    assert len(parse_after_reset) == 0


@pytest.mark.parametrize(
    "api_url_name",
    [
        # "dossier-set-semantic-documents-state-ready-for-export",
        "semantic-document-set-state-ready-for-export",
    ],
)
def test_set_dossier_ready_for_export_invalid_external_id(
    prepare_data_export,
    clientis_authenticated_client,
    api_url_name,
):
    """
    Test to make sure API only works when the external dossier ID is valid

    """
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    dossier.external_id = "test"
    dossier.save()

    # Request to generate an export
    # Test two different APIs, one that generates just for the semantic document
    # and one that generates for the whole dossier
    if api_url_name == "dossier-set-semantic-documents-state-ready-for-export":
        response = clientis_authenticated_client.post(
            path=reverse(
                "clientis-api:dossier-set-semantic-documents-state-ready-for-export",
                kwargs={"external_dossier_id": "test"},
            ),
        )
    else:
        response = clientis_authenticated_client.post(
            path=reverse(
                "clientis-api:semantic-document-set-state-ready-for-export",
                kwargs={
                    "semantic_document_uuid": str(semantic_document.uuid),
                    "external_dossier_id": "test",
                },
            ),
        )

    assert response.status_code == 422
    assert "Invalid external ID format" in response.json()["detail"]


def test_export_dossier_semantic_document_pdf_auth_failure(
    prepare_data_export,
    clientis_miss_signed_authenticated_client,
):
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )
    # Request to generate an export
    response = clientis_miss_signed_authenticated_client.post(
        path=reverse(
            "clientis-api:semantic-document-set-state-ready-for-export",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 401
    mock_dispatch_publish_request.assert_not_called()


def setup_test_dossier_with_document(
    clientis_account,
    mocker: MockerFixture,
    clientis_account_factory,
) -> tuple[str, OriginalFile, Dossier, list[SemanticDocument]]:
    """
    Sets up a test dossier with a single document for testing.

    Args:
        clientis_account: The clientis account instance
        mocker: PyTest mocker fixture

    Returns:
        Tuple containing:
        - external_dossier_id (str)
        - original_file (OriginalFile)
        - dossier (Dossier)
        - semantic_documents (list[SemanticDocument])
    """

    state_machine = StateMachine.objects.get(
        name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS
    )

    # Check that we have a semantic document state machine loaded
    assert (
        clientis_account.active_semantic_document_work_status_state_machine
        == state_machine
    )

    # Check that there is currently no a dossier state machine loaded
    assert clientis_account.active_work_status_state_machine is None

    # Create a dossier
    dossier = clientis_account_factory.create_dossier()

    external_dossier_id = dossier.external_id

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"
    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    mocker.patch("dossier.services_external.process_original_file")

    original_file = add_original_file(
        dossier=dossier,
        file=file,
        source=OriginalFileSource.API,
        create_user=User.objects.get(
            username="<EMAIL>"
        ),
    )[1]

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier, num_docs=1)

    assert len(semantic_documents) == 1
    # Pick a first one
    semantic_document = semantic_documents[0]
    semantic_document.title_suffix = "some_special_chars_äöü/-?"
    semantic_document.save()

    # Work status should have already been set by create semantic document function
    assert semantic_document.work_status == Status.objects.get(
        key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        state_machine=dossier.account.active_semantic_document_work_status_state_machine,
    )

    dossier.refresh_from_db()

    return external_dossier_id, original_file, dossier, semantic_documents


def test_dossier_end_to_end(
    mocked_get_dossier,
    clientis_authenticated_client,
    clientis_account,
    document_categories,
    set_clientis_JWK,
    mocker: MockerFixture,
    clientis_account_factory,
    get_user_or_create_clientis_user,
):
    # Test the whole process of creating a dossier, adding documents, exporting and checking status
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            clientis_account=clientis_account,
            clientis_account_factory=clientis_account_factory,
            mocker=mocker,
        )
    )

    dossier.external_id = "949.401678.001.110794"
    dossier.save()

    assert (
        dossier.account.semantic_document_export_strategy
        == SemanticDocumentExportStrategy.SWISSCOM_EDOSSIER_XML_ZIP
    )

    external_dossier_id = dossier.external_id

    semantic_document = semantic_documents[0]

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Request to generate an export
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=response.json()[0]
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Check that DEC has moved state from IN_FRONT_OFFICE to EXPORT_AVAILABLE
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )

    # Check we can poll for dossier status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:dossier-semantic-document-exports-available",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 1  # One export waiting (one doc)
    assert (
        parse[0].semantic_document_export_request_uuid == semantic_document_export.uuid
    )

    # Test we can also get the same via all-semantic-document-export-status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:all-semantic-document-export-status",
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 1  # One export waiting (one doc)
    assert (
        parse[0].semantic_document_export_request_uuid == semantic_document_export.uuid
    )

    dossier_export = SemanticDocumentExport.objects.filter(
        semantic_document__dossier__external_id=external_dossier_id
    ).first()

    export_url = dossier_export.file.fast_url

    with tempfile.TemporaryDirectory() as temp_dir:
        # Download zip to temp file
        zip_path = os.path.join(temp_dir, "export.zip")
        response = requests.get(export_url)
        with open(zip_path, "wb") as f:
            f.write(response.content)

        # Extract and verify contents
        with zipfile.ZipFile(zip_path) as zf:
            files = zf.namelist()
            assert len(files) == 2  # Should have PDF and XML

            # Find XML and PDF files
            xml_file = next(f for f in files if f.endswith(".xml"))
            pdf_file = next(f for f in files if f.endswith(".pdf"))

            assert Path(xml_file).stem == Path(pdf_file).stem

            # Extract and parse XML
            xml_content = zf.read(xml_file)
            xml_root = etree.fromstring(xml_content)

            xml_data = {
                child.tag: child.text if child.text is not None else ""
                for child in xml_root
            }

            # Convert boolean field
            xml_data["DirectArchive"] = xml_data["DirectArchive"].lower() == "true"

            # Convert date/datetime fields
            xml_data["Dokumentendatum"] = datetime.strptime(
                xml_data["Dokumentendatum"], "%Y-%m-%d"
            ).date()
            xml_data["Scandatum"] = datetime.fromisoformat(xml_data["Scandatum"])

            # Parse into ClientisExportMetadata
            ClientisExportMetadata.model_validate(xml_data)

            # Verify PDF exists and has content
            pdf_content = zf.read(pdf_file)
            assert len(pdf_content) > 0

            # Verify that the Dokumentenname property in the XML matches the filename of the PDF file
            assert (
                xml_data["Dokumentenname"] == pdf_file
            ), f"Expected Dokumentenname '{xml_data['Dokumentenname']}' to match PDF filename '{pdf_file}'"

    # Set from their end, that we have fetched the export
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-semantic-document-export-in-progress",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
    )
    assert response.status_code == 200

    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:dossier-semantic-document-exports-available",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)

    # No documents in ready for export status
    assert len(parse) == 0

    # There should be one with Export in progress
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:dossier-semantic-document-export-in-progress",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)

    assert (
        parse[0].semantic_document_work_status
        == SemanticDocumentState.EXPORT_IN_PROGRESS
    )

    # Check we can set the export as done (success case)
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-semantic-document-export-set-done",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
    )

    affected_export_uuids = response.json()
    assert affected_export_uuids[0] == str(semantic_document_export.uuid)
    assert len(affected_export_uuids) == 1

    # Check we can poll again for dossier status, to check that it is removed from export status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:dossier-semantic-document-exports-available",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    # Should not be in the list anymore
    assert response.json() == []

    # Test we can also get the same via all-semantic-document-export-status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:all-semantic-document-export-status",
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 0

    # Reset document to ready for export, to test

    reset_semantic_document_export(
        SemanticDocument.objects.filter(uuid=semantic_document.uuid)
    )

    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200

    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-semantic-document-export-set-done",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
    )

    assert response.status_code == 200


def test_dossier_close_workflow(
    mocked_get_dossier,
    document_categories,
    clientis_authenticated_client,
    clientis_account,
    set_clientis_JWK,
    mocker: MockerFixture,
    clientis_account_factory,
    get_user_or_create_clientis_user,
):
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            clientis_account=clientis_account,
            mocker=mocker,
            clientis_account_factory=clientis_account_factory,
        )
    )

    # Set it to status processed
    original_file.status = FileStatus.PROCESSED
    original_file.save()

    # Expect initial state open
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.OPEN.value
    )

    # Dossier is Open, expect read-write access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_WRITE)

    assert (
        schemas.DossierStatusResponse.model_validate_json(
            clientis_authenticated_client.get(
                path=reverse(
                    "clientis-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.OPEN.value
    )

    mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Check whether a dossier can be closed, should return false as semantic documents have not been
    # exported yet
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: schemas.DossierCloseReadyResponse = (
        schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    print(res_check_dossier_close_ready)

    assert res_check_dossier_close_ready == schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=0,
        num_documents_export_not_started=1,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        ready_for_close=False,
        msg_nok_de="Es gibt noch Dokumente (1), die nicht aus HypoDossier übertragen wurden. Gehen Sie zu HypoDossier und benennen Sie jedes Dokument im Abschnitt 'Zuweisung / Dokumententrennung offen'. Anschliessend Dokumente übertragen.",
        msg_nok_en="There are still documents (1) that are not transferred from HypoDossier. Go to HypoDossier and rename each of the documents in section 'Assignment / Document separation open'. Afterwards transfer documents.",
        msg_nok_fr="Il reste encore des documents (1) qui n'ont pas été transférés depuis HypoDossier. Accédez à HypoDossier et renommez chaque document dans la section 'Affectation / Séparation des documents ouverte'. Ensuite, transférez les documents.",
        msg_nok_it="Ci sono ancora documenti (1) che non sono stati trasferiti da HypoDossier. Accedi a HypoDossier e rinomina ciascun documento nella sezione 'Assegnazione / Separazione documenti aperta'. Successivamente trasferisci i documenti.",
    )

    # Try to close the dossier - should fail

    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 400

    res_close_dossier = Message.model_validate_json(response.content)

    assert res_close_dossier == Message(
        detail="Transition not allowed from Default Dossier State Machine/OPEN to Default Dossier State Machine/CLOSED or conditions not fulfilled. Only the following next states are allowed: ['CLOSING']."
    )

    # Dossier is still Open,
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.OPEN.value
    )
    # expect read-write access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_WRITE)

    # Set the dossier to closing and dispatch export of semantic documents
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:prepare-close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_prepare_close_dossier = schemas.PrepareCloseDossierResponse.model_validate_json(
        response.content
    )

    assert res_prepare_close_dossier == schemas.PrepareCloseDossierResponse(
        success=True,
        exports_triggered_count=1,
        detail="Dossier moved to Closing state",
        close_result=None,
    )

    assert (
        schemas.DossierStatusResponse.model_validate_json(
            clientis_authenticated_client.get(
                path=reverse(
                    "clientis-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.CLOSING.value
    )

    # Dossier is Closing,
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.CLOSING.value
    )
    # expect read only access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_ONLY)

    # Check whether a dossier can be closed, should return True
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: schemas.DossierCloseReadyResponse = (
        schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    assert res_check_dossier_close_ready == schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=1,
        num_documents_export_not_started=0,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        ready_for_close=True,
        msg_nok_de=None,
        msg_nok_en=None,
        msg_nok_fr=None,
        msg_nok_it=None,
    )

    # Finally close the dossier
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_close_dossier = schemas.DossierCloseResponse.model_validate_json(
        response.content
    )

    assert res_close_dossier == schemas.DossierCloseResponse(
        success=True, msg_nok_de=None, msg_nok_en=None, msg_nok_fr=None, msg_nok_it=None
    )

    assert (
        schemas.DossierStatusResponse.model_validate_json(
            clientis_authenticated_client.get(
                path=reverse(
                    "clientis-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.CLOSED.value
    )

    # Dossier is Closed,
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.CLOSED.value
    )
    # expect read only access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_ONLY)


@pytest.mark.django_db(transaction=True)  # Avoid deadlock in parallel execution
def test_dossier_cant_close_original_file_processing(
    mocked_get_dossier,
    document_categories,
    clientis_authenticated_client,
    clientis_account,
    set_clientis_JWK,
    mocker: MockerFixture,
    clientis_account_factory,
    get_user_or_create_clientis_user,
):
    # Test the case where we have an original file in processing state
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            clientis_account=clientis_account,
            mocker=mocker,
            clientis_account_factory=clientis_account_factory,
        )
    )

    mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:prepare-close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_prepare_close_dossier = schemas.PrepareCloseDossierResponse.model_validate_json(
        response.content
    )

    assert res_prepare_close_dossier == schemas.PrepareCloseDossierResponse(
        success=True,
        exports_triggered_count=1,
        detail="Dossier moved to Closing state",
        close_result=None,
    )

    # Check whether a dossier can be closed, should return false as semantic documents have not been
    # exported yet
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: schemas.DossierCloseReadyResponse = (
        schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    assert res_check_dossier_close_ready == schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=1,
        num_documents_export_not_started=0,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=1,
        ready_for_close=False,
        msg_nok_de="Es gibt noch 1 Originaldateien in Verarbeitung. Bitte warten Sie, bis die Verarbeitung abgeschlossen ist.",
        msg_nok_en="There are still 1 original files being processed. Please wait until processing is complete.",
        msg_nok_fr="Il y a encore 1 fichiers originaux en cours de traitement. Veuillez attendre que le traitement soit terminé.",
        msg_nok_it="Ci sono ancora 1 file originali in elaborazione. Attendere il completamento dell'elaborazione.",
    )

    # Try to close the dossier - should fail
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 400

    res_close_dossier = Message.model_validate_json(response.content)

    # Show anyway is disabled for all transitions. Therefore no valid target state for the transition
    assert res_close_dossier == Message(
        detail="Transition not allowed from Default Dossier State Machine/CLOSING to Default Dossier State Machine/CLOSED or conditions not fulfilled. Only the following next states are allowed: []."
    )


def test_dossier_early_close_workflow(
    mocked_get_dossier,
    document_categories,
    clientis_authenticated_client,
    clientis_account,
    set_clientis_JWK,
    mocker: MockerFixture,
    clientis_account_factory,
    get_user_or_create_clientis_user,
):
    # Test the case where we can close at prepare-close-dossier stage, as all semantic documents are exported

    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            clientis_account=clientis_account,
            mocker=mocker,
            clientis_account_factory=clientis_account_factory,
        )
    )

    # Set it to status processed
    original_file.status = FileStatus.PROCESSED
    original_file.save()

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Request to generate an export
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    # Check whether a dossier can be closed, should return True
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: schemas.DossierCloseReadyResponse = (
        schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    assert res_check_dossier_close_ready == schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=1,
        num_documents_export_not_started=0,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        ready_for_close=True,
        msg_nok_de=None,
        msg_nok_en=None,
        msg_nok_fr=None,
        msg_nok_it=None,
    )

    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:prepare-close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_prepare_close_dossier = schemas.PrepareCloseDossierResponse.model_validate_json(
        response.content
    )

    assert res_prepare_close_dossier == schemas.PrepareCloseDossierResponse(
        success=True,
        exports_triggered_count=0,
        detail="Dossier moved to Closed state",
        close_result=DossierCloseResponse(
            success=True,
            msg_nok_de=None,
            msg_nok_en=None,
            msg_nok_fr=None,
            msg_nok_it=None,
        ),
    )

    assert (
        schemas.DossierStatusResponse.model_validate_json(
            clientis_authenticated_client.get(
                path=reverse(
                    "clientis-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.CLOSED.value
    )


def test_get_dossier_status_success(
    mocked_get_dossier,
    document_categories,
    clientis_authenticated_client,
    clientis_account,
    set_clientis_JWK,
    mocker: MockerFixture,
    clientis_account_factory,
    get_user_or_create_clientis_user,
):
    """Test successful retrieval of dossier status"""
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            clientis_account=clientis_account,
            mocker=mocker,
            clientis_account_factory=clientis_account_factory,
        )
    )

    # Set dossier to CLOSING state
    closing_state = Status.objects.get(
        key=DossierState.CLOSING.value,
        state_machine=dossier.account.active_work_status_state_machine,
    )
    dossier.work_status = closing_state
    dossier.save()

    # Request dossier status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:dossier-status",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    status_response = schemas.DossierStatusResponse.model_validate_json(
        response.content
    )
    assert status_response.status_key == DossierState.CLOSING.value


def test_get_dossier_status_no_status(
    mocked_get_dossier,
    document_categories,
    clientis_authenticated_client,
    clientis_account,
    set_clientis_JWK,
    mocker: MockerFixture,
    clientis_account_factory,
    get_user_or_create_clientis_user,
):
    """Test response when dossier has no status set"""
    # Setup test dossier without setting status
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            clientis_account=clientis_account,
            mocker=mocker,
            clientis_account_factory=clientis_account_factory,
        )
    )

    # Clear work status
    dossier.work_status = None
    dossier.save()

    # Request dossier status
    response = clientis_authenticated_client.get(
        path=reverse(
            "clientis-api:dossier-status",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 404
    error_response = Message.model_validate_json(response.content)
    assert error_response.detail == "No status set for this dossier"


def test_update_export_status_error(
    mocked_get_dossier,
    clientis_authenticated_client,
    clientis_account,
    document_categories,
    set_clientis_JWK,
    mocker: MockerFixture,
    clientis_account_factory,
    get_user_or_create_clientis_user,
):
    # Test the whole process of creating a dossier, adding documents, exporting and checking status
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            clientis_account=clientis_account,
            clientis_account_factory=clientis_account_factory,
            mocker=mocker,
        )
    )

    dossier.external_id = "949.401678.001.110794"
    dossier.save()

    external_dossier_id = dossier.external_id

    semantic_document = semantic_documents[0]

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Request to generate an export
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=response.json()[0]
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-semantic-document-export-set-error",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
        data=json.dumps({"error_message": "Test error message"}),
        content_type="application/json",
    )

    assert len(response.json()) == 1
    semantic_document_export.refresh_from_db()
    assert semantic_document_export.error_message == "Test error message"

    # Update it to set done, if we've fixed the error state
    response = clientis_authenticated_client.post(
        path=reverse(
            "clientis-api:dossier-semantic-document-export-set-done",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
    )

    assert response.status_code == 200
