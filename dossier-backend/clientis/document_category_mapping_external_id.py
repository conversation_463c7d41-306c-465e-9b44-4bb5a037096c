import csv
import re
from collections import OrderedDict
from pathlib import Path
from typing import TypedDict, List


from dossier.models import DocumentCategory, Account


def strip_end_number(text):
    # Use regex to remove the trailing number in parentheses
    return re.sub(r"\(\d+\)$", "", text).strip()


class DocumentCategoryMapping(TypedDict):
    id_external: str
    fr: str
    de: str


def parse_document_category_csv_clientis(
    file_path: Path | str,
) -> OrderedDict[str, DocumentCategoryMapping]:
    """
    Reads a UTF-8 encoded CSV file and returns an OrderedDict with keys from the first column
    and values from the second column. The first line is assumed to be a header.

    Input format looks like this (left is HD document_category key, right is custom id of client):

    # Kategorie: Category
    # Name: Name
    # Titel DE: Title (German)
    # Titel FR: Title (French)
    # Titel EN: Title (English)
    # Titel IT: Title (Italian)
    # Beschreibung: Description
    # Misc Type: Miscellaneous Type
    # CLI_Dokttyp: CLI Document Type
    # CLI_Doktyp_Bezeichnung_d: CLI Document Type Designation (German)
    # CLI_Doktyp_Bezeichnung_f: CLI Document Type Designation (French)
    # Bemerkung Martin: Martin's Note/Comment

    :param file_path: Path to the input CSV file.
    :return: OrderedDict where keys are from the first column and values from the second.
    """
    ordered_dict = OrderedDict()

    # Open the CSV file with UTF-8 encoding
    with open(file_path, encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile, delimiter=";")

        for row in reader:
            name = row["Name"]
            # Construct the inner dictionary
            ordered_dict[name] = {
                # Use column "J" (CLI_Dokttyp) as the document_category.id_external
                "id_external": row["CLI_Dokttyp"],
                # Use column "L" (CLI_Doktyp_Bezeichnung_f) as document_category.fr ((but remove the number in round brackets)
                "fr": strip_end_number(row["CLI_Doktyp_Bezeichnung_f"]),
                # Use column "K" (CLI_Doktyp_Bezeichnung_d) as document_category.de,en,it (but remove the number in round
                # brackets. So "3.06.01_Diverse Dokumente Finanzieren" (200810)" and not "3.06.01_Diverse Dokumente
                # Finanzieren (200810)"
                "de": strip_end_number(row["CLI_Doktyp_Bezeichnung_d"]),
            }

    return ordered_dict


def update_external_id_to_document_categories_clientis(
    account: Account, document_categories_mapping_csv_path: Path
) -> tuple[bool, list[str], list[str]]:
    """
    Loop over all document categories and override the field "id_external" based on the mapping
    in a static csv file. It is expected that all document categories of the catalog (standard and custom bcge
    categories must be present in the mapping file).
    @param account_key:
    @return:
    """
    mapping: OrderedDict[str, DocumentCategoryMapping] = (
        parse_document_category_csv_clientis(document_categories_mapping_csv_path)
    )

    updates = []
    doc_cats: List[DocumentCategory] = list(
        DocumentCategory.objects.filter(account=account, name__in=mapping.keys())
    )
    for dc in doc_cats:
        dc.id_external = mapping[dc.name]["id_external"]
        dc.de_external = mapping[dc.name]["de"]
        dc.en_external = mapping[dc.name]["de"]
        dc.fr_external = mapping[dc.name]["fr"]
        dc.it_external = mapping[dc.name]["de"]
        dc.save()
        updates.append(dc.name)

    not_found = list(
        DocumentCategory.objects.filter(account=account)
        .exclude(name__in=mapping.keys())
        .values_list("name", flat=True)
    )

    # These are added/updated in code at a later stage for custom prop docs for clients
    prop_individual_doccat_names = ["CLIENTIS_DCB_ARBEITSHILFE_FIN"]
    for doccat_name in prop_individual_doccat_names:
        if doccat_name in not_found:
            not_found.remove(doccat_name)

    success = len(not_found) == 0
    return success, updates, not_found
