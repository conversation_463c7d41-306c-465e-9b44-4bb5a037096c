import djclick as click
import structlog
from django.contrib.auth import get_user_model
from faker import Faker

from clientis.factories import (
    ClientisAccountFactoryFaker,
    load_clientis_document_categories,
    update_or_create_semantic_document_workflow,
)
from clientis.schemas.schemas import AccountName
from dossier.models import Account
from semantic_document.models import SemanticDocument
from dossier.helpers_timezone import create_faker_past_datetime_with_timezone

User = get_user_model()
logger = structlog.get_logger()

# Get all enabled account keys from the AccountName enum
ENABLED_ACCOUNT_KEYS = AccountName._member_map_.values()

TEST_DOSSIERS_DATA = [
    {"name": "Test Testet /Test Testine", "external_id": "8397.********.001.101889"},
    {"name": "Test Tester", "external_id": "8397.********.001.101890"},
    {"name": "Test Testerli", "external_id": "8397.********.001.101891"},
    {"name": "Test AG", "external_id": "8397.********.001.101892"},
    {"name": "Testmitarbeiter Max", "external_id": "6450.********.001.104075"},
    {"name": "Test Maya", "external_id": "6450.********.001.104076"},
    {"name": "Testgesellschaft", "external_id": "6450.********.001.104077"},
]


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key", required=False)
def load_account(
    account_key: str | None = None,
):
    """
    Create clientis account(s) with close to production config. This loads
    document categories.

    When called without an account_key, prints commands to update all known accounts.
    When called with an account_key, updates that specific account.

    Example:

    python manage.py reset-db
    python manage.py load_clientis_data load-account clientistest
    python manage.py load_clientis_data load-account dcb

    """
    if account_key is None:

        for key in ENABLED_ACCOUNT_KEYS:
            print(f"python manage.py load_clientis_data load-account {key}")
        return

    if account_key not in AccountName._member_map_.values():
        logger.error(
            f"Account name {account_key} not in allowed keys {list(AccountName._member_map_.values())}"
        )
        return

    Faker.seed(234777)
    bfac = ClientisAccountFactoryFaker(
        account_key=account_key,
    )
    bfac.load_initial_document_categories()

    bfac.account.save()


@grp.command()
@click.argument("account_key", required=False)
def update_document_categories(
    account_key: str | None = None,
):
    """
    Load / update all document categories for clientis. Do not rely on the factory here
    as the factory changes the account itself on initialization.
    python manage.py load_clientis_data update-document-categories clientistest
    python manage.py load_clientis_data update-document-categories dcb
    @param account_key:
    @return:
    """

    if account_key is None:
        print("No account key provided, not updating any accounts.")
        print(
            "Run some or all of these commands to update the document categories for Clients banks:"
        )
        for key in ENABLED_ACCOUNT_KEYS:
            print(
                f"python manage.py load_clientis_data update-document-categories {key}"
            )
        return

    try:
        Account.objects.get(key=account_key)
    except Account.DoesNotExist:
        logger.error(
            f"Account {account_key} does not exist. Run without account_key to see all accounts."
        )
        return

    success_mapping, updates, not_found = load_clientis_document_categories(account_key)

    if success_mapping:
        logger.info(
            "Document categories successfully updated",
            account_key=account_key,
            success_mapping=success_mapping,
            updates=updates,
            not_found=not_found,
        )
    else:
        logger.warning(
            "Document categories updated with issues",
            account_key=account_key,
            success_mapping=success_mapping,
            updates=updates,
            not_found=not_found,
        )


@grp.command()
@click.argument("account_key", required=False)
def load_semantic_document_workflow(
    account_key: str | None = None,
):
    """
    Load / update semantic document workflow for clientis accounts.
    python manage.py load_clientis_data load-semantic-document-workflow clientistest
    python manage.py load_clientis_data load-semantic-document-workflow dcb
    @param account_key:
    @return:
    """

    if account_key is None:
        print("No account key provided, not updating any accounts.")
        print(
            "Run some or all of these commands to update the semantic document workflow for Clients banks:"
        )
        for key in ENABLED_ACCOUNT_KEYS:
            print(
                f"python manage.py load_clientis_data load-semantic-document-workflow {key}"
            )
        return

    try:
        account = Account.objects.get(key=account_key)
    except Account.DoesNotExist:
        logger.error(
            f"Account {account_key} does not exist. Run without account_key to see all accounts."
        )
        return

    # Update the semantic document workflow for the account
    updated_account = update_or_create_semantic_document_workflow(account)
    updated_account.save()
    logger.info(
        "Semantic document workflow updated successfully",
        account_key=account_key,
        frontend_theme=updated_account.frontend_theme,
        enable_semantic_document_export=updated_account.enable_semantic_document_export,
        dossier_close_strategy=updated_account.dossier_close_strategy,
    )

    # Find semantic documents without a work status in a single efficient query
    semantic_documents_without_status = SemanticDocument.objects.filter(
        dossier__account=account, work_status__isnull=True
    )

    # Get the initial work status from the state machine
    initial_work_status = (
        updated_account.active_semantic_document_work_status_state_machine.start_status
    )

    # Update the semantic documents with the initial work status
    count = semantic_documents_without_status.count()
    if count > 0:
        semantic_documents_without_status.update(work_status=initial_work_status)
        logger.info(
            f"Updated {count} semantic documents with initial work status",
            account_key=account_key,
            initial_work_status=initial_work_status.key,
        )
    else:
        logger.info(
            "No semantic documents found without work status",
            account_key=account_key,
        )


@grp.command()
@click.argument("account_key", required=False)
def load_test_data(account_key: str | None = None):
    """
    Create test dossiers with predefined data for clientis accounts.
    python manage.py load_clientis_data load-test-data clientistest
    python manage.py load_clientis_data load-test-data dcbtest
    @param account_key: The account key to create test dossiers for
    @return:
    """
    if account_key is None:
        print(
            "No account key provided. Please provide one of the following account keys:"
        )
        for key in ENABLED_ACCOUNT_KEYS:
            print(f"python manage.py load_clientis_data load-test-data {key}")
        return

    try:
        Account.objects.get(key=account_key)
    except Account.DoesNotExist:
        logger.error(f"Account {account_key} does not exist.")
        return

    factory = ClientisAccountFactoryFaker(account_key=account_key)

    for dossier_data in TEST_DOSSIERS_DATA:
        # Create dossier
        dossier = factory.create_dossier()

        # Update dossier with test data
        # Add external_id to the name and include production status
        prod_status = (
            "(auch auf PRD)"
            if dossier_data["external_id"].startswith("8397")
            else "(bis Clone (21.04.))"
        )
        dossier.name = (
            f"{dossier_data['name']} {prod_status} {dossier_data['external_id']}"
        )
        dossier.external_id = dossier_data["external_id"]

        # Set created_at to a date in the past (not the future)
        dossier.created_at = create_faker_past_datetime_with_timezone(
            factory.faker, days_in_the_past=5
        )

        dossier.save()

        logger.info(
            "Created test dossier",
            dossier_name=dossier.name,
            external_id=dossier.external_id,
        )

    logger.info(f"Successfully created {len(TEST_DOSSIERS_DATA)} test dossiers")
