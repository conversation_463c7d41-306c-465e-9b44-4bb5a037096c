from enum import Enum
from typing import List, Optional, Annotated

from lxml import etree
from ninja.errors import HttpError
from pydantic import (
    StringConstraints,
)
from dossier.schemas import EntityTypes, AccessMode
from dossier import schemas_external
from pydantic import ValidationError as PydanticValidationError

from uuid import UUID

from dateutil.tz import tz
from datetime import datetime, date

from core.generics import AnyHttpUrlStr
from dossier.statemachine_types import DossierState
from semantic_document.schemas import Confidence
from pydantic import BaseModel, Field, field_validator

from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
)

ExternalDossierID = Annotated[
    str, StringConstraints(max_length=255, pattern="[A-Za-z0-9-]{1,255}")
]
PrincipalID = Annotated[str, StringConstraints(max_length=255)]
DossierName = Annotated[str, StringConstraints(max_length=255)]

# In clients terminology this is Mandant.Kundennummer.Rahmennummer.Antragsnummer (KundenNR is unformatted, so without ‘.’)
#
# external_id == bank_id + '.' + client_id + '.' + framecontract_id + '.' + 'application_id'

# Example: 949.401678.001.110794

ClientisExternalDossierID = Annotated[
    str,
    StringConstraints(
        max_length=255,
        pattern=r"^[A-Za-z0-9-]+\.[A-Za-z0-9-]+\.[A-Za-z0-9-]+\.[A-Za-z0-9-]+$",
    ),
]


class ClientisExternalId(BaseModel):
    external_id: ClientisExternalDossierID = Field(
        ..., description="Mandant.Kundennummer.Rahmennummer.Antragsnummer"
    )

    @field_validator("external_id")
    def validate_external_id(cls, v: str) -> str:
        """
        Validates that the external_id follows the format:
        Mandant.Kundennummer.Rahmennummer.Antragsnummer

        Example: 949.401678.001.110794

        Rules:
        - Must contain exactly 4 parts separated by dots
        - No part can be empty
        - Only allows alphanumeric characters and hyphens
        - Maximum length 255 characters
        """
        # Check total length
        if len(v) > 255:
            raise ValueError("External ID must not exceed 255 characters")

        # Split into components
        parts = v.split(".")

        # Must have exactly 4 parts
        if len(parts) != 4:
            raise ValueError(
                "External ID must contain exactly 4 parts separated by dots"
            )

        # Check each part
        for i, part in enumerate(parts):
            # Check if part is empty
            if not part:
                raise ValueError(f"Part {i + 1} cannot be empty")

            # Check for valid characters (alphanumeric and hyphen only)
            if not all(c.isalnum() or c == "-" for c in part):
                raise ValueError(
                    f"Part {i + 1} contains invalid characters. Only alphanumeric and hyphens allowed"
                )

        return v


def api_validate_dossier_external_id_format(external_dossier_id: str):
    try:
        # Validate external id matches required schema
        ClientisExternalId(external_id=external_dossier_id)
    except PydanticValidationError as e:
        raise HttpError(422, f"Invalid external ID format: {str(e)}")


class AccountName(str, Enum):
    # These strings are used as account keys
    bbr = "bbr"
    boa = "boa"
    cba = "cba"
    cbo = "cbo"
    cbt = "cbt"
    cco = "cco"
    cec = "cec"
    dcb = "dcb"
    ebs = "ebs"
    eks = "eks"
    spc = "spc"
    zlb = "zlb"
    bbrtest = "bbrtest"
    boatest = "boatest"
    cbatest = "cbatest"
    cbotest = "cbotest"
    cbttest = "cbttest"
    ccotest = "ccotest"
    cectest = "cectest"
    dcbtest = "dcbtest"
    ebstest = "ebstest"
    ekstest = "ekstest"
    spctest = "spctest"
    zlbtest = "zlbtest"
    clientistest = "clientistest"


class SemanticPage(BaseModel):
    uuid: UUID
    number: int = Field(
        description="Page number is zero based. First page has page number 0"
    )
    image_url: Optional[str] = None

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())


class SemanticDocument(BaseModel):
    uuid: UUID

    external_semantic_document_id: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ] = None

    title: str

    document_category_confidence: Confidence

    document_category_key: str

    semantic_pages: List[SemanticPage]

    entity_type: Optional[EntityTypes] = None
    entity_key: Optional[str] = None

    access_mode: AccessMode = AccessMode.READ_WRITE

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())


class ClientisExportMetadata(BaseModel):
    Mandant: str  # bank_id, e.g. "949"
    Kundennummer: str  # client_id, e.g. "401678"
    Dokumententyp: str  # document_category_id_external, e.g. "123456" which could be the mapping for "PENSION_CERTIFICATE"
    DokumentenID: str = (
        ""  # Should be empty, will be set by Swisscom during their import into eDossier
    )
    Auffindcode: str = "0"  # Always "0", e.g. "0"
    Dokumentenname: str  # semantic_document filename, e.g. "410 PK Ausweis Thiemann Manuel AXA Basis Kader 2017-03-01.pdf"
    DokumentenRECID: str = ""  # e.g. ""
    Dokumentendatum: date  # created_at of semantic_document, e.g. "2022-05-12"
    Kontonummer: str = ""  # e.g. ""
    Nummernkontocode: str = "0"  # e.g. "0"
    Personalcode: str = "0"  # e.g. "0"
    Portfolionummer: str = ""  # always empty
    Rahmennummer: str  # framecontract_id, e.g. "001". Must be empty for some list of document_category.id_external (e.g. personal documents)
    Scandatum: str  # created_at timezone aware of related original_file in format 2024-08-27 09:30, e.g. "2022-05-12 08:09"
    ClientAdvisorId: str = ""  # e.g. ""
    CreatorId: str  # TBD: user ID, e.g. "<EMAIL>"
    Dokumentenbezeichnung: str  # semantic_document title, e.g. "410 PK Ausweis Thiemann Manuel AXA Basis Kader 2017-03-01"
    Kommentar: str = ""  # e.g. ""
    Seiten: int  # Number of pages, e.g. 1
    DirectArchive: bool = False  # e.g. False
    SourceSystem: str = "HypoDossier"  # e.g. "HypoDossier"

    @field_validator("Scandatum", mode="before")
    def format_scandatum(cls, value):
        if isinstance(value, datetime):
            return value.strftime("%Y-%m-%d %H:%M")
        return value

    def to_xml(self) -> bytes:
        """Convert the model to XML bytes"""
        root = etree.Element("ClientisExport")

        # Add all fields as child elements
        for field_name, value in self.model_dump().items():
            child = etree.SubElement(root, field_name)
            # Convert dates/datetimes to string format
            if isinstance(value, (date, datetime)):
                child.text = value.isoformat()
            else:
                child.text = str(value)

        # Convert to bytes with XML declaration and proper encoding
        s = etree.tostring(
            root, xml_declaration=True, encoding="UTF-8", pretty_print=True
        )
        # If we have to modify/customize the xml we can do it here
        return s


class ExportStatusSemanticDocument(BaseModel):
    semantic_document_export_request_uuid: UUID
    semantic_document_uuid: UUID
    # We use AnyHttpUrl instead of HttpUrl as CI uses ports as part of the URL
    semantic_document_url: AnyHttpUrlStr
    external_dossier_id: Optional[ExternalDossierID] = None

    # Internal key, e.g. 'PASSPORT_CH'
    document_category_key: str

    # External unique identifier in eDossier that the document category should be mapped to, e.g. PASSPORT_IN_EDOSSIER
    document_category_id_external: Optional[str] = None

    updated_at: datetime

    semantic_document_work_status: SemanticDocumentState

    @field_validator("semantic_document_work_status", mode="before")
    @classmethod
    def parse_status(cls, value):
        if isinstance(value, str) and value.startswith("SemanticDocumentState."):
            # Strip the prefix and get just the enum value name
            return value.split(".")[1]
        return value


class DossierCloseReadyResponse(schemas_external.DossierCloseReadyResponse):
    pass


class DossierCloseResponse(schemas_external.DossierCloseResponse):
    pass


class PrepareCloseDossierResponse(schemas_external.PrepareCloseDossierResponse):
    pass


class DossierStatusResponse(BaseModel):
    """Schema for dossier status response"""

    status_key: Optional[DossierState] = None


class ErrorMessageSchema(BaseModel):
    error_message: str | None = None
