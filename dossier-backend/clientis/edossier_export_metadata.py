from dataclasses import dataclass
from typing import Dict, Optional, List
from pathlib import Path
import pandas as pd
from assets import ASSETS_PATH

# Global cache for export metadata
_EXPORT_METADATA_CACHE: Optional[Dict[int, "EdossierExportMetadata"]] = None


@dataclass
class EdossierExportMetadata:
    external_id: int
    title_de: str
    title_fr: str
    required_fields: str
    not_allowed_fields: str
    relevant_for_hd: bool
    allow_framecontract_id: bool
    require_account_number: bool


def load_export_metadata(
    file_path: Optional[Path] = None, use_cache: bool = True
) -> Dict[int, EdossierExportMetadata]:
    """
    Load and parse the Excel file containing export metadata.
    Results are cached after the first call if use_cache is True.

    Args:
        file_path: Path to the Excel file. If not provided, uses the default path
                  relative to ASSETS_PATH.
        use_cache: Whether to use cached results (if available) or force reload from file.
                  Defaults to True.

    Returns:
        Dictionary mapping external_id to EdossierExportMetadata objects
    """
    global _EXPORT_METADATA_CACHE

    # Return cached result if available and use_cache is True
    if use_cache and _EXPORT_METADATA_CACHE is not None:
        return _EXPORT_METADATA_CACHE

    if file_path is None:
        file_path = (
            ASSETS_PATH
            / "document_category"
            / "clientis"
            / "document_category_mapping"
            / "20250127_Mapping_Hypodossier_Spezifikation_Aktenplan_DEFAULT.xlsx"
        )

    # Read the Excel file, specifically from the "CLI_Aktenplan Extrakt" tab
    df = pd.read_excel(file_path, sheet_name="CLI_Aktenplan Extrakt")

    # Print column names for debugging
    print("Column names:", df.columns.tolist())

    # Drop the first row which is a header
    df = df.iloc[1:]

    # Initialize the result dictionary
    metadata_dict: Dict[int, EdossierExportMetadata] = {}

    # Process each row
    for _, row in df.iterrows():
        # Convert relevant_for_hd to boolean based on column AD (index 29)
        relevant_for_hd = (
            str(row.iloc[29]).lower() == "true" if pd.notna(row.iloc[29]) else False
        )

        # Get not_allowed_fields and determine allow_framecontract_id
        not_allowed_fields = str(row.iloc[14]) if pd.notna(row.iloc[14]) else ""
        allow_framecontract_id = "CreditNr" not in not_allowed_fields

        # Get required_fields and determine require_account_number
        required_fields = str(row.iloc[13]) if pd.notna(row.iloc[13]) else ""
        require_account_number = "accountNr" in required_fields

        # Convert external_id to integer
        external_id = int(row.iloc[0])

        # Get values by position since we know the column order
        metadata = EdossierExportMetadata(
            external_id=external_id,  # First column
            title_de=str(row.iloc[1]),  # Second column
            title_fr=str(row.iloc[2]),  # Third column
            required_fields=required_fields,  # Column N
            not_allowed_fields=not_allowed_fields,  # Column O
            relevant_for_hd=relevant_for_hd,
            allow_framecontract_id=allow_framecontract_id,
            require_account_number=require_account_number,
        )

        metadata_dict[metadata.external_id] = metadata

    # Cache the result if use_cache is True
    if use_cache:
        _EXPORT_METADATA_CACHE = metadata_dict

    return metadata_dict


def get_external_ids_with_account_number(file_path: Optional[Path] = None) -> List[int]:
    """
    Load metadata and return a list of external_ids (as integers) where require_account_number is True.

    Args:
        file_path: Optional path to the Excel file. If not provided, uses the default path.

    Returns:
        List of external_ids as integers, in the order they appear in the file.
    """
    metadata_dict = load_export_metadata(file_path)

    # Filter for entries where require_account_number is True and keep ids as integers
    external_ids = [
        external_id
        for external_id, metadata in metadata_dict.items()
        if metadata.require_account_number
    ]

    return external_ids


def get_external_ids_allow_framecontract_id(
    file_path: Optional[Path] = None,
) -> List[int]:
    """
    Load metadata and return a list of external_ids (as integers) where allow_framecontract_id is True.

    Args:
        file_path: Optional path to the Excel file. If not provided, uses the default path.

    Returns:
        List of external_ids as integers, in the order they appear in the file.
    """
    metadata_dict = load_export_metadata(file_path)

    # Filter for entries where allow_framecontract_id is True and keep ids as integers
    external_ids = [
        external_id
        for external_id, metadata in metadata_dict.items()
        if metadata.allow_framecontract_id
    ]

    return external_ids
