from typing import List, Optional, Annotated
from uuid import UUID

import structlog
from django.conf import settings
from django.db import transaction
from ninja import NinjaAPI, Path
from ninja.security import HttpBearer
from dossier import services_external as dossier_services_external

from clientis.services import (
    create_export_status_object_list,
)
from dossier.models import Dossier
from dossier.schemas_external import PrepareCloseDossierResponse
from semantic_document.services_external import update_semantic_document_export_status
from core.schema import Message
from dossier.services_external import (
    get_dossier_with_access_check_api,
    perform_dossier_close_api_wrapper,
    check_dossier_ready_for_closing,
    create_dossier_close_ready_response,
    create_dossier_state_context,
    prepare_dossier_for_closing,
)
from projectconfig.authentication import (
    authenticate_from_account,
)
from ninja.errors import ValidationError, HttpError
from dossier.helpers_api import handle_api_validation_error

from finnova.api import create_shared_router as finnova_shared_router
import clientis.schemas.schemas as clientis_schemas
from semantic_document.services import (
    set_dossier_semantic_documents_state_ready_for_export,
    api_set_semantic_document_ready_for_export,
    map_confidence,
)
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
)
from statemgmt.services import StateTransitionError
from workers.models import SemanticDocumentExport

logger = structlog.get_logger()


class ClientisJWTAuth(HttpBearer):
    def authenticate(self, request, token, *args, **kw):
        jwt = authenticate_from_account(token)
        if settings.API_ROLE not in jwt.user_roles:
            return
        return jwt


api = NinjaAPI(
    title="Hypodossier - Clientis API",
    csrf=False,
    auth=ClientisJWTAuth(),
    urls_namespace="clientis-api",
    version="0.1.0",
    servers=[],
)


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


# Mount the finnova API under root path
api.add_router("", finnova_shared_router())


@api.get(
    "/dossier/{external_dossier_id}/semantic-documents",
    response={200: List[clientis_schemas.SemanticDocument], 404: Message},
    url_name="semantic-documents",
    exclude_none=True,
)
def get_semantic_documents(request, external_dossier_id: str, show_pages: bool = False):
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_documents = dossier_services_external.get_semantic_documents(
        dossier=dossier,
        semantic_document_serializer=clientis_schemas.SemanticDocument,
        page_serializer=clientis_schemas.SemanticPage,
        map_confidence=map_confidence,
        show_pages=show_pages,
    )

    return semantic_documents


@api.get(
    "/export/all-semantic-documents-ready-for-export",
    response=List[clientis_schemas.ExportStatusSemanticDocument],
    url_name="all-semantic-document-export-status",
    exclude_none=True,
    description="Get the status of all semantic document exports available for an account",
)
def get_all_semantic_document_ready_for_export(request):

    dossier_user = request.auth.get_user_or_create()

    export_semantic_docs = (
        SemanticDocumentExport.objects.filter(
            semantic_document__dossier__account=dossier_user.account,
            # Ignore all dossiers, that have no external id
            semantic_document__dossier__external_id__isnull=False,
            done__isnull=False,
        )
        .filter(
            semantic_document__work_status__key=SemanticDocumentState.EXPORT_AVAILABLE.value
        )
        .order_by("done")
        .select_related(
            "semantic_document",
            "semantic_document__dossier",
            "semantic_document__document_category",
        )
    )

    ret = create_export_status_object_list(export_semantic_docs)
    return ret


@api.post(
    "/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-state-ready-for-export",
    url_name="semantic-document-set-state-ready-for-export",
    description="Set the state of a Semantic document to ready for export, and dispatch export process. "
    "Will only process a document that has SemanticDocument.work_state=IN_FRONT_OFFICE",
    response={200: UUID, 204: None, 404: Message, 422: Message},
    include_in_schema=False,
)
def post_api_set_semantic_document_ready_for_export(
    request,
    external_dossier_id: Annotated[
        str, Path(validator=clientis_schemas.ClientisExternalId)
    ],
    semantic_document_uuid: UUID,
):

    # Validate external id matches required schema, as it is not done by the path validator
    clientis_schemas.api_validate_dossier_external_id_format(external_dossier_id)

    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    # This throws a 404 if the uuid does not exist
    result: Optional[UUID] = api_set_semantic_document_ready_for_export(
        dossier=dossier, semantic_document_uuid=semantic_document_uuid
    )

    if result is None:
        return 204, None  # No Content when the document was deleted

    return 200, result  # Return the UUID when available


@api.post(
    "/export/dossier/{external_dossier_id}/start-semantic-document-export",
    url_name="dossier-set-semantic-documents-state-ready-for-export",
    description="Set the state of Semantic documents within a dossier to ready for export, and dispatch export process. "
    "Will only process a document that has SemanticDocument.work_state=IN_FRONT_OFFICE. This returns a list of a semantic document export UUIDs (not semantic document UUIDs).",
    response=List[UUID],
    include_in_schema=False,
)
def start_semantic_document_export(
    request,
    external_dossier_id: Annotated[
        str, Path(validator=clientis_schemas.ClientisExternalId)
    ],
):
    """
    This deletes empty non-deleted documents without warning as it does not make sense to export them and we want
    a "fully exported" dossier after this call.
    @param request:
    @param external_dossier_id:
    @return:
    """

    # Validate external id matches required schema, as it is not done by the path validator
    clientis_schemas.api_validate_dossier_external_id_format(external_dossier_id)

    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    return set_dossier_semantic_documents_state_ready_for_export(dossier=dossier)


@api.get(
    "/export/{external_dossier_id}/semantic-document-exports-available",
    response=List[clientis_schemas.ExportStatusSemanticDocument],
    url_name="dossier-semantic-document-exports-available",
    exclude_none=True,
    description="Get the status of all semantic document exports available for a dossier",
)
def get_semantic_document_exports_available(request, external_dossier_id: str):

    request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semdoc_exports = (
        SemanticDocumentExport.objects.filter(
            semantic_document__dossier=dossier, done__isnull=False
        )
        .filter(
            semantic_document__work_status__key__in=[
                SemanticDocumentState.EXPORT_AVAILABLE.value,
            ]
        )
        .order_by("done")
        .select_related(
            "semantic_document",
            "semantic_document__dossier",
            "semantic_document__document_category",
        )
    )

    return create_export_status_object_list(semdoc_exports)


@api.get(
    "/export/{external_dossier_id}/semantic-document-exports-in-progress",
    response=List[clientis_schemas.ExportStatusSemanticDocument],
    url_name="dossier-semantic-document-export-in-progress",
    exclude_none=True,
    description="Get the status of all semantic document exports with status in progress",
)
def get_semantic_document_exports_in_progress(request, external_dossier_id: str):

    request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semdoc_exports = (
        SemanticDocumentExport.objects.filter(
            semantic_document__dossier=dossier, done__isnull=False
        )
        .filter(
            semantic_document__work_status__key__in=[
                SemanticDocumentState.EXPORT_IN_PROGRESS.value,
            ]
        )
        .order_by("done")
        .select_related(
            "semantic_document",
            "semantic_document__dossier",
            "semantic_document__document_category",
        )
    )

    return create_export_status_object_list(semdoc_exports)


@api.get(
    "/export/all-semantic-documents-available",
    response=List[clientis_schemas.ExportStatusSemanticDocument],
    url_name="all-semantic-document-export-status",
    exclude_none=True,
    description="Get the status of all semantic document exports available for an account",
)
def get_all_semantic_document_exports_available(request):

    dossier_user = request.auth.get_user_or_create()
    username = dossier_user.user.username
    if username == "<EMAIL>":
        valid_account_keys = ["clientistest"]
    elif username == "	service-account-clientis-swisscom-dev":
        # This is for the test account of Swisscom on clientistest and all bank test accounts
        valid_account_keys = [
            name.value for name in clientis_schemas.AccountName if "test" in name.value
        ]
    else:
        # Fallback, needs improvement: create list of valid account names based on whether username contains "test"
        if "test" in username.lower():
            valid_account_keys = [
                name.value
                for name in clientis_schemas.AccountName
                if "test" in name.value
            ]
        else:
            valid_account_keys = [
                name.value
                for name in clientis_schemas.AccountName
                if "test" not in name.value
            ]

    # Verify that the user's account is valid
    if dossier_user.account.key not in valid_account_keys:
        raise HttpError(403, f"Invalid account: {dossier_user.account.key}")

    export_semantic_docs = (
        SemanticDocumentExport.objects.filter(
            semantic_document__dossier__account__key__in=valid_account_keys,
            # Ignore all dossiers, that have no external id
            semantic_document__dossier__external_id__isnull=False,
            done__isnull=False,
        )
        .filter(
            semantic_document__work_status__key__in=[
                SemanticDocumentState.EXPORT_AVAILABLE.value,
                SemanticDocumentState.EXPORT_IN_PROGRESS.value,
            ]
        )
        .order_by("done")
        .select_related(
            "semantic_document",
            "semantic_document__dossier",
            "semantic_document__document_category",
        )
    )

    ret = create_export_status_object_list(export_semantic_docs)
    return ret


@api.post(
    "/export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-export-in-progress",
    response=List[UUID],
    url_name="dossier-semantic-document-export-in-progress",
    exclude_none=True,
    description="Set status of a semantic document to in progress. Returns UUID of affected export.",
)
@transaction.atomic
def update_export_status_in_progress(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    target_state = SemanticDocumentState.EXPORT_IN_PROGRESS
    return update_semantic_document_export_status(
        request, external_dossier_id, semantic_document_uuid, target_state
    )


@api.post(
    "/export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-done",
    response=List[UUID],
    url_name="dossier-semantic-document-export-set-done",
    exclude_none=True,
    description="Confirm successful export for a semantic document. Returns UUID of affected export.",
)
@transaction.atomic
def update_export_status_done(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    target_state = SemanticDocumentState.EXPORT_DONE
    return update_semantic_document_export_status(
        request, external_dossier_id, semantic_document_uuid, target_state
    )


@api.post(
    "/export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-error",
    response=List[UUID],
    url_name="dossier-semantic-document-export-set-error",
    exclude_none=True,
    description="Confirm unsuccessful export for a semantic document. Returns list of UUID of affected exports. This is one export in all normal successful operations.",
)
@transaction.atomic
def update_export_status_error(
    request,
    external_dossier_id: str,
    semantic_document_uuid: UUID,
    error_message: Optional[clientis_schemas.ErrorMessageSchema] = None,
):
    target_state = SemanticDocumentState.EXPORT_ERROR
    return update_semantic_document_export_status(
        request=request,
        external_dossier_id=external_dossier_id,
        semantic_document_uuid=semantic_document_uuid,
        target_state=target_state,
        error_message=error_message.error_message,
    )


# --- API's for handling closing dossier via finite state machine ---


@api.post(
    "/dossier/{external_dossier_id}/prepare-close-dossier",
    response={
        200: PrepareCloseDossierResponse,
        400: Message,
        404: Message,
    },
    url_name="prepare-close-dossier",
    description="Prepare a dossier for closing by setting state to CLOSING and making it read-only. "
    "If close_dossier_if_possible is True, also attempt to close a dossier if it is possible.",
)
def prepare_close_dossier(
    request, external_dossier_id: str, close_dossier_if_possible: bool = True
):
    """
    Prepares a dossier for closing by:
    1. Validating it can be closed
    2. Setting state to CLOSING
    3. Making it read-only
    4. Triggering semantic document exports if needed
    """
    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,
        external_dossier_id=external_dossier_id,
    )

    # Check if dossier can be closed as part of context
    context = create_dossier_state_context(
        dossier=dossier, is_system=True, is_user=False
    )

    status_code, response = prepare_dossier_for_closing(
        dossier=dossier,
        context=context,
        close_dossier_if_possible=close_dossier_if_possible,
    )

    return status_code, response


@api.get(
    "/dossier/{external_dossier_id}/check-dossier-close-ready",
    response={200: clientis_schemas.DossierCloseReadyResponse},
    url_name="check-dossier-close-ready",
    exclude_none=True,
)
def check_dossier_close_ready(request, external_dossier_id):
    """
    Check if a dossier is ready to be closed. If not return instructions what the user needs to do
    """
    dossier_user = request.auth.get_user_or_create()

    dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    stats, evaluation = check_dossier_ready_for_closing(dossier)
    res1 = create_dossier_close_ready_response(stats, evaluation)

    # We need to convert to Client specific pydantic schema
    res2: clientis_schemas.DossierCloseReadyResponse = (
        clientis_schemas.DossierCloseReadyResponse.model_validate(res1.model_dump())
    )
    return 200, res2


@api.post(
    "/dossier/{external_dossier_id}/close-dossier",
    response={200: clientis_schemas.DossierCloseResponse, 400: Message},
    url_name="close-dossier",
    exclude_none=True,
)
def close_dossier(request, external_dossier_id: str):
    """
    Check if a dossier can be closed and close it if all requirements are fulfilled.
    Return success (or not). In case it was not successful, instructions for user are returned.

    Rely on the state machine, dossier.work_status to ensure the dossier is read-only

    """
    dossier_user = request.auth.get_user_or_create()

    dossier: Dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,
        external_dossier_id=external_dossier_id,
    )
    try:

        success, evaluation = perform_dossier_close_api_wrapper(dossier)
    except StateTransitionError as e:
        return 400, {"detail": e.message}

    ret = clientis_schemas.DossierCloseResponse(
        success=success,
        msg_nok_de=evaluation.msg_nok_de,
        msg_nok_en=evaluation.msg_nok_en,
        msg_nok_fr=evaluation.msg_nok_fr,
        msg_nok_it=evaluation.msg_nok_it,
    )
    return 200, ret


@api.get(
    "/dossier/{external_dossier_id}/status",
    response={200: clientis_schemas.DossierStatusResponse, 404: Message},
    url_name="dossier-status",
    description="Get the current status of a dossier if one is set",
)
def get_dossier_status(request, external_dossier_id: str):
    """
    Returns the current status of a dossier if one is set.
    Requires authentication and proper access rights.
    """
    dossier_user = request.auth.get_user_or_create()

    dossier: Dossier = get_dossier_with_access_check_api(
        dossier_user=dossier_user,
        is_manager=True,
        external_dossier_id=external_dossier_id,
    )

    if not dossier.work_status:
        return 404, {"detail": "No status set for this dossier"}

    return 200, clientis_schemas.DossierStatusResponse(
        status_key=dossier.work_status.key,
    )
