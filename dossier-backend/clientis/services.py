from typing import List

import structlog
from pydantic import BaseModel
from django.utils import timezone

from clientis.schemas.schemas import ClientisExportMetadata
from semantic_document.models import SemanticDocument
from workers.models import SemanticDocumentExport
import clientis.schemas.schemas as clientis_schemas
from clientis.edossier_export_metadata import get_external_ids_allow_framecontract_id


logger = structlog.get_logger(__name__)


class ContractException(BaseModel):
    _class: str


class Msg(BaseModel):
    key: str
    message: str
    args: List[str] = []


class ServiceException(ContractException):
    msg: Msg


class PropertyFailure(BaseModel):
    path: str
    value: str
    proposal: str = None


class ValidationFailure(BaseModel):
    msg: Msg
    properties: List[PropertyFailure]


class ValidationException(ServiceException):
    failures: List[ValidationFailure]


def create_export_status_object_list(semdoc_exports: List[SemanticDocumentExport]):
    response_list = []

    for export in semdoc_exports:
        semantic_document_url = export.file.get_fast_url()
        doc_cat = export.semantic_document.document_category
        response_list.append(
            clientis_schemas.ExportStatusSemanticDocument(
                semantic_document_export_request_uuid=export.uuid,
                semantic_document_uuid=export.semantic_document.uuid,
                external_dossier_id=export.semantic_document.dossier.external_id,
                semantic_document_url=semantic_document_url,
                document_category_key=doc_cat.name,
                document_category_id_external=(
                    doc_cat.id_external if doc_cat.id_external else doc_cat.name
                ),
                updated_at=export.done,
                semantic_document_work_status=export.semantic_document.work_status.key,
            )
        )

    return response_list


def create_clientis_export_package(
    semantic_document: SemanticDocument,
    dokumentenname: str,
) -> ClientisExportMetadata:
    """
    Creates xml for a clientis export package
    :param semantic_document: Document to export
    :param dokumentenname: Target filename
    :return:
    """
    # Parse external_id components
    bank_id, client_id, framecontract_id, application_id = (
        semantic_document.dossier.external_id.split(".")
    )

    original_file_created_at = (
        semantic_document.semantic_pages.first().processed_page.processed_file.extracted_file.original_file.created_at
    )

    # Convert to local time and format as string
    local_created_at = timezone.localtime(
        original_file_created_at, timezone=timezone.get_current_timezone()
    )
    scandatum_str = local_created_at.strftime("%Y-%m-%d %H:%M")

    # Get the number of pages - only count non-deleted pages
    page_count = semantic_document.semantic_pages.alive().count()

    # Get list of document categories that allow framecontract_id
    external_ids_with_framecontract_id_allowed = (
        get_external_ids_allow_framecontract_id()
    )

    # Check if this document's category allows framecontract_id
    document_external_id = semantic_document.document_category.id_external

    # Check if document_external_id is empty
    if not document_external_id:
        error_message = "Document category id_external is empty"
        logger.error(
            error_message,
            document_category=semantic_document.document_category.name,
            document_uuid=semantic_document.uuid,
            dossier_uuid=semantic_document.dossier.uuid,
            account_key=semantic_document.dossier.account.key,
        )
        raise ValueError(
            f"{error_message}. Document category: {semantic_document.document_category.name}, "
            f"Document UUID: {semantic_document.uuid}, "
            f"Dossier UUID: {semantic_document.dossier.uuid}, "
            f"Account key: {semantic_document.dossier.account.key}"
        )

    if document_external_id and document_external_id.isnumeric():
        use_framecontract_id = (
            int(document_external_id) in external_ids_with_framecontract_id_allowed
        )
    else:
        use_framecontract_id = False

    # Create metadata
    # keep it as a pydantic type, and we can get the consumer to call
    # metadata.to_xml() to get the xml
    metadata = ClientisExportMetadata(
        Mandant=bank_id,
        Kundennummer=client_id,
        Dokumententyp=semantic_document.document_category.id_external,
        Dokumentenname=dokumentenname,  # Semantic document filename with extension
        Dokumentendatum=semantic_document.created_at.date(),
        Rahmennummer=framecontract_id if use_framecontract_id else "",
        Scandatum=scandatum_str,
        CreatorId=semantic_document.dossier.owner.username,
        Dokumentenbezeichnung=semantic_document.title,
        Seiten=page_count,  # Set the actual number of non-deleted pages as an integer
    )

    # Validate that Mandant can be cast to an integer
    try:
        int(metadata.Mandant)
    except ValueError:
        error_message = (
            f"Mandant value '{metadata.Mandant}' cannot be cast to an integer"
        )
        logger.error(error_message, metadata=metadata.dict())
        raise ValueError(f"{error_message}. Full metadata: {metadata.dict()}")

    return metadata
