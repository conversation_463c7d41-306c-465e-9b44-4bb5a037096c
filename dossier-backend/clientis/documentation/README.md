## HypoDossier Clientis API: Document Export and Dossier Closing

This document describes how to use the HypoDossier Clientis API to:

1.  **Export Semantic Documents:** Periodically check for documents ready for export, retrieve them (as ZIP archives containing a PDF and XML metadata), and confirm the export status.
2.  **Manage Dossier Closing:** Check if a dossier is ready to be closed, initiate the closing process, and finally close the dossier once all conditions are met.

**Target Audience:** Developers integrating external systems (like Swisscom eDossier) with HypoDossier.

**Base URL:** The specific base URL for the API will be provided separately (e.g., `https://hypodossier.example.com/partner/clientis/api/v1`).

### Prerequisites

*   **Authentication:** All API endpoints require authentication using a Bearer Token. You will receive a JWT token specific to your Clientis account with the necessary `API_ROLE`. Include this token in the `Authorization` header of your requests:
    ```
    Authorization: Bearer YOUR_JWT_TOKEN
    ```
*   **External Dossier ID Format:** Dossiers are identified using an `external_dossier_id`. For Clientis, this ID **must** follow the format `Mandant.Kundennummer.Rahmennummer.Antragsnummer` (e.g., `949.401678.001.110794`). Each part consists of alphanumeric characters and hyphens.

### Workflow Overview

#### 1. User Transfers Dossier to eDossier which locks the Dossier and triggers export process

![Transfer to eDossier](img/transfer_to_edossier.png)

![Transfer to eDossier](img/locked_documents.png)

![Transfer to eDossier](img/clients_semantic_document_state_machine.drawio.png)


[//]: # (```mermaid)

[//]: # (graph TD)

[//]: # (    subgraph Hypodossier sets state)

[//]: # (        A[Document in Front Office] --> |Transfer to eDossier| B[Ready for Export])

[//]: # (        B --> |Hypodossier Export Ready| C[Export Available])

[//]: # (    end)

[//]: # ()
[//]: # (    subgraph Clientis sets state)

[//]: # (        C --> |Clientis Sets Error State| D[Export Error])

[//]: # (        C --> |Clientis Downloads Export| E[Export in Progress])

[//]: # (        C --> |Clientis Confirms Download| F[Export Completed])

[//]: # (        D --> |Clientis Clears Error State| F)

[//]: # (        E --> |Clientis Confirms Download| F)

[//]: # (    end)

[//]: # ()
[//]: # (```)
#### 1. Semantic Document Export Workflow

The process involves polling for available documents, processing them one by one, and updating their status.

```mermaid
graph TD

    %% Subgraph for internal doc states

    O(Doc: EXPORT_AVAILABLE)

    style O fill:#e6f7ff,stroke:#333,stroke-width:1.5px,color:#000

    %% Polling and retrieval
    A[External System: Poll API] -->|GET /export/all-semantic-documents-available| B(HypoDossier: Returns List)
    O --> B

    %% Processing logic
    B --> C{Process Document?}
    C -->|Yes| D[External System: Set In Progress]
    D -->|POST /export/.../set-export-in-progress| E(HypoDossier: Doc status -> EXPORT_IN_PROGRESS)

    %% Download and archive
    E --> F[External System: Download ZIP via semantic_document_url]
    F --> G[External System: Process & Archive PDF/XML]

    %% Success and error handling
    G -->|Success| H[External System: Set Done]
    G -->|Failure| I[External System: Set Error]

    H -->|POST /export/.../set-done| J(HypoDossier: Doc status -> EXPORT_DONE)
    I -->|POST /export/.../set-error| K(HypoDossier: Doc status -> EXPORT_ERROR)

    %% Poll wait
    J --> L[Wait for next Poll]
    K --> L
    C -->|No more docs| L

    %% Styling Classes for consistency
    classDef ext fill:#ffffff,stroke:#888,stroke-width:1.5px,color:#000
    classDef hypo fill:#e6fffb,stroke:#006064,stroke-width:1.5px,color:#000

    %% Apply styles
    class A,D,F,G,H,I ext
    class B,E,J,K hypo


```

**Steps:**

1.  **Poll for Available Exports:** Periodically (e.g., every 5 minutes), call `GET /export/all-semantic-documents-available`. This returns a list of documents currently in the `EXPORT_AVAILABLE` or `EXPORT_IN_PROGRESS` state. Focus on those in `EXPORT_AVAILABLE`.
2.  **Process Each Document:** For each document listed:
    a.  **Mark as In Progress:** Call `POST /export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-export-in-progress` to prevent other systems or polls from picking up the same document.
    b.  **Download:** Use the `semantic_document_url` provided in the response from the polling endpoint (Step 1) to download the ZIP archive. This archive contains the document PDF and its corresponding `ClientisExportMetadata` XML file.
    c.  **Archive:** Process the ZIP file, extract the PDF and XML, and archive them in the target system (e.g., eDossier) using the metadata from the XML.
    d.  **Confirm Outcome:**
        *   **Success:** If archiving was successful, call `POST /export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-done`.
        *   **Failure:** If archiving failed, call `POST /export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-error`. You can optionally include an error message in the request body (`{"error_message": "Reason for failure"}`).
3.  **Repeat:** Continue processing documents from the poll list. Wait for the next polling interval to check for new documents.

#### 2. Dossier Closing Workflow

This workflow ensures a dossier is finalized and made read-only after all processing and exporting are complete.

```mermaid
graph TD
    A[Dossier: OPEN] -- POST /dossier/.../prepare-close-dossier --> B(Dossier: CLOSING);
    B -- Check Conditions (Internal) --> C{Ready to Close?};
    C -- Yes --> D[POST /dossier/.../close-dossier];
    D --> E(Dossier: CLOSED);
    C -- No --> F[Wait/Resolve Issues];
    F -- Conditions Met --> C;

    %% Optional Check
    G[External System: Check Readiness] -- GET /dossier/.../check-dossier-close-ready --> H{Ready?};
    H -- Yes --> A;
    H -- No --> F;

    style A fill:#ADD8E6,stroke:#333,stroke-width:2px;
    style B fill:#FFFFE0,stroke:#333,stroke-width:2px;
    style E fill:#808080,stroke:#333,stroke-width:2px;

```

**Steps:**

1.  **Check Readiness (Optional but Recommended):** Before attempting to close, call `GET /dossier/{external_dossier_id}/check-dossier-close-ready`. This endpoint returns the status of document exports and processing within the dossier and indicates if it's ready (`ready_for_close: true`). If not ready, the response includes messages explaining what needs to be done.
2.  **Prepare for Closing:** Call `POST /dossier/{external_dossier_id}/prepare-close-dossier`.
    *   This initiates the closing process by transitioning the dossier's status to `CLOSING`.
    *   The dossier becomes read-only.
    *   If any documents are still in `IN_FRONT_OFFICE` state, this call *may* trigger their export process automatically (equivalent to calling `start-semantic-document-export` internally). Check the `exports_triggered_count` in the response.
    *   If `close_dossier_if_possible=True` (default) is used *and* the dossier meets all closing criteria *at that moment*, the API will attempt to transition it directly to `CLOSED`. The result of this attempt will be in the `close_result` field of the response.
3.  **Wait for Exports (If Applicable):** If `prepare-close-dossier` triggered exports or if documents were already exporting, wait until all semantic documents in the dossier have reached a final state (`EXPORT_DONE` or `EXPORT_ERROR`). You can monitor this using the document export polling endpoint (`GET /export/all-semantic-documents-available` or `GET /export/{external_dossier_id}/semantic-documents-available`).
4.  **Finalize Closing:** Once you have confirmed the dossier is ready (e.g., by calling `check-dossier-close-ready` again and getting `ready_for_close: true`), call `POST /dossier/{external_dossier_id}/close-dossier`.
    *   This performs the final checks.
    *   If successful (`success: true`), the dossier state transitions to `CLOSED`.
    *   If unsuccessful (`success: false`), the response includes messages explaining why it failed.

### Semantic Document Export State Machine

This diagram shows the lifecycle of a semantic document concerning the export process. External systems primarily interact when the document is in the `EXPORT_AVAILABLE` state.

```mermaid
stateDiagram-v2
    [*] --> Document_in_Front_Office : Document Created/Uploaded

    Document_in_Front_Office --> Ready_for_Export : User/System marks Ready\n(e.g., POST /start-semantic-document-export)
    Ready_for_Export --> Export_Available : Export process generates file\n(Internal Process)

    Export_Available --> Export_in_Progress : External System calls\nPOST /set-export-in-progress
    Export_Available --> Export_Error : Internal Export Error before pickup

    Export_in_Progress --> Export_Done : External System calls\nPOST /set-done
    Export_in_Progress --> Export_Error : External System calls\nPOST /set-error

    Export_Error --> [*] : Terminal State (Error)
    Export_Done --> [*] : Terminal State (Success)

    state Export_Available {
        [*] --> Available : Ready for pickup
    }
    state Export_in_Progress {
        [*] --> InProgress : Being processed by External System
    }
    state Export_Error {
        [*] --> Error : Export Failed
    }
    state Export_Done {
        [*] --> Done : Export Successful
    }

    note right of Export_Available : External System polls for docs in this state
    note right of Export_in_Progress : External System downloads ZIP during this state

```

### Dossier State Machine

This diagram shows the lifecycle of a dossier, focusing on the closing process managed via the API.

```mermaid
stateDiagram-v2
    [*] --> Open : Dossier Created
    Open --> Closing : POST /prepare-close-dossier\n[is_system=True]
    Closing --> Closed : POST /close-dossier\n[is_system=True & dossier_close_is_possible=True]
    Closing --> Open : Close conditions not met / Reverted (Manual?)
    Closed --> [*] : Terminal State

    note right of Closing : Dossier is Read-Only
    note right of Closed : Dossier is Archived/Finalized

```

### API Endpoint Details

#### Document Export Endpoints

*   **`GET /export/all-semantic-documents-available`**
    *   **Description:** Poll this endpoint to find documents ready for export (`EXPORT_AVAILABLE`) or currently being processed (`EXPORT_IN_PROGRESS`) across all accessible dossiers for the account.
    *   **Response:** `200 OK` - List of `ExportStatusSemanticDocument` objects.
*   **`GET /export/{external_dossier_id}/semantic-documents-available`**
    *   **Description:** Get the export status for documents within a *specific* dossier that are either `EXPORT_AVAILABLE` or `EXPORT_IN_PROGRESS`.
    *   **Parameters:**
        *   `external_dossier_id` (path): The specific dossier ID.
    *   **Response:** `200 OK` - List of `ExportStatusSemanticDocument` objects.
*   **`POST /export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-export-in-progress`**
    *   **Description:** Call this *before* downloading. Marks a specific semantic document's export status as `EXPORT_IN_PROGRESS`.
    *   **Parameters:**
        *   `external_dossier_id` (path): Dossier ID.
        *   `semantic_document_uuid` (path): UUID of the specific semantic document.
    *   **Response:** `200 OK` - List containing the UUID of the affected `SemanticDocumentExport` record.
*   **`POST /export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-done`**
    *   **Description:** Call this *after* successful download and archiving. Marks the export as successfully completed (`EXPORT_DONE`).
    *   **Parameters:**
        *   `external_dossier_id` (path): Dossier ID.
        *   `semantic_document_uuid` (path): UUID of the specific semantic document.
    *   **Response:** `200 OK` - List containing the UUID of the affected `SemanticDocumentExport` record.
*   **`POST /export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-error`**
    *   **Description:** Call this if download or archiving fails. Marks the export as failed (`EXPORT_ERROR`).
    *   **Parameters:**
        *   `external_dossier_id` (path): Dossier ID.
        *   `semantic_document_uuid` (path): UUID of the specific semantic document.
    *   **Request Body (Optional):** `ErrorMessageSchema` (e.g., `{"error_message": "Failed to archive in eDossier: Connection timeout"}`)
    *   **Response:** `200 OK` - List containing the UUID of the affected `SemanticDocumentExport` record(s).

#### Dossier Closing Endpoints

*   **`GET /dossier/{external_dossier_id}/check-dossier-close-ready`**
    *   **Description:** Checks if a specific dossier meets the criteria for closing (e.g., all documents exported, no files processing).
    *   **Parameters:**
        *   `external_dossier_id` (path): The dossier ID.
    *   **Response:** `200 OK` - `DossierCloseReadyResponse` object indicating readiness and providing instructions if not ready.
*   **`POST /dossier/{external_dossier_id}/prepare-close-dossier`**
    *   **Description:** Initiates the closing process. Sets the dossier state to `CLOSING` (read-only) and may trigger outstanding exports. Can attempt full close if `close_dossier_if_possible=True` and conditions are met.
    *   **Parameters:**
        *   `external_dossier_id` (path): The dossier ID.
        *   `close_dossier_if_possible` (query, optional, default: `True`): If true, attempts to fully close the dossier immediately if possible.
    *   **Response:**
        *   `200 OK` - `PrepareCloseDossierResponse` object indicating success, number of exports triggered, and potential close result.
        *   `400 Bad Request` / `404 Not Found` - Errors (e.g., dossier already closed, invalid state).
*   **`POST /dossier/{external_dossier_id}/close-dossier`**
    *   **Description:** Attempts the final step of closing the dossier, transitioning it to the `CLOSED` state. Only works if the dossier is in `CLOSING` state and all conditions are met.
    *   **Parameters:**
        *   `external_dossier_id` (path): The dossier ID.
    *   **Response:**
        *   `200 OK` - `DossierCloseResponse` object indicating success or failure with reasons.
        *   `400 Bad Request` - If the dossier cannot be closed (e.g., not in `CLOSING` state, conditions not met).

#### Status & Helper Endpoints

*   **`GET /dossier/{external_dossier_id}/status`**
    *   **Description:** Retrieves the current work status (`OPEN`, `CLOSING`, `CLOSED`) of a specific dossier.
    *   **Parameters:**
        *   `external_dossier_id` (path): The dossier ID.
    *   **Response:**
        *   `200 OK` - `DossierStatusResponse` with the `status_key`.
        *   `404 Not Found` - If the dossier doesn't exist or has no status set.
*   **`GET /dossier/{external_dossier_id}/semantic-documents`**
    *   **Description:** Lists semantic documents within a specific dossier.
    *   **Parameters:**
        *   `external_dossier_id` (path): The dossier ID.
        *   `show_pages` (query, optional, default: `False`): Include page details in the response.
    *   **Response:** `200 OK` - List of `SemanticDocument` objects.
*   **`GET /ping`**
    *   **Description:** Simple endpoint to check API availability and authentication.
    *   **Response:** `200 OK` - `{"detail": "pong"}` (or similar message).

### Data Structures

#### `ExportStatusSemanticDocument` (Polling Response Item)

```json
{
  "semantic_document_export_request_uuid": "uuid", // UUID of the export task
  "semantic_document_uuid": "uuid", // UUID of the document itself
  "semantic_document_url": "string (url)", // URL to download the ZIP archive
  "external_dossier_id": "string", // e.g., 949.401678.001.110794
  "document_category_key": "string", // Internal category key, e.g., PASSPORT_CH
  "document_category_id_external": "string | null", // External system ID for the category
  "updated_at": "datetime (isoformat)",
  "semantic_document_work_status": "string (Enum: EXPORT_AVAILABLE, EXPORT_IN_PROGRESS, ...)" // Current state
}
```

#### `ClientisExportMetadata` (XML content within the ZIP)

This XML file is included alongside the PDF in the downloaded ZIP archive. Its structure is defined by the `ClientisExportMetadata` schema and should match the example provided.

```xml
<?xml version='1.0' encoding='UTF-8'?>
<ClientisExport>
  <Mandant>string</Mandant> <!-- Bank ID -->
  <Kundennummer>string</Kundennummer> <!-- Client ID -->
  <Dokumententyp>string</Dokumententyp> <!-- Mapped external document category ID -->
  <DokumentenID>string</DokumentenID> <!-- # Should be empty, will be set by Swisscom during their import into eDossier -->
  <Dokumentenname>string</Dokumentenname> <!-- Filename of the PDF -->
  <DokumentenRECID>string</DokumentenRECID> <!-- Optional RECID -->
  <Dokumentendatum>date (YYYY-MM-DD)</Dokumentendatum> <!-- Document creation date -->
  <Kontonummer>string</Kontonummer> <!-- Optional account number -->
  <Nummernkontocode>string</Nummernkontocode> <!-- Default '0' -->
  <Personalcode>string</Personalcode> <!-- Default '0' -->
  <Portfolionummer>string</Portfolionummer> <!-- Optional portfolio number -->
  <Rahmennummer>string</Rahmennummer> <!-- Frame contract ID -->
  <Scandatum>string (YYYY-MM-DD HH:MM)</Scandatum> <!-- Upload/Scan timestamp -->
  <ClientAdvisorId>string</ClientAdvisorId> <!-- Optional advisor ID -->
  <CreatorId>string</CreatorId> <!-- User ID who initiated/uploaded -->
  <Dokumentenbezeichnung>string</Dokumentenbezeichnung> <!-- Title of the document -->
  <Kommentar>string</Kommentar> <!-- Optional comment -->
  <DirectArchive>boolean (True/False)</DirectArchive> <!-- Default False -->
  <SourceSystem>string</SourceSystem> <!-- Default 'HypoDossier' -->
</ClientisExport>
```

#### `DossierCloseReadyResponse`

```json
{
  "num_documents_all": "integer",
  "num_documents_exported": "integer", // Count in EXPORT_DONE state
  "num_documents_export_not_started": "integer", // Count in IN_FRONT_OFFICE, READY_FOR_EXPORT
  "num_documents_in_export": "integer", // Count in EXPORT_AVAILABLE, EXPORT_IN_PROGRESS
  "num_documents_unknown": "integer", // Count classified as 'unknown' category
  "num_original_files_in_processing": "integer", // Count of uploads still processing
  "ready_for_close": "boolean", // True if dossier can be closed now
  "msg_nok_de": "string | null", // Instructions if not ready (German)
  "msg_nok_en": "string | null", // Instructions if not ready (English)
  "msg_nok_fr": "string | null", // Instructions if not ready (French)
  "msg_nok_it": "string | null"  // Instructions if not ready (Italian)
}
```

#### `DossierCloseResponse`

```json
{
  "success": "boolean", // True if closing was successful
  "msg_nok_de": "string | null", // Reason if not successful (German)
  "msg_nok_en": "string | null", // Reason if not successful (English)
  "msg_nok_fr": "string | null", // Reason if not successful (French)
  "msg_nok_it": "string | null"  // Reason if not successful (Italian)
}
```

### Example Usage

Remember to replace placeholders like `YOUR_API_BASE_URL`, `YOUR_BEARER_TOKEN`, and example dossier IDs with actual values. Add robust error handling and logging suitable for your production environment.

Okay, here are `curl` command examples for the key API interactions described in the documentation.

**Remember to replace:**

*   `YOUR_API_BASE_URL` with the actual base URL (e.g., `https://hypodossier.example.com/partner/clientis/api/v1`)
*   `YOUR_BEARER_TOKEN` with your valid JWT token.
*   `YOUR_EXTERNAL_DOSSIER_ID` with a specific dossier ID (e.g., `949.401678.001.110794`).
*   `YOUR_SEMANTIC_DOCUMENT_UUID` with the UUID of a specific semantic document.

**Common Headers:** All requests require the Authorization header. POST requests with a JSON body also need the Content-Type header.

```bash
# Define variables for convenience (optional, but recommended)
export API_BASE_URL="YOUR_API_BASE_URL"
export TOKEN="YOUR_BEARER_TOKEN"
export DOSSIER_ID="YOUR_EXTERNAL_DOSSIER_ID"
export DOC_UUID="YOUR_SEMANTIC_DOCUMENT_UUID"
```

---

### 1. Ping (Check API Availability & Auth)

```bash
curl -X GET \
  -H "Authorization: Bearer $TOKEN" \
  "$API_BASE_URL/ping"
```

---

### 2. Poll for Available Document Exports

```bash
curl -X GET \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json" \
  "$API_BASE_URL/export/all-semantic-documents-available"

# Alternative: For a specific dossier
# curl -X GET \
#   -H "Authorization: Bearer $TOKEN" \
#   -H "Accept: application/json" \
#   "$API_BASE_URL/export/$DOSSIER_ID/semantic-documents-available"
```
*   **Parse the JSON response** from this call to find documents with `"semantic_document_work_status": "EXPORT_AVAILABLE"`. Extract `semantic_document_uuid`, `external_dossier_id`, and `semantic_document_url` for the next steps.

---

### 3. Set Export Status to In Progress

*Call this **before** downloading the document.*

```bash
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  "$API_BASE_URL/export/$DOSSIER_ID/semantic-documents/$DOC_UUID/set-export-in-progress"
```

---

### 4. Download the Document ZIP

*Use the `semantic_document_url` obtained from the polling step (Step 2). This URL might be different from the API base URL.*

```bash
# Assume DOWNLOAD_URL variable holds the URL from the polling response
# Example: export DOWNLOAD_URL="https://some-storage-url/path/to/doc.zip?sig=..."
export DOWNLOAD_URL="THE_SEMANTIC_DOCUMENT_URL_FROM_POLLING" # Replace this

curl -L \
  -H "Authorization: Bearer $TOKEN" \
  -o "$DOC_UUID.zip" \
  "$DOWNLOAD_URL"
```
*   `-L` follows redirects, which might be necessary depending on the storage mechanism.
*   `-o "$DOC_UUID.zip"` saves the downloaded content to a file named after the document UUID.

---

### 5. Set Export Status to Done (After Successful Archiving)

```bash
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  "$API_BASE_URL/export/$DOSSIER_ID/semantic-documents/$DOC_UUID/set-done"
```

---

### 6. Set Export Status to Error (If Archiving Fails)

```bash
# Option 1: Without an error message
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  "$API_BASE_URL/export/$DOSSIER_ID/semantic-documents/$DOC_UUID/set-error"

# Option 2: With an error message
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"error_message": "Failed to archive in target system due to timeout."}' \
  "$API_BASE_URL/export/$DOSSIER_ID/semantic-documents/$DOC_UUID/set-error"
```

---

### 7. Check Dossier Readiness for Closing

```bash
curl -X GET \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json" \
  "$API_BASE_URL/dossier/$DOSSIER_ID/check-dossier-close-ready"
```
*   Check the `ready_for_close` field in the JSON response.

---

### 8. Prepare Dossier for Closing

```bash
# Option 1: Default behavior (attempt close if possible immediately)
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  "$API_BASE_URL/dossier/$DOSSIER_ID/prepare-close-dossier"

# Option 2: Only prepare, do not attempt immediate close
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  "$API_BASE_URL/dossier/$DOSSIER_ID/prepare-close-dossier?close_dossier_if_possible=false"
```

---

### 9. Finalize Dossier Closing

*Only call this if the dossier is in the `CLOSING` state and `check-dossier-close-ready` returns `ready_for_close: true`.*

```bash
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  "$API_BASE_URL/dossier/$DOSSIER_ID/close-dossier"
```

---

### 10. Get Dossier Status

```bash
curl -X GET \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json" \
  "$API_BASE_URL/dossier/$DOSSIER_ID/status"
```

These examples cover the main interactions. You would typically script these calls, parse the JSON responses (e.g., using tools like `jq`), and implement the logic for downloading, processing ZIP files, and handling potential errors.