from datetime import datetime, date
from enum import Enum
from typing import List, Optional, Dict
from uuid import UUID

from events.schemas import EventDetail
from pydantic import StringConstraints, BaseModel, ConfigDict

from doccheck.schemas import DocumentOption
from dossier.schemas import Page<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON>
from dossier import schemas as dossier_schemas
from typing_extensions import Annotated

from projectconfig.settings import MAX_LENGTH_SEMANTIC_DOCUMENT_TITLE_SUFFIX


class SavingResultWithMessage(BaseModel):
    message: str


class SavingResultWithMessageAndUUID(SavingResultWithMessage):
    uuid: UUID
    title: str


class CreatedSemanticDocument(BaseModel):
    uuid: UUID  # UUID of newly created semantic document


class UpdateSemanticTitleSchema(BaseModel):
    uuid: UUID  # UUID of SemanticDocument
    suffix: Optional[
        Annotated[
            str, StringConstraints(max_length=MAX_LENGTH_SEMANTIC_DOCUMENT_TITLE_SUFFIX)
        ]
    ] = None
    id: str  # id of DocumentCategory
    name: str  # name of DocumentCategory

    # 'document date' of semantic document. Set this as custom
    # 'document_date' if not None and if different from created_at of related original file.
    semantic_document_date: Optional[date] = None


class SemanticDocumentDateSchema(BaseModel):
    # Client really wants date with a Z timezone, hence casting to UTC
    # https://gitlab.com/hypodossier/document-universe/-/issues/679
    semantic_document_date: datetime


class CategoryGet(BaseModel):
    id: str
    name: str


class UndeleteSemanticDocumentSchema(BaseModel):
    semantic_document_uuid: UUID


class MoveSemanticPageSchema(BaseModel):
    uuid: str  # UUID of
    source_file_uuid: str  # UUID of ProcessedFile
    number: int  # page number of semantic page

    # List of page objects that are on the processed page with uuid
    page_objects: List[Optional[PageObjectFullApiData]] = []


class CreatedSemanticDocumentBody(BaseModel):
    suffix: Optional[str] = None
    document_category_id: str
    document_category_name: str


class MoveSemanticPagesToNewDocumentSchema(CreatedSemanticDocumentBody):
    semantic_pages: List[MoveSemanticPageSchema]
    real_estate_property_key: Optional[str] = None


class MoveSemanticPagesToNewDocumentResponseSchema(BaseModel):
    semantic_pages_number: List[int] = None
    semantic_document_uuid: str
    real_estate_property_key: Optional[str] = None


class CategoryRecommendationsRequestBody(BaseModel):
    semantic_pages_uuid: List[str] = None


class CombinePagesRequestBody(BaseModel):
    applicable_pages: List[MoveSemanticPageSchema]
    document_category_id: str
    document_category_name: str


class CombinePagesRealEstateRequestBody(BaseModel):
    document_category_key: str
    photos_sorting: Optional[bool] = False


class assignCollateralRequestBody(BaseModel):
    semantic_document_uuid: str
    collateral_number: Optional[str] = None


class assignAutoCollateralRequestBody(BaseModel):
    document: List[assignCollateralRequestBody]


class SemanticDocumentMoved(BaseModel):
    uuid: UUID
    document_category_name: str
    title: str


class SemanticPageMovedDetail(BaseModel):
    semantic_page_uuid: UUID
    processed_page_uuid: UUID
    semantic_document: SemanticDocumentMoved
    page_number: int


class SemanticPageMoved(BaseModel):
    source: SemanticPageMovedDetail
    destination: SemanticPageMovedDetail


class SemanticDocumentRenamedEvent(EventDetail):
    source: SemanticDocumentMoved
    destination: SemanticDocumentMoved


class SemanticPagesMovedEvent(EventDetail):
    moves: List[SemanticPageMoved]


class FulfillmentType(str, Enum):
    # This assignment tries to fulfill the requirements by assigning 1..n docs.
    # This is successful if at least 1 doc is assigned
    DOC_ASSIGNMENT = "doc_assignment"
    # This assignment tries to fulfill the requirements by adding a comment
    BYPASSING = "bypassing"


class DocCheckAssignmentCreate(BaseModel):
    context_model_uuid: UUID
    completeness_rule_key: str
    fulfillment_type: FulfillmentType
    assigned_document_uuids: Optional[List[UUID]] = []
    comment: Optional[str] = None


class CreatedDocCheckAssignment(BaseModel):
    uuid: UUID


class RequirementStatus(str, Enum):
    NOT_FULFILLED = "not fulfilled"
    NOT_FULFILLED_OPTIONAL = "not fulfilled, optional"
    FULFILLED_COMMENT = "fulfilled, comment"
    FULFILLED_ASSIGNMENT = "fulfilled, assignment"


class DocCheckAssignmentOut(BaseModel):
    case_uuid: UUID
    context_model_uuid: UUID
    rule_key: str
    rule_title_de: Optional[str] = None
    rule_title_en: Optional[str] = None
    rule_title_fr: Optional[str] = None
    rule_title_it: Optional[str] = None
    rule_desc_de: Optional[str] = None
    rule_desc_en: Optional[str] = None
    rule_desc_fr: Optional[str] = None
    rule_desc_it: Optional[str] = None
    rule_strictness: Optional[str] = None
    rule_order: int
    document_options: List[DocumentOption]

    assigned_document_uuids: Optional[List[UUID]] = []
    assigned_document_titles: Optional[List[str]] = []
    fulfillment_type: FulfillmentType
    comment: Optional[str] = None
    requirement_status: RequirementStatus


class RealEstatePropertyEntity(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)

    key: Annotated[str, StringConstraints(max_length=255)]
    title: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    floor: Optional[int] = None
    street: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    street_nr: Optional[Annotated[str, StringConstraints(max_length=10)]] = None
    zipcode: Optional[Annotated[str, StringConstraints(max_length=60)]] = None
    city: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    updated_at: datetime


class RealEstatePropertyAssignment(RealEstatePropertyEntity):
    semantic_document_uuid: UUID


class RealEstatePropertyKey(BaseModel):
    real_estate_property_key: str


class EntityTypes(str, Enum):
    # PG: we could import the classes and drive the enum from class.__name__
    # but I'm going to hardcode it as I'm worried about circular imports
    # we have similar behaviour in bekb apps
    REAL_ESTATE_PROPERTY = "RealEstatePropertyEntity"


class SemanticDocumentEntityMappings(BaseModel):
    type_mappings: Optional[Dict[str, List[EntityTypes]]] = {}


class RealEstatePropertyAssignmentCombinePages(BaseModel):
    real_estate_property_key: str
    semantic_document_uuid: UUID
    semantic_pages_uuid: Optional[List[UUID]] = []


class PermittedCombinePagesCombinations(BaseModel):
    combinations: Optional[
        Dict[str, List[RealEstatePropertyAssignmentCombinePages]]
    ] = {}


# Category of combinations that have no real estate property assigned.
# Only used as key in PermittedCombinePagesCombinations and in
# RealEstatePropertyAssignmentCombinePages
COMBINE_PAGES_UNASSIGNED: str = "unassigned"


class SemanticDocumentPDFRequestV2(BaseModel):
    # Same as SemanticDocumentPDFRequestV1 but without put_upload_url
    # as we are using a temporary minio resource
    semantic_document_pdf_request_uuid: UUID
    # Needed to fetch the correct semantic document from within the semantic dossier
    semantic_document_uuid: UUID
    semantic_dossier: dossier_schemas.SemanticDossier

    add_metadata: Optional[bool] = True
    # From account.document_download_ui_add_uuid_suffix
    # Added a UUID suffix to the pdf file name
    add_uuid_suffix: Optional[bool] = False


class SemanticDocumentPDFResponseV1(BaseModel):
    semantic_document_pdf_request_uuid: UUID


class Confidence(str, Enum):
    CERTAIN = "certain"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class SemanticDocumentWorkStatusChangedEvent(EventDetail):
    semantic_document_uuid: UUID
    username: str
    from_state_key: Optional[str] = None
    to_state_key: Optional[str] = None


class SemanticPage(BaseModel):
    uuid: UUID
    semantic_document_uuid: UUID
    deleted: bool
    page_number: int
    image_url: str
    rotation_angle: int
    document_category: str
    page_category: str
    created_at: datetime
    updated_at: datetime


class SemanticDocumentDate(BaseModel):
    document_date: datetime
