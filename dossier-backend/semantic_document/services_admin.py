import structlog
from django.contrib import messages
from django.db.models import QuerySet

from clientis.schemas.schemas import ClientisExternalId
from dossier.models import SemanticDocumentExportStrategy
from semantic_document.models import SemanticDocument
from semantic_document.services import (
    set_semantic_documents_ready_for_export,
    reset_semantic_document_export,
    force_semantic_document_done,
)

logger = structlog.get_logger(__name__)


def admin_reset_semantic_document_export(semdocs: QuerySet[SemanticDocument], request):
    semdocs_changed, semdocs_unchanged, initial_work_status = (
        reset_semantic_document_export(semdocs)
    )

    logger.info(
        "reset_semantic_document_export",
        num_semdocs_changed=len(semdocs_changed),
        num_semdocs_unchanged=len(semdocs_unchanged),
        initial_work_status=initial_work_status,
    )

    messages.info(
        request,
        f"reset_semantic_document_export num_semdocs_changed={len(semdocs_changed)}, num_semdocs_unchanged={len(semdocs_unchanged)}, initial_work_status={initial_work_status}",
    )

    for semdoc in semdocs_changed:
        messages.info(
            request,
            f"Reset of export done for sem doc {semdoc.dossier.account.key} {semdoc.uuid} '{semdoc.title}'",
        )


def admin_reset_restart_semantic_document_export(request, queryset):

    for semantic_document in queryset.filter(semantic_pages__isnull=False).all():

        if (
            semantic_document.dossier.account.semantic_document_export_strategy
            == SemanticDocumentExportStrategy.SWISSCOM_EDOSSIER_XML_ZIP
        ):
            external_id = semantic_document.dossier.external_id
            try:
                ClientisExternalId(external_id=external_id)
            except:
                messages.error(
                    request,
                    f"Invalid Dossier external id: {external_id} for semantic document "
                    f"{semantic_document.title} {semantic_document.uuid} ",
                )
                return

    # This also deletes the semdoc export
    admin_reset_semantic_document_export(queryset, request)

    uuids = set_semantic_documents_ready_for_export(queryset)

    logger.info(
        "reset_restart_semantic_document_export",
        semantic_documents_exported_count=len(uuids),
    )

    messages.info(
        request,
        f"Started Semantic Document Exports Count: {len(uuids)}",
    )


def admin_force_semantic_document_export_done(request, queryset):
    (
        semantic_export_set_done,
        new_semantic_export_created,
        semantic_documents_work_state_changed,
    ) = force_semantic_document_done(queryset)
    messages.info(
        request,
        f"Semantic Documents Export Set Done: {semantic_export_set_done}",
    )
    messages.info(
        request,
        f"Semantic Documents Export Created: {new_semantic_export_created}",
    )
    messages.info(
        request,
        f"Semantic Documents Workstate Changed: {semantic_documents_work_state_changed}",
    )
