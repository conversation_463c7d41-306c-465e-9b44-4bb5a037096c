import uuid
from typing import List
from uuid import UUID

from black.trans import defaultdict
from django.shortcuts import get_object_or_404
from django.http import HttpResponseBadRequest
from ninja import Router

from dossier import schemas as dossier_schemas
from dossier import helpers as dossier_helpers
from dossier.helpers_access_check import (
    get_dossier_from_request_with_access_check,
    get_writable_dossier_from_request_with_access_check,
)
from semantic_document.models import (
    SemanticPage,
    SemanticPagePageObject,
    SemanticPageUserAnnotations,
)
from semantic_document.schemas_page_annotations import (
    UserAnnotationGroupUUIDSchema,
    UserAnnotationsSchema,
    SemanticPageUserAnnotationsSchema,
    UpdateUserAnnotationSchema,
)
from semantic_document.services import (
    assert_semantic_document_is_writable,
)

semantic_page_router = Router()


@semantic_page_router.get(
    "/{semantic_page_uuid}/get-user-annotations",
    url_name="semantic-page-user-annotations",
    response=SemanticPageUserAnnotationsSchema,
    description="Get user annotations for a specific semantic page",
)
def get_semantic_page_user_annotations(
    request, semantic_page_uuid: UUID, annotation_type: str = None
):
    """
    Get user annotations for a specific semantic page.
    Annotations of type 'highlight' come in groups (identified by annotation_group_id) as the text selections is made out of many bboxes.
    Annotations of type 'comment' are made up of just one entry.
    """

    semantic_page = get_object_or_404(SemanticPage, uuid=semantic_page_uuid)

    get_dossier_from_request_with_access_check(request, semantic_page.dossier.uuid)

    if annotation_type:
        user_annotations = SemanticPageUserAnnotations.objects.filter(
            semantic_page=semantic_page, annotation_type=annotation_type
        )
    else:
        user_annotations = SemanticPageUserAnnotations.objects.filter(
            semantic_page=semantic_page
        )
    user_annotations_values = user_annotations.values(
        "annotation_type",
        "annotation_group_uuid",
        "text",
        "bbox_left",
        "bbox_top",
        "bbox_width",
        "bbox_height",
        "hexcolor",
    )

    annotations = defaultdict(list)
    for annotation in user_annotations_values:
        annotations[str(annotation["annotation_group_uuid"])].append(
            UserAnnotationsSchema(
                annotation_type=annotation["annotation_type"],
                annotation_group_uuid=annotation["annotation_group_uuid"],
                text=annotation["text"],
                bbox_left=annotation["bbox_left"],
                bbox_top=annotation["bbox_top"],
                bbox_width=annotation["bbox_width"],
                bbox_height=annotation["bbox_height"],
                hexcolor=(
                    annotation["hexcolor"] if annotation["hexcolor"] else "#FFFF00"
                ),
            )
        )

    return SemanticPageUserAnnotationsSchema(
        searchable_pdf_url=semantic_page.processed_page.searchable_pdf.get_fast_url(),
        user_annotations=annotations,
    )


@semantic_page_router.post(
    "/{semantic_page_uuid}/create-user-annotations",
    url_name="semantic-page-user-annotations-create",
    response=UserAnnotationGroupUUIDSchema,
    description="Create user annotations for a specific semantic page",
)
def create_semantic_page_user_annotations(
    request,
    semantic_page_uuid: UUID,
    user_annotations: List[UserAnnotationsSchema],
):
    semantic_page = get_object_or_404(SemanticPage, uuid=semantic_page_uuid)
    assert_semantic_document_is_writable(semantic_page.semantic_document)

    get_writable_dossier_from_request_with_access_check(
        request, semantic_page.dossier.uuid
    )

    annotation_group_uuid = uuid.uuid4()
    annotations = [
        SemanticPageUserAnnotations(
            semantic_page=semantic_page,
            annotation_type=annotation.annotation_type,
            annotation_group_uuid=annotation_group_uuid,
            text=annotation.text,
            bbox_left=annotation.bbox_left,
            bbox_top=annotation.bbox_top,
            bbox_width=annotation.bbox_width,
            bbox_height=annotation.bbox_height,
            hexcolor=annotation.hexcolor,
        )
        for annotation in user_annotations
    ]
    SemanticPageUserAnnotations.objects.bulk_create(annotations)
    return UserAnnotationGroupUUIDSchema(annotation_group_uuid=annotation_group_uuid)


@semantic_page_router.delete(
    "/{semantic_page_uuid}/delete-user-annotations",
    url_name="semantic-page-user-annotations-delete",
    response=UserAnnotationGroupUUIDSchema,
    description="Delete user annotations for a specific semantic page",
)
def delete_semantic_page_user_annotations(
    request, semantic_page_uuid: UUID, annotation_group_uuid: UUID
):
    semantic_page = get_object_or_404(SemanticPage, uuid=semantic_page_uuid)
    assert_semantic_document_is_writable(semantic_page.semantic_document)

    get_writable_dossier_from_request_with_access_check(
        request, semantic_page.dossier.uuid
    )

    SemanticPageUserAnnotations.objects.filter(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    ).delete()

    return UserAnnotationGroupUUIDSchema(annotation_group_uuid=annotation_group_uuid)


@semantic_page_router.get(
    "/{semantic_page_uuid}/all-page-objects",
    response=List[dossier_schemas.PageObjectApiDataWithUUID],
    url_name="semantic-page-all-page-objects",
    description="Get all page objects for a specific semantic page",
)
def get_semantic_page_page_objects(request, semantic_page_uuid: UUID):

    semantic_page = get_object_or_404(SemanticPage, uuid=semantic_page_uuid)

    # Security access check
    get_dossier_from_request_with_access_check(request, semantic_page.dossier.uuid)

    semantic_page_page_objects = (
        SemanticPagePageObject.objects.filter(semantic_page=semantic_page)
        .select_related(
            "page_object",
            "semantic_page",
            "semantic_page__semantic_document",
            "page_object__key",
            "page_object__type",
            "page_object__processed_page",
        )
        .order_by(
            "semantic_page__semantic_document",
            "semantic_page__number",
            "page_object__top",
            "page_object__right",
        )
    )

    return [
        dossier_helpers.prepare_page_object_v2(page_object)
        for page_object in semantic_page_page_objects
    ]


@semantic_page_router.patch(
    "/{semantic_page_uuid}/update-user-annotation",
    url_name="semantic-page-user-annotation-update",
    response=UserAnnotationGroupUUIDSchema,
    description="Update a comment annotation for a specific semantic page. Either update text only, or all bbox fields together.",
)
def update_semantic_page_user_annotation(
    request,
    semantic_page_uuid: UUID,
    annotation_group_uuid: UUID,
    update_data: UpdateUserAnnotationSchema,
):
    semantic_page = get_object_or_404(SemanticPage, uuid=semantic_page_uuid)
    assert_semantic_document_is_writable(semantic_page.semantic_document)

    get_writable_dossier_from_request_with_access_check(
        request, semantic_page.dossier.uuid
    )

    annotation = get_object_or_404(
        SemanticPageUserAnnotations,
        semantic_page=semantic_page,
        annotation_group_uuid=annotation_group_uuid,
        annotation_type="comment",  # Only allow updating comments
    )

    if not update_data.validate_bbox_fields():
        return HttpResponseBadRequest("All bbox fields must be provided together")

    update_fields = []
    if update_data.text is not None:
        annotation.text = update_data.text
        update_fields.append("text")

    if update_data.bbox_left is not None:
        annotation.bbox_left = update_data.bbox_left
        annotation.bbox_top = update_data.bbox_top
        annotation.bbox_width = update_data.bbox_width
        annotation.bbox_height = update_data.bbox_height
        update_fields.extend(["bbox_left", "bbox_top", "bbox_width", "bbox_height"])

    if not update_fields:
        return HttpResponseBadRequest("No fields to update")

    annotation.save(update_fields=update_fields)

    return UserAnnotationGroupUUIDSchema(annotation_group_uuid=annotation_group_uuid)
