from typing import List
from uuid import uuid4

from asgiref.sync import sync_to_async

from dossier.helpers_access_check import get_dossier_from_request_with_access_check
from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.schemas_pss import PageStreamPageV2, PSSRequestV2


@sync_to_async
def prepare_pss_data(request, semantic_document: SemanticDocument):
    get_dossier_from_request_with_access_check(request, semantic_document.dossier.uuid)

    semantic_pages: List[SemanticPage] = semantic_document.semantic_pages.all()
    pss_request = PSSRequestV2(
        request_id=uuid4(),
        pages=[
            PageStreamPageV2(
                id=str(page.uuid),
                src_url=page.processed_page.image.get_fast_url(),
                number=page.number,
            )
            for page in semantic_pages
        ],
    )
    return pss_request
