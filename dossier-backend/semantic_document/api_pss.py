from typing import List
from uuid import UUID

from asgiref.sync import sync_to_async
from django.core.exceptions import ValidationError
from django.shortcuts import get_object_or_404
from ninja import Router

from dossier.helpers_access_check import (
    get_dossier_from_request_with_access_check,
    get_writable_dossier_from_request_with_access_check,
)
from hyrabbit.apps import broker
from projectconfig.authentication import async_auth
from semantic_document.helpers_pss import prepare_pss_data
from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.schemas_pss import (
    PageStreamCategorySuggestions,
    PageStreamsCategorySuggestionsRequest,
    DocumentSplitResponse,
    DocumentSplitRequest,
    PSSResponseV1,
)
from semantic_document.services import (
    save_page_streams_to_new_semantic_documents,
    assert_semantic_document_is_writable,
)

pss_router = Router()


@pss_router.post(
    "/page-streams-document-categories-suggestions",
    response=List[PageStreamCategorySuggestions],
    url_name="get-page-streams-categories-suggestions",
    description="Get document category suggestions for page streams",
)
def get_page_streams_categories_suggestions(
    request, body: PageStreamsCategorySuggestionsRequest
):
    semantic_document = get_object_or_404(
        SemanticDocument, uuid=body.semantic_document_uuid
    )
    dossier = get_dossier_from_request_with_access_check(
        request, semantic_document.dossier.uuid
    )

    page_streams_suggestions_response: List[PageStreamCategorySuggestions] = []
    for page_stream in body.page_streams:
        semantic_pages_uuids = [page.uuid for page in page_stream.pages]
        semantic_pages = SemanticPage.objects.filter(
            uuid__in=semantic_pages_uuids,
            dossier=dossier,
            semantic_document=semantic_document,
        )
        categories_suggestions = semantic_document.get_document_type_recommendations(
            semantic_pages, semantic_document.document_category, False, 5
        )
        categories_suggestions_names = [
            suggestion.name for suggestion in categories_suggestions
        ]
        page_streams_suggestions_response.append(
            PageStreamCategorySuggestions(
                stream_index=page_stream.index,
                suggested_document_categories=categories_suggestions_names,
            )
        )
    return page_streams_suggestions_response


@pss_router.post(
    "/{semantic_document_uuid}/split",
    response={200: None, 400: DocumentSplitResponse},
    url_name="split-semantic-document",
    description="Split a semantic document into multiple new semantic documents",
)
def save_page_streams_to_new_semantic_documents_view(
    request, semantic_document_uuid: UUID, body: DocumentSplitRequest
):
    semantic_document = get_object_or_404(
        SemanticDocument, uuid=semantic_document_uuid, deleted_at=None
    )
    assert_semantic_document_is_writable(semantic_document)

    dossier = get_writable_dossier_from_request_with_access_check(
        request, semantic_document.dossier.uuid
    )

    try:
        save_page_streams_to_new_semantic_documents(
            semantic_document, dossier, body.page_streams
        )
    except ValidationError as e:
        return 400, {"detail": e.message}


@pss_router.post(
    "/{semantic_document_uuid}/split-suggestions",
    response=PSSResponseV1,
    url_name="suggest-semantic-document-splits",
    description="Suggestions for splitting a semantic document into multiple new semantic documents",
    auth=async_auth,
)
async def get_split_suggestions(request, semantic_document_uuid: UUID):
    semantic_document = await sync_to_async(get_object_or_404)(
        SemanticDocument, uuid=semantic_document_uuid, deleted_at=None
    )
    pss_request = await prepare_pss_data(request, semantic_document)
    response = await broker.rpc(
        "hypss.predictor.PSSRequestV2", pss_request, PSSResponseV1
    )
    if (
        response.page_stream_prediction
        and len(response.page_stream_prediction.cut_decisions) > 0
    ):
        response.page_stream_prediction.cut_decisions[0].decision = 0
    return response
