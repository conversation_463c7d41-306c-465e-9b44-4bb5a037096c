from typing import Type

import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.utils import timezone

from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import Account, Dossier
from dossier.services import create_dossier
from semantic_document.helpers import (
    get_semantic_documents_with_incorrect_page_numbering,
    fix_document_page_numbering,
)
from semantic_document.models import SemanticDocument

User: Type[AbstractUser] = get_user_model()


@pytest.fixture
def test_dossier(db):
    Dossier.objects.all().delete()
    account = Account.objects.get(key="default")
    user = User.objects.get(username="<EMAIL>")
    dossier = create_dossier(
        account,
        "dossier with case",
        "de",
        user,
    )
    dossier.save()
    return dossier, account


def test_correctly_numbered_pages_returns_empty_lists(test_dossier):
    dossier, account = test_dossier

    semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=2,
        min_num_pages=2,
    )[0]

    # Verify initial state
    assert semantic_document.semantic_pages.all().order_by("number")[0].number == 0
    assert semantic_document.semantic_pages.all().order_by("number")[1].number == 1

    results = get_semantic_documents_with_incorrect_page_numbering(account)
    assert len(results["start_at_one"]) == 0
    assert len(results["start_at_two_or_more"]) == 0
    assert len(results["non_consecutive"]) == 0


def test_non_zero_start_detected(test_dossier):
    dossier, account = test_dossier

    semantic_document = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=2,
        min_num_pages=2,
    )[0]

    # Modify both pages to start at 1 instead of 0
    pages = semantic_document.semantic_pages.all().order_by("number")
    page_0 = pages[0]
    page_0.number = 1
    page_0.save()
    page_1 = pages[1]
    page_1.number = 2
    page_1.save()

    results = get_semantic_documents_with_incorrect_page_numbering(account)
    assert len(results["start_at_one"]) == 1
    assert len(results["non_consecutive"]) == 0
    assert len(results["start_at_two_or_more"]) == 0
    assert results["start_at_one"][0].uuid == semantic_document.uuid


def test_empty_document_not_detected(test_dossier):
    dossier, account = test_dossier

    add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=True,
        max_pages=0,
        min_num_pages=0,
    )

    results = get_semantic_documents_with_incorrect_page_numbering(account)
    assert len(results["start_at_one"]) == 0
    assert len(results["start_at_two_or_more"]) == 0
    assert len(results["non_consecutive"]) == 0


def test_non_consecutive_page_numbers_detected(test_dossier):
    dossier, account = test_dossier

    consecutive_doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=2,
        min_num_pages=2,
    )[0]

    # Modify the second page to have number 2 (skipping 1)
    second_page = consecutive_doc.semantic_pages.all().order_by("number")[1]
    second_page.number = 2
    second_page.save()

    results = get_semantic_documents_with_incorrect_page_numbering(account)
    assert len(results["start_at_one"]) == 0
    assert len(results["non_consecutive"]) == 1
    assert len(results["start_at_two_or_more"]) == 0
    assert results["non_consecutive"][0].uuid == consecutive_doc.uuid


def test_multiple_problematic_documents_detected(test_dossier):
    dossier, account = test_dossier

    # Create document with non-zero start
    doc1 = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=2,
        min_num_pages=2,
    )[0]
    pages = doc1.semantic_pages.all().order_by("number")
    page_0 = pages[0]
    page_0.number = 1
    page_0.save()
    page_1 = pages[1]
    page_1.number = 2
    page_1.save()

    # Create document with non-consecutive pages
    doc2 = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=2,
        min_num_pages=2,
    )[0]
    second_page = doc2.semantic_pages.all().order_by("number")[1]
    second_page.number = 2
    second_page.save()

    results = get_semantic_documents_with_incorrect_page_numbering(account)
    assert len(results["start_at_one"]) == 1
    assert len(results["non_consecutive"]) == 1
    assert len(results["start_at_two_or_more"]) == 0
    assert results["start_at_one"][0].uuid == doc1.uuid
    assert results["non_consecutive"][0].uuid == doc2.uuid


def test_fix_document_page_numbering_sequential(test_dossier):
    dossier, account = test_dossier

    # Create document with pages starting at 1
    doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=3,
        min_num_pages=3,
    )[0]

    # Set pages to 1, 2, 3
    pages = doc.semantic_pages.all().order_by("number")
    for i, page in enumerate(pages, start=1):
        page.number = i
        page.save()

    # Fix numbering
    assert fix_document_page_numbering(doc) is True

    # Verify pages are now 0, 1, 2
    fixed_pages = doc.semantic_pages.order_by("number")
    assert [p.number for p in fixed_pages] == [0, 1, 2]


def test_fix_document_page_numbering_non_sequential(test_dossier):
    dossier, account = test_dossier

    # Create document with non-sequential pages
    doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=3,
        min_num_pages=3,
    )[0]

    # Set pages to 1, 5, 8
    pages = doc.semantic_pages.all().order_by("number")
    page_numbers = [1, 5, 8]
    for page, number in zip(pages, page_numbers):
        page.number = number
        page.save()

    # Should fix non-sequential pages
    assert fix_document_page_numbering(doc) is True

    # Verify pages are now 0, 1, 2
    fixed_pages = doc.semantic_pages.order_by("number")
    assert [p.number for p in fixed_pages] == [0, 1, 2]


def test_fix_document_page_numbering_non_sequential_disabled(test_dossier):
    dossier, account = test_dossier

    # Create document with non-sequential pages
    doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=3,
        min_num_pages=3,
    )[0]

    # Set pages to 1, 5, 8
    pages = doc.semantic_pages.all().order_by("number")
    page_numbers = [1, 5, 8]
    for page, number in zip(pages, page_numbers):
        page.number = number
        page.save()

    # Should not fix when fix_non_sequential_pages is False
    assert fix_document_page_numbering(doc, fix_non_sequential_pages=False) is False

    # Verify pages remained unchanged
    unchanged_pages = doc.semantic_pages.order_by("number")
    assert [p.number for p in unchanged_pages] == [1, 5, 8]


def test_fix_document_page_numbering_duplicates(test_dossier):
    dossier, account = test_dossier

    # Create document with duplicate page numbers
    doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=4,
        min_num_pages=4,
    )[0]

    # Set pages to 1, 2, 2, 2 (multiple duplicates)
    pages = list(doc.semantic_pages.all().order_by("updated_at"))
    page_numbers = [1, 2, 2, 2]

    # Set different updated_at times to ensure consistent ordering
    base_time = timezone.now()
    for i, (page, number) in enumerate(zip(pages, page_numbers)):
        page.number = number
        page.updated_at = base_time + timezone.timedelta(minutes=i)
        page.save()

    # Should fix documents with duplicate page numbers
    assert fix_document_page_numbering(doc) is True

    # Verify pages are now 0, 1, 2, 3 and maintained relative order
    fixed_pages = doc.semantic_pages.order_by("number")
    assert [p.number for p in fixed_pages] == [0, 1, 2, 3]


def test_fix_document_page_numbering_empty(test_dossier):
    dossier, account = test_dossier

    # Create empty document
    doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=True,
        max_pages=0,
        min_num_pages=0,
    )[0]

    assert fix_document_page_numbering(doc) is False


def test_fix_document_page_numbering_already_zero_based(test_dossier):
    dossier, account = test_dossier

    # Create document with pages already starting at 0
    doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=3,
        min_num_pages=3,
    )[0]

    # Set pages to 0, 1, 2
    pages = doc.semantic_pages.all().order_by("number")
    for i, page in enumerate(pages):
        page.number = i
        page.save()

    # Should return False as no fix needed
    assert fix_document_page_numbering(doc) is False

    # Verify pages remained unchanged
    unchanged_pages = doc.semantic_pages.order_by("number")
    assert [p.number for p in unchanged_pages] == [0, 1, 2]


def test_fix_document_page_numbering_duplicates_with_gaps(test_dossier):
    dossier, account = test_dossier

    # Create document with duplicate numbers and gaps
    doc = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=5,
        min_num_pages=5,
    )[0]

    # Set pages to 1, 3, 3, 7, 7 (duplicates and gaps)
    pages = list(doc.semantic_pages.all().order_by("updated_at"))
    page_numbers = [1, 3, 3, 7, 7]

    # Set different updated_at times to ensure consistent ordering
    base_time = timezone.now()
    for i, (page, number) in enumerate(zip(pages, page_numbers)):
        page.number = number
        page.updated_at = base_time + timezone.timedelta(minutes=i)
        page.save()

    # Should fix both duplicates and gaps
    assert fix_document_page_numbering(doc) is True

    # Verify pages are now 0, 1, 2, 3, 4
    fixed_pages = doc.semantic_pages.order_by("number")
    assert [p.number for p in fixed_pages] == [0, 1, 2, 3, 4]
