from typing import List

import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.urls import reverse
from urllib.parse import urlencode
from pydantic import TypeAdapter
from pytest_mock import MockerFixture

from dossier.fakes import (
    add_some_fake_semantic_documents,
    load_initial_document_categories,
)
from dossier.models import Dossier, DocumentCategory, Account
from dossier import schemas as dossier_schemas
from semantic_document.models import SemanticPagePageObject, SemanticPage
from semantic_document.schemas import SemanticDocumentDateSchema
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)
from statemgmt.models import Status
from workers import schemas as worker_schemas
from workers.models import SemanticDocumentExport
from workers.workers import process_semantic_dossier_pdf_request
from django.test.client import Client

pytestmark = pytest.mark.django_db

User: AbstractUser = get_user_model()


def test_get_semantic_document_aggregate_page_objects_sample_dossier(
    testuser1_client, django_assert_max_num_queries
):

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    semantic_document = dossier.semantic_documents.first()

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page__semantic_document=semantic_document
    ).count()

    assert page_object_count > 0

    with django_assert_max_num_queries(21):
        result = testuser1_client.get(
            reverse(
                "api:semantic-document-aggregate-page-objects",
                kwargs={"semantic_document_uuid": semantic_document.uuid},
            )
        )

    assert result.status_code == 200

    parsed = TypeAdapter(List[dossier_schemas.PageObjectApiDataWithUUID]).validate_json(
        result.content
    )

    # We are fetching aggregates, so we should have less page objects than the total
    assert page_object_count > 13

    assert len(parsed) == 13


def test_get_semantic_document_aggregate_page_objects_synthetic_dossier(
    testuser1_client, django_assert_max_num_queries
):

    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    sem_docs = add_some_fake_semantic_documents(
        dossier,
        num_docs=2,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    semantic_document = sem_docs[0]

    with django_assert_max_num_queries(21):

        response = testuser1_client.get(
            path=reverse(
                "api:semantic-document-aggregate-page-objects",
                kwargs={"semantic_document_uuid": semantic_document.uuid},
            )
        )

    assert response.status_code == 200

    parsed = TypeAdapter(List[dossier_schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page__semantic_document=semantic_document
    ).count()

    assert page_object_count > 0

    assert len(parsed) == page_object_count


def test_get_semantic_document_all_page_objects_sample_dossier(db, testuser1_client):

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    semantic_document = dossier.semantic_documents.first()

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page__semantic_document=semantic_document
    ).count()

    assert page_object_count > 0

    response = testuser1_client.get(
        reverse(
            "api:semantic-document-all-page-objects",
            kwargs={"semantic_document_uuid": semantic_document.uuid},
        )
    )

    assert response.status_code == 200

    parsed = TypeAdapter(List[dossier_schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    assert len(parsed) == page_object_count


def test_get_dossier_all_page_objects_sample_synthetic_dossier(
    testuser1_client, django_assert_max_num_queries
):

    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    sem_docs = add_some_fake_semantic_documents(
        dossier,
        num_docs=2,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    semantic_document = sem_docs[0]

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page__semantic_document=semantic_document
    ).count()

    assert page_object_count > 0

    with django_assert_max_num_queries(21):

        response = testuser1_client.get(
            path=reverse(
                "api:semantic-document-all-page-objects",
                kwargs={"semantic_document_uuid": semantic_document.uuid},
            )
        )

    assert response.status_code == 200

    parsed = TypeAdapter(List[dossier_schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    assert len(parsed) == page_object_count


def test_export_dossier_semantic_document_pdf_success(
    testuser1_client,
    mocker: MockerFixture,
):

    user = User.objects.get(username="<EMAIL>")

    # Create a dossier
    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    load_initial_document_categories(account=dossier.account)

    # Ensure that a state machine is created for the account
    created_objects = create_semantic_document_state_machine()

    account = dossier.account

    account.active_semantic_document_work_status_state_machine = created_objects[
        "machine"
    ]
    account.save()
    account.refresh_from_db()

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    # Ensure initial work status is none
    for semantic_document in semantic_documents:
        semantic_document.work_status = None
        semantic_document.save()

    def mock_publish_side_effect(*args, **kwargs) -> str:
        request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
            kwargs["message"]
        )

        # Returns json dump in format SemanticDocumentPDFResponseV1
        return process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request
        )

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect,
    )

    # Additionally check that get_semantic_documents_ready_for_export returns an empty list
    # As we have not set the state to IN_FRONT_OFFICE
    response = testuser1_client.get(
        path=reverse(
            "api:get-semantic-documents-target-state-ready-for-export",
            kwargs={
                "dossier_uuid": str(dossier.uuid),
            },
        ),
    )

    assert response.json() == []

    # Request to generate an export, expect 404 as we have not set the state
    response = testuser1_client.post(
        path=reverse(
            "api:semantic-document-set-state-ready-for-export",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
    )

    # Not found as we have not set IN_FRONT_OFFICE
    assert response.status_code == 404

    ready_for_export_state = Status.objects.get(
        key="IN_FRONT_OFFICE", state_machine=created_objects["machine"]
    )

    semantic_document.work_status = ready_for_export_state

    semantic_document.save()

    response = testuser1_client.get(
        path=reverse(
            "api:get-semantic-documents-target-state-ready-for-export",
            kwargs={
                "dossier_uuid": str(dossier.uuid),
            },
        ),
    )

    assert response.json() == [str(semantic_document.uuid)]

    response = testuser1_client.post(
        path=reverse(
            "api:semantic-document-set-state-ready-for-export",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    semantic_document_export = SemanticDocumentExport.objects.get(uuid=response.json())

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    assert semantic_document_export.semantic_document == semantic_document


def test_get_semantic_pages(testuser1_client, bekbuser1_client):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_document = dossier.semantic_documents.first()

    url = reverse(
        "api:get-semantic-pages",
        kwargs={"semantic_document_uuid": semantic_document.uuid},
    )

    # Unauthenticated user does not have permission
    unauthenticated_client = Client()
    response = unauthenticated_client.get(url)
    assert response.status_code == 401

    # Authenticated user without access to this dossier
    response = bekbuser1_client.get(url)
    assert response.status_code == 404

    # Authenticated user with access to this dossier
    response = testuser1_client.get(url)
    assert response.status_code == 200
    semantic_pages_response = response.json()
    assert len(semantic_pages_response) == len(semantic_document.semantic_pages.all())

    # Test soft deletion query param
    url = reverse(
        "api:get-semantic-pages",
        kwargs={"semantic_document_uuid": semantic_document.uuid},
    )
    query_param = urlencode({"show_soft_deleted_pages": True})
    url = f"{url}?{query_param}"
    response = testuser1_client.get(url)
    assert response.status_code == 200
    semantic_pages_response = response.json()
    assert len(semantic_pages_response) == len(
        SemanticPage.all_objects.filter(semantic_document=semantic_document)
    )


def test_get_semantic_document_date(testuser1_client):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_document = dossier.semantic_documents.first()

    url = reverse(
        "api:get-semantic-document-date",
        kwargs={"semantic_document_uuid": semantic_document.uuid},
    )

    # Authenticated user with access to this dossier
    response = testuser1_client.get(url)
    assert response.status_code == 401

    account = dossier.account

    account.enable_custom_semantic_document_date = True

    account.save()

    response = testuser1_client.get(url)
    assert response.status_code == 200

    assert "00:00:00Z" in response.content.decode("utf-8")

    SemanticDocumentDateSchema.model_validate(response.json())
