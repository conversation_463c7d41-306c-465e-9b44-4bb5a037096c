from typing import List
import uuid

import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.urls import reverse
from pydantic import TypeAdapter

from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import <PERSON><PERSON><PERSON>, Account, DocumentCategory
from dossier import schemas as dossier_schemas
from semantic_document.models import (
    SemanticPagePageObject,
    SemanticPageUserAnnotations,
    SemanticPage,
)
from django.test.client import Client

pytestmark = pytest.mark.django_db

User: AbstractUser = get_user_model()


def test_get_get_semantic_page_page_objects_sample_dossier(
    testuser1_client, django_assert_max_num_queries
):

    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    semantic_page = dossier.semantic_pages.first()

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page=semantic_page
    ).count()

    assert page_object_count > 0

    with django_assert_max_num_queries(21):
        response = testuser1_client.get(
            reverse(
                "api:semantic-page-all-page-objects",
                kwargs={"semantic_page_uuid": semantic_page.uuid},
            )
        )

    assert response.status_code == 200

    parsed = TypeAdapter(List[dossier_schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    assert len(parsed) == page_object_count


def test_get_get_semantic_page_page_objects_synthetic_dossier(
    testuser1_client, django_assert_max_num_queries
):

    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    sem_docs = add_some_fake_semantic_documents(
        dossier,
        num_docs=2,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    semantic_page = sem_docs[0].semantic_pages.first()

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page=semantic_page
    ).count()

    assert page_object_count > 0

    with django_assert_max_num_queries(21):

        response = testuser1_client.get(
            path=reverse(
                "api:semantic-page-all-page-objects",
                kwargs={"semantic_page_uuid": semantic_page.uuid},
            )
        )

    assert response.status_code == 200

    parsed = TypeAdapter(List[dossier_schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    assert len(parsed) == page_object_count


def create_test_annotations(
    semantic_page, annotation_group_uuid=None, annotation_type="highlight"
):
    """Helper function to create test annotations for a semantic page"""
    if annotation_group_uuid is None:
        annotation_group_uuid = uuid.uuid4()

    annotations = [
        SemanticPageUserAnnotations.objects.create(
            semantic_page=semantic_page,
            annotation_type=annotation_type,
            annotation_group_uuid=annotation_group_uuid,
            text=f"Test {annotation_type} {i+1}",
            bbox_left=0.1 + i * 0.05,
            bbox_top=0.2 + i * 0.05,
            bbox_width=0.3 - i * 0.05,
            bbox_height=0.15 - i * 0.02,
        )
        for i in range(2)
    ]
    return annotation_group_uuid, annotations


def test_get_user_annotations(testuser1_client, django_assert_max_num_queries):
    """Test getting user annotations for a semantic page"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotations with different types
    group1_uuid, _ = create_test_annotations(semantic_page, annotation_type="highlight")
    group2_uuid, _ = create_test_annotations(semantic_page, annotation_type="comment")

    with django_assert_max_num_queries(20):
        response = testuser1_client.get(
            reverse(
                "api:semantic-page-user-annotations",
                kwargs={"semantic_page_uuid": semantic_page.uuid},
            )
        )

    assert response.status_code == 200
    data = response.json()

    assert "searchable_pdf_url" in data
    # We expect 2 annotation groups
    assert len(data["user_annotations"]) == 2
    # Each group has 2 annotations
    assert len(data["user_annotations"][str(group1_uuid)]) == 2
    assert len(data["user_annotations"][str(group2_uuid)]) == 2

    # Test filtering by annotation type
    response = testuser1_client.get(
        reverse(
            "api:semantic-page-user-annotations",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + "?annotation_type=highlight"
    )

    assert response.status_code == 200
    data = response.json()
    assert len(data["user_annotations"]) == 1
    assert len(data["user_annotations"][str(group1_uuid)]) == 2


def test_create_user_annotations(testuser1_client):
    """Test creating user annotations for a semantic page"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    annotation_data = [
        {
            "annotation_type": "highlight",
            "text": "Test highlight",
            "bbox_left": 0.1,
            "bbox_top": 0.2,
            "bbox_width": 0.3,
            "bbox_height": 0.15,
        },
        {
            "annotation_type": "highlight",
            "text": "Test highlight 2",
            "bbox_left": 0.15,
            "bbox_top": 0.25,
            "bbox_width": 0.25,
            "bbox_height": 0.12,
        },
    ]

    response = testuser1_client.post(
        reverse(
            "api:semantic-page-user-annotations-create",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        ),
        data=annotation_data,
        content_type="application/json",
    )

    assert response.status_code == 200
    data = response.json()
    assert "annotation_group_uuid" in data

    # Verify annotations were created
    annotations = SemanticPageUserAnnotations.objects.filter(
        semantic_page=semantic_page, annotation_group_uuid=data["annotation_group_uuid"]
    )
    assert annotations.count() == 2


def test_delete_user_annotations(testuser1_client):
    """Test deleting user annotations for a semantic page"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create test annotations
    annotation_group_uuid, _ = create_test_annotations(semantic_page)

    response = testuser1_client.delete(
        reverse(
            "api:semantic-page-user-annotations-delete",
            kwargs={"semantic_page_uuid": str(semantic_page.uuid)},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}"
    )

    assert response.status_code == 200
    data = response.json()
    assert data["annotation_group_uuid"] == str(annotation_group_uuid)

    # Verify annotations were deleted
    assert not SemanticPageUserAnnotations.objects.filter(
        semantic_page=semantic_page, annotation_group_uuid=annotation_group_uuid
    ).exists()


def test_annotation_access_control(testuser1_client, testuser2_client):
    """Test access control for annotation endpoints"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    # Create an annotation
    annotation_group_uuid, _ = create_test_annotations(semantic_page)

    # Test unauthorized access
    client = Client()

    # Get annotations
    response = client.get(
        reverse(
            "api:semantic-page-user-annotations",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
    )
    assert response.status_code in [401, 404]

    # Create annotation
    response = client.post(
        reverse(
            "api:semantic-page-user-annotations-create",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        ),
        data=[
            {
                "annotation_type": "highlight",
                "text": "Test",
                "bbox_left": 10.0,
                "bbox_top": 20.0,
                "bbox_width": 100.0,
                "bbox_height": 50.0,
            }
        ],
        content_type="application/json",
    )
    assert response.status_code in [401, 404]

    # Delete annotation - note the annotation_group_uuid is passed as a query param
    response = client.delete(
        reverse(
            "api:semantic-page-user-annotations-delete",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        )
        + f"?annotation_group_uuid={annotation_group_uuid}"
    )
    assert response.status_code in [401, 404]


def test_delete_undelete_semantic_pages_api(
    testuser1_client,
):
    # Verify the deletion and undeletion of a semantic page, and how it looks via data_v2

    user = User.objects.get(username="<EMAIL>")

    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        owner=user,
        name="test dossier",
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    sem_docs = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    sem_doc = sem_docs[0]

    # We want to use a list as we want to evaluate the query now
    semantic_pages_list: List[SemanticPage] = list(
        sem_doc.semantic_pages.order_by("number", "-updated_at")
        .filter(deleted_at=None)
        .all()
    )

    assert len(semantic_pages_list) == 5

    response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_pages = dossier_data.semantic_documents[0].semantic_pages

    # Verify the lists have the same length
    assert len(semantic_pages_list) == len(semantic_pages)

    # Verify page numbers start at 0 and match between lists, along with UUIDs
    for i, (db_page, api_page) in enumerate(zip(semantic_pages_list, semantic_pages)):
        assert db_page.number == i, f"Expected page number {i}, got {db_page.number}"
        assert api_page.number == i, f"Expected page number {i}, got {api_page.number}"
        assert str(db_page.uuid) == api_page.uuid, f"UUID mismatch on page {i}"

    # Pick a page to delete, say the 3rd one (index 2)
    third_semantic_page = semantic_pages_list[2]

    result = testuser1_client.delete(
        reverse(
            "api:delete-semantic-page",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "semantic_page_uuid": third_semantic_page.uuid,
            },
        ),
    )
    assert result.status_code == 200

    # Now fetch data after deletion

    # We want to use a list as we want to evaluate the query now
    semantic_pages_list_post_deletion: List[SemanticPage] = list(
        sem_doc.semantic_pages.order_by("number", "-updated_at")
        .filter(deleted_at=None)
        .all()
    )

    assert len(semantic_pages_list_post_deletion) == 4

    response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_pages = dossier_data.semantic_documents[0].semantic_pages

    # Verify the lists have the same length
    assert len(semantic_pages_list_post_deletion) == len(semantic_pages)

    # Verify page numbers start at 0 and match between lists, along with UUIDs
    # for i, (db_page, api_page) in enumerate(
    #     zip(semantic_pages_list_post_deletion, semantic_pages)
    # ):
    #     assert db_page.number == i, f"Expected page number {i}, got {db_page.number}"
    #     assert api_page.number == i, f"Expected page number {i}, got {api_page.number}"
    #     assert str(db_page.uuid) == api_page.uuid, f"UUID mismatch on page {i}"

    # No no longer fix page number gaps after soft deletion
    assert semantic_pages_list_post_deletion[0].number == 0
    assert semantic_pages_list_post_deletion[1].number == 1
    assert semantic_pages_list_post_deletion[2].number == 3

    # Now get data_v2, but with show soft deleted page

    response = testuser1_client.get(
        reverse(
            "api:get-dossier-data-v2",
            kwargs={"dossier_uuid": dossier.uuid},
        )
        + "?show_soft_deleted=true",
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_pages = dossier_data.semantic_documents[0].semantic_pages

    semantic_pages_post_deleted_qs = (
        SemanticPage.all_objects.filter(semantic_document=sem_doc)
        .order_by("number", "-updated_at")
        .all()
    )

    # Verify the lists have the same length
    assert len(semantic_pages) == semantic_pages_post_deleted_qs.count()

    # Ordering should be the same
    for db_page, api_page in zip(semantic_pages_post_deleted_qs, semantic_pages):
        assert (
            db_page.number == api_page.number
        ), f"Page numbers should match for page {db_page.uuid}"
        assert (
            str(db_page.uuid) == api_page.uuid
        ), f"UUIDs should match for page {db_page.number}"

    # Now undelete the page
    result = testuser1_client.patch(
        reverse(
            "api:undelete-semantic-page",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "semantic_page_uuid": third_semantic_page.uuid,
            },
        ),
    )
    assert result.status_code == 200

    # Refetch data_v2 and check that the ordering matches the original
    response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_pages = dossier_data.semantic_documents[0].semantic_pages

    # Verify the lists have the same length
    assert len(semantic_pages_list) == len(semantic_pages)

    # Verify page numbers start at 0 and match between lists, along with UUIDs
    for i, (db_page, api_page) in enumerate(zip(semantic_pages_list, semantic_pages)):
        assert db_page.number == i, f"Expected page number {i}, got {db_page.number}"
        assert api_page.number == i, f"Expected page number {i}, got {api_page.number}"
        assert str(db_page.uuid) == api_page.uuid, f"UUID mismatch on page {i}"


def test_create_user_annotations_with_hexcolor(testuser1_client):
    """Test creating user annotations with custom and default hexcolor values"""
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_page = dossier.semantic_pages.first()

    annotation_data = [
        {
            "annotation_type": "highlight",
            "text": "Test highlight with custom color",
            "bbox_left": 0.1,
            "bbox_top": 0.2,
            "bbox_width": 0.3,
            "bbox_height": 0.15,
            "hexcolor": "#FF0000",  # Custom red color
        },
        {
            "annotation_type": "highlight",
            "text": "Test highlight with default color",
            "bbox_left": 0.15,
            "bbox_top": 0.25,
            "bbox_width": 0.25,
            "bbox_height": 0.12,
            # No hexcolor specified - should use default "#FFFF00"
        },
    ]

    response = testuser1_client.post(
        reverse(
            "api:semantic-page-user-annotations-create",
            kwargs={"semantic_page_uuid": semantic_page.uuid},
        ),
        data=annotation_data,
        content_type="application/json",
    )

    assert response.status_code == 200
    data = response.json()
    assert "annotation_group_uuid" in data

    # Verify annotations were created with correct colors
    annotations = SemanticPageUserAnnotations.objects.filter(
        semantic_page=semantic_page, annotation_group_uuid=data["annotation_group_uuid"]
    ).order_by("text")
    assert annotations.count() == 2

    # Check custom color annotation
    custom_color_annotation = annotations[0]  # First one alphabetically by text
    assert custom_color_annotation.hexcolor == "#FF0000"
    assert custom_color_annotation.text == "Test highlight with custom color"

    # Check default color annotation
    default_color_annotation = annotations[1]  # Second one alphabetically by text
    assert default_color_annotation.hexcolor == "#FFFF00"  # Default color
    assert default_color_annotation.text == "Test highlight with default color"
