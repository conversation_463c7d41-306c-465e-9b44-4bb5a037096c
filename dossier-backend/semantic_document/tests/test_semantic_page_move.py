from datetime import timed<PERSON><PERSON>

import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.utils import timezone

from dossier.fakes import add_some_fake_semantic_documents

from dossier.models import <PERSON><PERSON><PERSON>, Account
from dossier.services import create_dossier
from semantic_document.helpers import fix_soft_deleted_position_block_approach
from semantic_document.models import SemanticDocument, SemanticPage


User: AbstractUser = get_user_model()

pytestmark = pytest.mark.django_db


def soft_delete_page(page):
    soft_del_time = timezone.now() - timedelta(days=1)
    page.deleted_at = soft_del_time
    page.save()


@pytest.fixture
def test_dossier(db):
    Dossier.objects.all().delete()
    account = Account.objects.get(key="default")
    user = User.objects.get(username="<EMAIL>")
    dossier = create_dossier(
        account,
        "source_test_dossier",
        "de",
        user,
    )
    dossier.save()
    return dossier


def test_empty_document(test_dossier):
    target_semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        test_dossier,
        num_docs=1,
        allow_empty_docs=True,
        max_pages=0,
        min_num_pages=0,
    )[0]
    fix_soft_deleted_position_block_approach(target_semantic_document, [])
    assert target_semantic_document.semantic_pages.count() == 0


def test_no_issues_document(test_dossier):
    """Test document with sequential numbering and no soft-deleted pages"""
    target_semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        test_dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=3,
        min_num_pages=3,
    )[0]

    fix_soft_deleted_position_block_approach(
        target_semantic_document,
        [page for page in target_semantic_document.semantic_pages.order_by("number")],
    )

    updated_pages = target_semantic_document.semantic_pages.order_by("number")
    assert [page.number for page in updated_pages] == [0, 1, 2]


@pytest.fixture()
def create_semantic_document_5_pages(test_dossier: Dossier) -> tuple:
    # Create a semantic document.
    target_semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        test_dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=5,
        min_num_pages=5,
    )[0]

    semantic_pages = target_semantic_document.semantic_pages.order_by("number")

    # pages:
    page_E = semantic_pages[0]
    page_F = semantic_pages[1]
    page_G = semantic_pages[2]
    page_H = semantic_pages[3]
    page_I = semantic_pages[4]

    return target_semantic_document, page_E, page_F, page_G, page_H, page_I


@pytest.fixture()
def create_semantic_document_4_pages(test_dossier: Dossier) -> tuple:
    # Create a semantic document.
    source_semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        test_dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=5,
        min_num_pages=5,
    )[0]

    semantic_pages = source_semantic_document.semantic_pages.order_by("number")

    # pages:
    page_A = semantic_pages[0]
    page_B = semantic_pages[1]
    page_C = semantic_pages[2]
    page_D = semantic_pages[3]

    return source_semantic_document, page_A, page_B, page_C, page_D


def test_fix_soft_deleted_position_middle_block(create_semantic_document_5_pages):
    """
    Test a document with a soft-deleted block in the middle.
    Original order: [E, F, (G deleted), (H deleted), I]
    Active pages (as in the current DB ordering): [E, F, I]
    After fixing, we expect the merged order to be: [E, F, G, H, I]
    with sequential numbering 0...4
    """
    target_semantic_document, page_E, page_F, page_G, page_H, page_I = (
        create_semantic_document_5_pages
    )

    # Soft-deleted pages (simulate duplicate numbering with F, for example).
    soft_delete_page(page_G)
    soft_delete_page(page_H)

    # The original ordering (from the source document) is:
    # [E, F, G, H, I]
    original_pages = [page_E, page_F, page_G, page_H, page_I]

    # Call the function.
    fix_soft_deleted_position_block_approach(target_semantic_document, original_pages)

    # Reload all pages (including soft-deleted) in order.
    pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    # Expected final ordering: [E, F, G, H, I]
    expected_order = [page_E.uuid, page_F.uuid, page_G.uuid, page_H.uuid, page_I.uuid]
    assert [p.uuid for p in pages] == expected_order

    # Expected sequential numbering.
    expected_numbers = list(range(5))
    assert [p.number for p in pages] == expected_numbers


def test_fix_soft_deleted_position_start_block(create_semantic_document_5_pages):
    """
    Test a document with a soft-deleted block at the beginning.
    Original order: [(E deleted), (F deleted), G, H, I]
    Active pages: [G, H, I]
    After fixing, we expect the merged order to be: [E, F, G, H, I]
    with sequential numbering 0...4.
    """

    target_semantic_document, page_E, page_F, page_G, page_H, page_I = (
        create_semantic_document_5_pages
    )

    soft_delete_page(page_E)
    soft_delete_page(page_F)

    # The original ordering (from the source document) is:
    original_pages = [page_E, page_F, page_G, page_H, page_I]

    # Call the function.
    fix_soft_deleted_position_block_approach(target_semantic_document, original_pages)

    # Reload all pages (including soft-deleted) in order.
    pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    # Expected final ordering: [E, F, G, H, I]
    expected_order = [page_E.uuid, page_F.uuid, page_G.uuid, page_H.uuid, page_I.uuid]
    assert [p.uuid for p in pages] == expected_order

    # Expected sequential numbering.
    expected_numbers = list(range(5))
    assert [p.number for p in pages] == expected_numbers


def test_fix_soft_deleted_move_document(create_semantic_document_5_pages):
    """
    Original order:  [E, (Soft Deleted F), (Soft Deleted G), H, I]

    Active pages: [I]
    After fixing, we expect the merged order to be: [E, F, G, H, I]
    with sequential numbering 0...4.
    """

    target_semantic_document, page_E, page_F, page_G, page_H, page_I = (
        create_semantic_document_5_pages
    )

    soft_delete_page(page_F)
    soft_delete_page(page_G)

    # The original ordering (from the source document) is:
    original_pages = [page_E, page_F, page_G, page_H, page_I]

    page_E.number = 5
    page_E.save()

    # Call the function.
    fix_soft_deleted_position_block_approach(target_semantic_document, original_pages)

    # Reload all pages (including soft-deleted) in order.
    pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    expected_order = [page_F.uuid, page_G.uuid, page_H.uuid, page_I.uuid, page_E.uuid]
    assert [p.uuid for p in pages] == expected_order

    # Expected sequential numbering.
    assert [p.number for p in pages] == list(range(5))


def test_fix_soft_deleted_delete_start(create_semantic_document_5_pages):
    """
    Original order:  [E, F, (G soft deleted), (H soft deleted), I]

    Active pages: [I]
    After fixing, we expect the merged order to be: [E, F, G, H, I]
    with sequential numbering 0...4.
    """

    target_semantic_document, page_E, page_F, page_G, page_H, page_I = (
        create_semantic_document_5_pages
    )

    # The original ordering (from the source document) is:
    original_pages = [page_E, page_F, page_G, page_H, page_I]

    soft_delete_page(page_F)
    soft_delete_page(page_G)

    # Hard delete page E
    page_E.hard_delete()

    # Call the function.
    fix_soft_deleted_position_block_approach(target_semantic_document, original_pages)

    # Reload all pages (including soft-deleted) in order.
    pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    expected_order = [page_F.uuid, page_G.uuid, page_H.uuid, page_I.uuid]
    assert [p.uuid for p in pages] == expected_order

    # Expected sequential numbering.
    expected_numbers = list(range(4))
    assert [p.number for p in pages] == expected_numbers


def test_fix_soft_deleted_non_consecutive(test_dossier):

    target_semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        test_dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=10,
        min_num_pages=10,
    )[0]

    original_pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    page_4 = original_pages[3]
    page_5 = original_pages[4]

    soft_delete_page(page_4)
    soft_delete_page(page_5)

    # Renumber pages so they are non consecutive and start at 2
    for i, page in enumerate(original_pages):
        page.number = i * 2 + 2
        page.save()

    fix_soft_deleted_position_block_approach(target_semantic_document, original_pages)

    # Reload all pages (including soft-deleted) in order.
    pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    assert [p.uuid for p in pages] == [p.uuid for p in pages]

    # Expected sequential numbering.
    assert [p.number for p in pages] == list(range(10))

    assert pages[3] == page_4
    assert pages[4] == page_5


def test_fix_soft_deleted_non_consecutive_missing_first_page(test_dossier):

    target_semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        test_dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=10,
        min_num_pages=10,
    )[0]

    original_pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    page_4 = original_pages[3]
    page_5 = original_pages[4]

    soft_delete_page(page_4)
    soft_delete_page(page_5)

    # Renumber pages so they are non consecutive and start at 2
    for i, page in enumerate(original_pages):
        page.number = i * 2 + 2
        page.save()

    fix_soft_deleted_position_block_approach(target_semantic_document, original_pages)

    # Reload all pages (including soft-deleted) in order.
    pages = list(
        SemanticPage.all_objects.filter(
            semantic_document=target_semantic_document
        ).order_by("number")
    )

    assert [p.uuid for p in pages] == [p.uuid for p in pages]

    # Expected sequential numbering.
    assert [p.number for p in pages] == list(range(10))

    assert pages[3] == page_4
    assert pages[4] == page_5
