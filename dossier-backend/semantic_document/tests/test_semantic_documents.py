import json
from pathlib import Path
from typing import List
from uuid import uuid4

import pytest
from django.contrib.auth.models import User
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Prefetch, QuerySet
from django.urls import reverse_lazy, reverse
from pydantic import TypeAdapter

from dossier.helpers import is_document_topic_property
from events.models import Event

from core.authentication import AuthenticatedClient
from doccheck import schemas as doccheck_schemas
from doccheck.models import (
    BusinessCaseType as DocCheckBusinessCaseType,
    Entity,
    CompletenessRule,
    DocCheck,
    Case,
    DocumentCategory as DocCheckDocumentCategory,
    DocumentOption,
)
from doccheck.services import add_completeness_rule_to_check
from doccheck.tests.test_services import create_doc_check
from dossier.models import (
    Dossier,
    DocumentCategory,
    available_document_categories,
    ConfidenceLevel,
    BusinessCaseType,
    Account,
    RealestateProperty,
)
from dossier.services import create_dossier
from dossier.tests.common import tax_declaration_max_mustermann
from processed_file.models import PageObjectTitle
from semantic_document import helpers, schemas, models
from semantic_document.conftest import assert_page_objects_exist
from semantic_document.helpers import (
    get_start_work_status_from_dossier_account,
    get_semantic_documents_with_incorrect_page_numbering,
)
from semantic_document.models import (
    SemanticDocument,
    DocCheckAssignment,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPage,
    SemanticPagePageObject,
)
from semantic_document.schemas import RequirementStatus, COMBINE_PAGES_UNASSIGNED
from semantic_document.services import (
    assign_semantic_documents_to_doccheck_rule,
    assign_semantic_documents_to_doccheck_rules_automatically,
)
from semantic_document.services_doccheck import (
    get_doccheck_document_assignments,
    is_doccheck_fulfilled,
)
from semantic_document import helpers as semantic_document_helpers


def remove_uuid(details):
    try:
        del details["uuid"]
        for move in details["moves"]:
            del move["destination"]["semantic_document"]["uuid"]
            del move["destination"]["semantic_page_uuid"]

    except KeyError:
        pass

    return details


@pytest.mark.django_db
def test_created_unnamed_semantic_document():
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_document = helpers.create_unnamed_semantic_document(dossier, {})
    assert semantic_document.document_category.name == "UNKNOWN"
    assert semantic_document.dossier == dossier
    assert semantic_document.title_custom == "___ DOC Unnamed Document"
    assert semantic_document.confidence_level == ConfidenceLevel.HIGH
    assert semantic_document.confidence_formatted == "100%"
    assert semantic_document.confidence_value == 100


@pytest.mark.django_db
def test_semantic_document_title_suffix_recommendations_all_pages_from_one_extracted_file():
    semantic_document = tax_declaration_max_mustermann()
    assert semantic_document.title_suffix_recommendation == ["TAX_max_maria_2019 4.pdf"]


@pytest.mark.django_db
def test_semantic_document_title_suffix_recommendations_pages_from_multiple_extracted_files():
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    semantic_documents = dossier.semantic_documents.all()

    first_semantic_document = semantic_documents[0]

    for other_semantic_document in semantic_documents[1:]:
        for page in other_semantic_document.semantic_pages.all():
            page.semantic_document = first_semantic_document
            page.save()

    recommendations = first_semantic_document.title_suffix_recommendation

    assert len(recommendations) == 3  # we should only get the 3 most common filenames
    assert recommendations == [
        "TAX_max_maria_2019 4.pdf",
        "pk_mt_zwei_stück.pdf",
        "fzg_mt.pdf",
    ]


@pytest.mark.django_db
def test_document_category_available_should_not_show_excluded_for_recommendation():
    # check that we first have some document categories available
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    assert len(available_document_categories(dossier.account)) > 0

    # now set all to exclude_for_recommendation = True
    for document_category in DocumentCategory.objects.all():
        document_category.exclude_for_recommendation = True
        document_category.save()

    # we should not have any available document categories
    assert len(available_document_categories(dossier.account)) == 0


@pytest.mark.django_db
def test_get_semantic_documents(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    result = testuser1_client.get(f"/api/semantic_documents/{dossier.uuid}")
    assert result.status_code == 200


@pytest.mark.django_db
def test_logging_semantic_document_renamed(testuser1_client: AuthenticatedClient):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    Event.objects.all().delete()
    assert Event.objects.count() == 0

    request_json = Path(__file__).parent / "data/rename_request.json"
    result = testuser1_client.post(
        f"/api/semantic_documents/{dossier.uuid}/update_title",
        data=request_json.read_text(),
        content_type="application/json",
    )
    assert result.status_code == 200

    db_events = [
        ((event.type, remove_uuid(event.details))) for event in Event.objects.all()
    ]
    print(db_events)
    assert db_events == [
        (
            "semantic_document.schemas.SemanticDocumentRenamedEvent",
            {
                "source": {
                    "uuid": "4b2299ee-6f31-465c-b2f5-68631bca45de",
                    "title": "210 Pass CH Andreas Dominik»",
                    "document_category_name": "PASSPORT_CH",
                },
                "username": "<EMAIL>",
                "destination": {
                    "uuid": "4b2299ee-6f31-465c-b2f5-68631bca45de",
                    "title": "211 PASSPORT_DE Andreas Dominik»",
                    "document_category_name": "PASSPORT_DE",
                },
            },
        )
    ]


@pytest.mark.django_db
def test_logging_move_semantic_pages_to_new_semantic_document(
    testuser1_client: AuthenticatedClient,
):
    """
    Simulates the following event:
    - 2 pages in the dossier "sales pitch" get moved via the menu to the new document 212 PASSPORT_IT
    - page 1: semantic_page_uuid=993e3f85-5562-4869-82d0-f510173c818b,
        processed_page_uuid=6418888c-b718-41c2-8f08-a547597c370f,
        semantic_document="210 Pass CH Andreas Dominik"
    - page 2: semantic_page_uuid=95d02581-940b-4b93-8d09-8a089bd57b12,
        processed_page_uuid=2dde580e-bee9-4d27-ada0-68f9503d9916,
        semantic_document="310 Steuererklärung Mustermann Max", second page
    - The event deletes the semantic_pages and creates new ones at the destination, but the processed_page_uuid stays
      intact.
    - The request body give the target structure (but only for the new document), with the pages already moved.
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    Event.objects.all().delete()
    assert Event.objects.count() == 0

    request_json = Path(__file__).parent / "data/menu_move_new_request.json"
    result = testuser1_client.post(
        reverse(
            "api:move-semantic-pages-to-new-document",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=request_json.read_text(),
        content_type="application/json",
    )
    assert result.status_code == 200

    db_events = [
        ((event.type, remove_uuid(event.details))) for event in Event.objects.all()
    ]
    print(db_events)
    assert db_events == [
        (
            "semantic_document.schemas.SemanticPagesMovedEvent",
            {
                "moves": [
                    {
                        "source": {
                            "page_number": 0,
                            "semantic_document": {
                                "uuid": "4b2299ee-6f31-465c-b2f5-68631bca45de",
                                "title": "210 Pass CH Andreas Dominik»",
                                "document_category_name": "PASSPORT_CH",
                            },
                            "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b",
                            "processed_page_uuid": "51b2d9f6-ca64-42b6-8cff-3e5a7bbeb038",
                        },
                        "destination": {
                            "page_number": 0,
                            "semantic_document": {
                                "title": "212 Pass IT",
                                "document_category_name": "PASSPORT_IT",
                            },
                            "processed_page_uuid": "51b2d9f6-ca64-42b6-8cff-3e5a7bbeb038",
                        },
                    },
                    {
                        "source": {
                            "page_number": 1,
                            "semantic_document": {
                                "uuid": "7bf53b35-0fff-4c5f-8988-e23896175836",
                                "title": "310 Steuererklärung Mustermann Max ZH 2019",
                                "document_category_name": "TAX_DECLARATION",
                            },
                            "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12",
                            "processed_page_uuid": "2dde580e-bee9-4d27-ada0-68f9503d9916",
                        },
                        "destination": {
                            "page_number": 1,
                            "semantic_document": {
                                "title": "212 Pass IT",
                                "document_category_name": "PASSPORT_IT",
                            },
                            "processed_page_uuid": "2dde580e-bee9-4d27-ada0-68f9503d9916",
                        },
                    },
                ],
                "username": "<EMAIL>",
            },
        )
    ]


@pytest.mark.django_db
@pytest.mark.parametrize(
    "url, enable_real_estate_property",
    [
        ("api:move-semantic-pages-to-new-document", True),
        ("api:move-semantic-pages-to-new-document", False),
        ("api:copy-semantic-pages-to-new-document", True),
        ("api:copy-semantic-pages-to-new-document", False),
    ],
)
def test_move_copy_semantic_pages_to_new_semantic_document_add_real_estate(
    url,
    enable_real_estate_property,
    testuser1_client: AuthenticatedClient,
):
    """
    Simulates the following event:
    - 2 pages in the dossier "sales pitch" get moved via the menu to the new document 212 PASSPORT_IT
    - page 1: semantic_page_uuid=993e3f85-5562-4869-82d0-f510173c818b,
        processed_page_uuid=6418888c-b718-41c2-8f08-a547597c370f,
        semantic_document="210 Pass CH Andreas Dominik"
    - page 2: semantic_page_uuid=95d02581-940b-4b93-8d09-8a089bd57b12,
        processed_page_uuid=2dde580e-bee9-4d27-ada0-68f9503d9916,
        semantic_document="310 Steuererklärung Mustermann Max", second page
    - The event deletes the semantic_pages and creates new ones at the destination, but the processed_page_uuid stays
      intact.
    - The request body give the target structure (but only for the new document), with the pages already moved.
    - Additionally a realestate property is assigned to the new document.
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    Event.objects.all().delete()
    assert Event.objects.count() == 0

    request_dict = json.loads(
        (Path(__file__).parent / "data/menu_move_new_request.json").read_text()
    )

    semantic_page_uuids = [x["uuid"] for x in request_dict["semantic_pages"]]

    assert SemanticPage.objects.filter(uuid__in=semantic_page_uuids).count() == 2

    if enable_real_estate_property:
        property_entity = RealestateProperty.objects.create(
            dossier=dossier, key="mansion"
        )
        request_dict["real_estate_property_key"] = "mansion"

    result = testuser1_client.post(
        reverse(
            url,
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps(request_dict),
        content_type="application/json",
    )
    assert result.status_code == 200

    parsed = schemas.MoveSemanticPagesToNewDocumentResponseSchema.model_validate_json(
        result.content
    )

    semantic_document = SemanticDocument.objects.get(uuid=parsed.semantic_document_uuid)

    if enable_real_estate_property:
        assert parsed.real_estate_property_key == property_entity.key

        assert AssignedRealEstatePropertySemanticDocument.objects.get(
            semantic_document=semantic_document,
            realestate_property=property_entity,
        )

    # If we are copying make sure old pages still exist
    if url == "api:copy-semantic-pages-to-new-document":
        assert SemanticPage.objects.filter(uuid__in=semantic_page_uuids).count() == 2
    else:
        # If we are moving make sure old pages are deleted (soft deleted)
        assert SemanticPage.objects.filter(uuid__in=semantic_page_uuids).count() == 0


@pytest.mark.django_db
def test_logging_move_semantic_pages_to_existing_semantic_document(
    testuser1_client: AuthenticatedClient,
):
    """
    Simulates the following event:
    - 2 pages in the dossier "sales pitch" get moved via the menu to the existing document 210 Pass CH
    - page 1: semantic_page_uuid=afa3f050-19e4-4837-98cf-54f4193054e6,
        processed_page_uuid=8ed57ad1-9cbb-423f-977d-1093f72145c5,
        semantic_document="245 Strafregisterauszug Thiemann Manuel", first page
    - page 2: semantic_page_uuid=95d02581-940b-4b93-8d09-8a089bd57b12,
        processed_page_uuid=2dde580e-bee9-4d27-ada0-68f9503d9916,
        semantic_document="310 Steuererklärung Mustermann Max", second page
    - The event deletes the semantic_pages and creates new ones at the destination, but the processed_page_uuid stays
      intact.
    - The request body give the target structure (of all effected documents), with the pages already moved.
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    Event.objects.all().delete()
    assert Event.objects.count() == 0

    request_json = Path(__file__).parent / "data/menu_move_existing_request.json"
    result = testuser1_client.post(
        f"/api/semantic_documents/{dossier.uuid}/move_semantic_pages",
        data=request_json.read_text(),
        content_type="application/json",
    )
    assert result.status_code == 200

    db_events = [
        ((event.type, remove_uuid(event.details))) for event in Event.objects.all()
    ]
    print(db_events)
    assert db_events == [
        (
            "semantic_document.schemas.SemanticPagesMovedEvent",
            {
                "moves": [
                    {
                        "source": {
                            "page_number": 0,
                            "semantic_document": {
                                "uuid": "a7e7b2da-a760-4ff9-851f-52e9c75ecdd5",
                                "title": "245 Strafregisterauszug Thiemann Manuel Antonius 2020-09-01",
                                "document_category_name": "CRIMINAL_RECORDS",
                            },
                            "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6",
                            "processed_page_uuid": "8ed57ad1-9cbb-423f-977d-1093f72145c5",
                        },
                        "destination": {
                            "page_number": 1,
                            "semantic_document": {
                                "title": "210 Pass CH Andreas Dominik»",
                                "document_category_name": "PASSPORT_CH",
                            },
                            "processed_page_uuid": "8ed57ad1-9cbb-423f-977d-1093f72145c5",
                        },
                    },
                    {
                        "source": {
                            "page_number": 1,
                            "semantic_document": {
                                "uuid": "7bf53b35-0fff-4c5f-8988-e23896175836",
                                "title": "310 Steuererklärung Mustermann Max ZH 2019",
                                "document_category_name": "TAX_DECLARATION",
                            },
                            "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12",
                            "processed_page_uuid": "2dde580e-bee9-4d27-ada0-68f9503d9916",
                        },
                        "destination": {
                            "page_number": 2,
                            "semantic_document": {
                                "title": "210 Pass CH Andreas Dominik»",
                                "document_category_name": "PASSPORT_CH",
                            },
                            "processed_page_uuid": "2dde580e-bee9-4d27-ada0-68f9503d9916",
                        },
                    },
                ],
                "username": "<EMAIL>",
            },
        )
    ]


@pytest.mark.django_db
def test_logging_move_semantic_pages_dnd(testuser1_client: AuthenticatedClient):
    """
    Simulates the following event:
    - 2 pages in the dossier "sales pitch" get moved via dnd to the document 210 Pass CH
    - page 1: semantic_page_uuid=afa3f050-19e4-4837-98cf-54f4193054e6,
        processed_page_uuid=8ed57ad1-9cbb-423f-977d-1093f72145c5,
        semantic_document="245 Strafregisterauszug Thiemann Manuel", first page
    - page 2: semantic_page_uuid=95d02581-940b-4b93-8d09-8a089bd57b12,
        processed_page_uuid=2dde580e-bee9-4d27-ada0-68f9503d9916,
        semantic_document="310 Steuererklärung Mustermann Max", second page
    - The event deletes the semantic_pages and creates new ones at the destination, but the processed_page_uuid stays
      intact.
    - The request body give the target structure (of all effected documents), with the pages already moved, but also
      include some intermediate move targets! See comment in save_result_dnd_to_db().
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    Event.objects.all().delete()
    assert Event.objects.count() == 0

    request_json = Path(__file__).parent / "data/dnd_move_request.json"
    result = testuser1_client.post(
        f"/api/semantic_documents/{dossier.uuid}/move_semantic_pages",
        data=request_json.read_text(),
        content_type="application/json",
    )
    assert result.status_code == 200

    db_events = [
        ((event.type, remove_uuid(event.details))) for event in Event.objects.all()
    ]
    print(db_events)
    assert db_events == [
        (
            "semantic_document.schemas.SemanticPagesMovedEvent",
            {
                "moves": [
                    {
                        "source": {
                            "page_number": 0,
                            "semantic_document": {
                                "uuid": "a7e7b2da-a760-4ff9-851f-52e9c75ecdd5",
                                "title": "245 Strafregisterauszug Thiemann Manuel Antonius 2020-09-01",
                                "document_category_name": "CRIMINAL_RECORDS",
                            },
                            "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6",
                            "processed_page_uuid": "8ed57ad1-9cbb-423f-977d-1093f72145c5",
                        },
                        "destination": {
                            "page_number": 1,
                            "semantic_document": {
                                "title": "210 Pass CH Andreas Dominik»",
                                "document_category_name": "PASSPORT_CH",
                            },
                            "processed_page_uuid": "8ed57ad1-9cbb-423f-977d-1093f72145c5",
                        },
                    },
                    {
                        "source": {
                            "page_number": 1,
                            "semantic_document": {
                                "uuid": "7bf53b35-0fff-4c5f-8988-e23896175836",
                                "title": "310 Steuererklärung Mustermann Max ZH 2019",
                                "document_category_name": "TAX_DECLARATION",
                            },
                            "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12",
                            "processed_page_uuid": "2dde580e-bee9-4d27-ada0-68f9503d9916",
                        },
                        "destination": {
                            "page_number": 2,
                            "semantic_document": {
                                "title": "210 Pass CH Andreas Dominik»",
                                "document_category_name": "PASSPORT_CH",
                            },
                            "processed_page_uuid": "2dde580e-bee9-4d27-ada0-68f9503d9916",
                        },
                    },
                ],
                "username": "<EMAIL>",
            },
        )
    ]


@pytest.mark.django_db
def test_move_semantic_pages_dnd_malformed_request(
    testuser1_client: AuthenticatedClient,
):
    """
    Test the case where we get a 500 error
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    Event.objects.all().delete()
    assert Event.objects.count() == 0

    request_json = Path(__file__).parent / "data/dnd_move_malformed_request.json"
    result = testuser1_client.post(
        f"/api/semantic_documents/{dossier.uuid}/move_semantic_pages",
        data=request_json.read_text(),
        content_type="application/json",
    )
    assert result.status_code == 200


@pytest.fixture
def simple_case_with_completeness_rules(db):
    user = User.objects.get(username="<EMAIL>")
    doc_check = create_doc_check()
    account = Account.objects.create(
        key="test", name="Test Account", active_doc_check=doc_check
    )

    BusinessCaseType.objects.all().delete()
    dossier_business_case_type = BusinessCaseType.objects.create(
        account=account, key="test business case type"
    )
    assert BusinessCaseType.objects.count() == 1
    doccheck_business_case_type = DocCheckBusinessCaseType.objects.create(
        doc_check=doc_check, key=dossier_business_case_type.key
    )
    assert DocCheckBusinessCaseType.objects.count() == 2

    new_dossier = create_dossier(
        account,
        "sample dossier",
        "de",
        user,
        businesscase_type_id=dossier_business_case_type.uuid,
    )
    new_dossier.save()

    rule_person = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=doccheck_schemas.CompletenessRule(
            key="test_rule_person", entity=Entity.Person
        ),
    )
    rule_person.business_case_types.add(doccheck_business_case_type)
    doc_check_doc_cat_id = DocCheckDocumentCategory.objects.create(
        doc_check=doc_check, name="ID"
    )
    DocumentOption.objects.create(
        completeness_rule=rule_person, document_category=doc_check_doc_cat_id
    )
    doc_check_doc_cat_passport_ch = DocCheckDocumentCategory.objects.create(
        doc_check=doc_check, name="PASSPORT_CH"
    )
    DocumentOption.objects.create(
        completeness_rule=rule_person, document_category=doc_check_doc_cat_passport_ch
    )
    rule_property = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=doccheck_schemas.CompletenessRule(
            key="test_rule_property", entity=Entity.RealEstateProperty
        ),
    )
    rule_property.business_case_types.add(doccheck_business_case_type)
    doc_check_doc_cat_id = DocCheckDocumentCategory.objects.create(
        doc_check=doc_check, name="ZEK_CHECK"
    )
    DocumentOption.objects.create(
        completeness_rule=rule_property, document_category=doc_check_doc_cat_id
    )
    rule_no_assignemnt = add_completeness_rule_to_check(
        doc_check=doc_check,
        rule_to_create=doccheck_schemas.CompletenessRule(
            key="test_rule_no_assignment", entity=Entity.RealEstateProperty
        ),
    )
    rule_no_assignemnt.business_case_types.add(doccheck_business_case_type)

    DOC_CAT_ID, _ = DocumentCategory.objects.get_or_create(name="ID", account=account)
    SemanticDocument.objects.create(
        dossier=new_dossier,
        confidence_level=ConfidenceLevel.HIGH,
        confidence_value=0.9,
        document_category=DOC_CAT_ID,
        work_status=get_start_work_status_from_dossier_account(dossier=new_dossier),
    )
    SemanticDocument.objects.create(
        dossier=new_dossier,
        confidence_level=ConfidenceLevel.HIGH,
        confidence_value=0.9,
        document_category=DOC_CAT_ID,
        work_status=get_start_work_status_from_dossier_account(dossier=new_dossier),
    )
    PASSPORT_CH, _ = DocumentCategory.objects.get_or_create(
        name="PASSPORT_CH", account=account
    )
    SemanticDocument.objects.create(
        dossier=new_dossier,
        confidence_level=ConfidenceLevel.HIGH,
        confidence_value=0.9,
        document_category=PASSPORT_CH,
        work_status=get_start_work_status_from_dossier_account(dossier=new_dossier),
    )
    DOC_CAT_ZECK_CHECK, _ = DocumentCategory.objects.get_or_create(
        name="ZEK_CHECK", account=account
    )
    SemanticDocument.objects.create(
        dossier=new_dossier,
        confidence_level=ConfidenceLevel.HIGH,
        confidence_value=0.9,
        document_category=DOC_CAT_ZECK_CHECK,
        work_status=get_start_work_status_from_dossier_account(dossier=new_dossier),
    )

    return new_dossier.doccheck_case


def test_doccheck_assignment(db, simple_case_with_completeness_rules):
    case_1 = simple_case_with_completeness_rules
    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()
    dossier = Dossier.objects.get(doccheck_case=case_1)
    semantic_doc_id = SemanticDocument.objects.filter(
        dossier=dossier,
        document_category=DocumentCategory.objects.get(
            name="ID", account=dossier.account
        ),
    )[0]
    semantic_doc_passport = SemanticDocument.objects.get(
        dossier__doccheck_case=case_1,
        document_category=DocumentCategory.objects.get(
            name="PASSPORT_CH", account=dossier.account
        ),
    )
    semantic_doc_zek = SemanticDocument.objects.get(
        dossier__doccheck_case=case_1,
        document_category=DocumentCategory.objects.get(
            name="ZEK_CHECK", account=dossier.account
        ),
    )

    assert DocCheckAssignment.objects.count() == 0

    doc_assignment_1 = assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
        fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
        semantic_documents=[semantic_doc_id],
    )
    saved_assignment_1 = DocCheckAssignment.objects.get(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
    )
    assert DocCheckAssignment.objects.count() == 1
    assert (
        DocCheckAssignment.objects.get(uuid=doc_assignment_1.uuid) == saved_assignment_1
    )
    assert [
        doc.uuid for doc in saved_assignment_1.assigned_documents.order_by("uuid").all()
    ] == sorted([semantic_doc_id.uuid])

    doc_assignment_2 = assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
        fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
        semantic_documents=[semantic_doc_passport, semantic_doc_zek],
    )
    saved_assignment_2 = DocCheckAssignment.objects.get(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
    )
    assert DocCheckAssignment.objects.count() == 1
    assert (
        DocCheckAssignment.objects.get(uuid=doc_assignment_2.uuid) == saved_assignment_2
    )
    assert [
        doc.uuid for doc in saved_assignment_2.assigned_documents.order_by("uuid").all()
    ] == sorted([semantic_doc_passport.uuid, semantic_doc_zek.uuid])


def test_doccheck_assignment_comment_success(db, simple_case_with_completeness_rules):
    case_1 = simple_case_with_completeness_rules

    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()

    doc_assignment_1 = assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
        fulfillment_type=models.FulfillmentType.BYPASSING,
        comment="no assignment necessary",
    )
    saved_assignment_1 = DocCheckAssignment.objects.get(uuid=doc_assignment_1.uuid)
    assert list(saved_assignment_1.assigned_documents.all()) == []
    assert saved_assignment_1.comment == "no assignment necessary"


def test_doccheck_assignment_comment_empty(db, simple_case_with_completeness_rules):
    case_1 = simple_case_with_completeness_rules

    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()

    doc_assignment_1 = assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
        fulfillment_type=models.FulfillmentType.BYPASSING,
    )
    saved_assignment_1 = DocCheckAssignment.objects.get(uuid=doc_assignment_1.uuid)
    assert list(saved_assignment_1.assigned_documents.all()) == []
    assert saved_assignment_1.comment is None


def test_doccheck_assignment_comment_none(db, simple_case_with_completeness_rules):
    case_1 = simple_case_with_completeness_rules

    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()

    comment = "There is a non-empty comment"

    doc_assignment_1 = assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
        fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
        comment=comment,
    )
    saved_assignment_1 = DocCheckAssignment.objects.get(uuid=doc_assignment_1.uuid)
    assert list(saved_assignment_1.assigned_documents.all()) == []
    assert saved_assignment_1.comment == comment

    # Now update the assignment again but put comment == None. This should NOT override the existing comment
    doc_assignment_2 = assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
        fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
        comment=None,
    )
    saved_assignment_2 = DocCheckAssignment.objects.get(uuid=doc_assignment_2.uuid)
    assert list(saved_assignment_2.assigned_documents.all()) == []
    assert saved_assignment_2.comment == comment  # This is unchanged.

    # Now delete the comment by setting it to empty string
    doc_assignment_3 = assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_1,
        fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
        comment="",
    )
    saved_assignment_3 = DocCheckAssignment.objects.get(uuid=doc_assignment_3.uuid)
    assert list(saved_assignment_3.assigned_documents.all()) == []
    assert saved_assignment_3.comment is None  # This is now deleted.


def test_doccheck_assignment_errors(db, simple_case_with_completeness_rules):
    case_1 = simple_case_with_completeness_rules
    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()
    semantic_doc_id = SemanticDocument.objects.filter(
        dossier__doccheck_case=case_1, document_category__name="ID"
    )[0]

    some_other_doccheck = DocCheck.objects.create(key="entirely different check")
    rule_from_some_other_doccheck = CompletenessRule.objects.create(
        doc_check=some_other_doccheck,
        key="not_matching_rule",
        entity=Entity.Person,
        strictness="required",
    )
    with pytest.raises(
        LookupError, match="case and completeness_rule don't have the same doc_check"
    ):
        assign_semantic_documents_to_doccheck_rule(
            case=case_1,
            context_model_uuid=case_1_person_1.uuid,
            completeness_rule=rule_from_some_other_doccheck,
            fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
            semantic_documents=[semantic_doc_id],
        )

    with pytest.raises(LookupError, match="context_model_uuid is not part of case"):
        assign_semantic_documents_to_doccheck_rule(
            case=case_1,
            context_model_uuid=uuid4(),
            completeness_rule=completeness_rule_1,
            fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
            semantic_documents=[semantic_doc_id],
        )

    some_other_case = Case.objects.create(
        doc_check=DocCheck.objects.first(),
        business_case_type=DocCheckBusinessCaseType.objects.first(),
    )
    dossier_with_changed_case = Dossier.objects.get(uuid=semantic_doc_id.dossier.uuid)
    dossier_with_changed_case.doccheck_case = some_other_case
    dossier_with_changed_case.save()
    semantic_doc_id.dossier = dossier_with_changed_case
    semantic_doc_id.save()
    with pytest.raises(
        LookupError,
        match=f"case associated with semantic document uuid={semantic_doc_id.uuid} does not match case given",
    ):
        assign_semantic_documents_to_doccheck_rule(
            case=case_1,
            context_model_uuid=case_1_person_1.uuid,
            completeness_rule=completeness_rule_1,
            fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
            semantic_documents=[semantic_doc_id],
        )

    # drop this check for now as we want the frontend dialogue to be able to reset the assignments to no doc and no comment
    # with pytest.raises(LookupError, match="semantic_documents and comment cannot both be None"):
    #     assign_semantic_documents_to_doccheck_rule(
    #         case=case_1, context_model_uuid=case_1_person_1.uuid, completeness_rule=completeness_rule_1)


def test_doccheck_assignments_out(db, simple_case_with_completeness_rules):
    case_1 = simple_case_with_completeness_rules
    case_1_person_1 = case_1.person_set.first()
    case_1_property_1 = case_1.realestateproperty_set.first()
    completeness_rule_person = CompletenessRule.objects.get(key="test_rule_person")
    completeness_rule_property = CompletenessRule.objects.get(key="test_rule_property")
    semantic_doc_id = SemanticDocument.objects.filter(
        dossier__doccheck_case=case_1, document_category__name="ID"
    )[0]
    semantic_doc_passport = SemanticDocument.objects.get(
        dossier__doccheck_case=case_1, document_category__name="PASSPORT_CH"
    )

    assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_person,
        fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
        semantic_documents=[semantic_doc_id, semantic_doc_passport],
        comment="test",
    )
    assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_property_1.uuid,
        completeness_rule=completeness_rule_property,
        fulfillment_type=models.FulfillmentType.BYPASSING,
        comment="no assignment necessary",
    )
    assignments = get_doccheck_document_assignments(case=case_1)

    assert len(assignments) == 3
    for assignment in assignments:
        assert assignment.case_uuid == case_1.uuid

    assignment_person = next(
        (
            assignment
            for assignment in assignments
            if assignment.rule_key == "test_rule_person"
        ),
        None,
    )
    assert assignment_person
    assert assignment_person.assigned_document_titles == sorted(
        [semantic_doc_id.title, semantic_doc_passport.title]
    )
    assert assignment_person.comment == "test"
    assert (
        assignment_person.requirement_status == RequirementStatus.FULFILLED_ASSIGNMENT
    )

    assignment_property = next(
        (
            assignment
            for assignment in assignments
            if assignment.rule_key == "test_rule_property"
        ),
        None,
    )
    assert assignment_property
    assert assignment_property.assigned_document_titles == []
    assert assignment_property.comment == "no assignment necessary"
    assert assignment_property.requirement_status == RequirementStatus.FULFILLED_COMMENT

    assignment_not_fulfilled = next(
        (
            assignment
            for assignment in assignments
            if assignment.rule_key == "test_rule_no_assignment"
        ),
        None,
    )
    assert assignment_not_fulfilled
    assert assignment_not_fulfilled.assigned_document_titles == []
    assert assignment_not_fulfilled.comment is None
    assert (
        assignment_not_fulfilled.requirement_status == RequirementStatus.NOT_FULFILLED
    )


def test_is_doccheck_fulfilled(db, simple_case_with_completeness_rules):
    # Dossier without associated doccheck (no case)
    dossier_without_case = Dossier.objects.get(
        name="sales pitch mix with errors dossier"
    )
    assert dossier_without_case.doccheck_case is None
    assert is_doccheck_fulfilled(dossier=dossier_without_case) is True

    case_1 = simple_case_with_completeness_rules
    case_1_person_1 = case_1.person_set.first()
    case_1_property_1 = case_1.realestateproperty_set.first()
    completeness_rule_person = CompletenessRule.objects.get(key="test_rule_person")
    completeness_rule_property = CompletenessRule.objects.get(key="test_rule_property")
    completeness_rule_no_assignment = CompletenessRule.objects.get(
        key="test_rule_no_assignment"
    )
    semantic_doc_id = SemanticDocument.objects.filter(
        dossier__doccheck_case=case_1, document_category__name="ID"
    )[0]
    semantic_doc_passport = SemanticDocument.objects.get(
        dossier__doccheck_case=case_1, document_category__name="PASSPORT_CH"
    )

    assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_person_1.uuid,
        completeness_rule=completeness_rule_person,
        fulfillment_type=models.FulfillmentType.DOC_ASSIGNMENT,
        semantic_documents=[semantic_doc_id, semantic_doc_passport],
        comment="test",
    )
    assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_property_1.uuid,
        completeness_rule=completeness_rule_property,
        fulfillment_type=models.FulfillmentType.BYPASSING,
        comment="no assignment necessary",
    )
    assert is_doccheck_fulfilled(dossier=case_1.dossier) is False

    assign_semantic_documents_to_doccheck_rule(
        case=case_1,
        context_model_uuid=case_1_property_1.uuid,
        completeness_rule=completeness_rule_no_assignment,
        fulfillment_type=models.FulfillmentType.BYPASSING,
        comment="rule bypassed",
    )
    assert is_doccheck_fulfilled(dossier=case_1.dossier) is True


def test_doccheck_assignments_automatically(db, simple_case_with_completeness_rules):
    case_1 = simple_case_with_completeness_rules
    completeness_rule_person = CompletenessRule.objects.get(key="test_rule_person")
    completeness_rule_property = CompletenessRule.objects.get(key="test_rule_property")
    dossier_doc_cats = [
        doc.document_category.name for doc in case_1.dossier.semantic_documents.all()
    ]
    rule_person_doc_cat_options = [
        doc_cat
        for doc_cat in [
            option.document_category.name
            for option in completeness_rule_person.documentoption_set.all()
        ]
        if dossier_doc_cats.count(doc_cat) == 1
    ]
    rule_person_doc_title_options = [
        doc.title
        for doc in case_1.dossier.semantic_documents.all()
        if doc.document_category.name in rule_person_doc_cat_options
    ]
    rule_property_doc_cat_options = [
        doc_cat
        for doc_cat in [
            option.document_category.name
            for option in completeness_rule_property.documentoption_set.all()
        ]
        if dossier_doc_cats.count(doc_cat) == 1
    ]
    rule_property_doc_title_options = [
        doc.title
        for doc in case_1.dossier.semantic_documents.all()
        if doc.document_category.name in rule_property_doc_cat_options
    ]

    assignments_only_shown = assign_semantic_documents_to_doccheck_rules_automatically(
        case=case_1, make_assignment=False
    )
    assert len(assignments_only_shown) == 2
    assignment_person = next(
        (
            assignment
            for assignment in assignments_only_shown
            if assignment.rule_key == completeness_rule_person.key
        ),
        None,
    )
    assert assignment_person
    assert assignment_person.assigned_document_titles == sorted(
        rule_person_doc_title_options
    )
    assignment_property = next(
        (
            assignment
            for assignment in assignments_only_shown
            if assignment.rule_key == completeness_rule_property.key
        ),
        None,
    )
    assert assignment_property
    assert assignment_property.assigned_document_titles == sorted(
        rule_property_doc_title_options
    )
    assert DocCheckAssignment.objects.count() == 0

    assign_semantic_documents_to_doccheck_rules_automatically(
        case=case_1, make_assignment=True
    )
    assert DocCheckAssignment.objects.count() == 2

    assignments_only_shown2 = assign_semantic_documents_to_doccheck_rules_automatically(
        case=case_1, make_assignment=False
    )
    assert len(assignments_only_shown2) == 0


def test_doccheck_out_api(
    db, testuser1_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    res = testuser1_doc_client.get(
        reverse_lazy("api:doccheck_assignment", kwargs=dict(case_uuid=case_1.uuid))
    )
    assert res.status_code == 200


def test_doccheck_out_api_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    res = testuser2_doc_client.get(
        reverse_lazy("api:doccheck_assignment", kwargs=dict(case_uuid=case_1.uuid))
    )
    assert res.status_code == 404


def test_docheck_assignement_without_doc_api(
    db, testuser1_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()

    # First set an assignment with bypassing -> this should create the entity
    res = testuser1_doc_client.post(
        reverse_lazy("api:doccheck_assignment", kwargs=dict(case_uuid=case_1.uuid)),
        data=schemas.DocCheckAssignmentCreate(
            context_model_uuid=case_1_person_1.uuid,
            completeness_rule_key=completeness_rule_1.key,
            fulfillment_type=schemas.FulfillmentType.BYPASSING,
            comment="first comment",
        ).model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 201

    # Second set an assignment with document assignment -> this should update the entity
    res2 = testuser1_doc_client.post(
        reverse_lazy("api:doccheck_assignment", kwargs=dict(case_uuid=case_1.uuid)),
        data=schemas.DocCheckAssignmentCreate(
            context_model_uuid=case_1_person_1.uuid,
            completeness_rule_key=completeness_rule_1.key,
            fulfillment_type=schemas.FulfillmentType.DOC_ASSIGNMENT,
            comment="second comment",
        ).model_dump_json(),
        content_type="application/json",
    )
    assert res2.status_code == 201


def test_doccheck_assignment_api(
    db, testuser1_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()
    comment = "test777"

    res = testuser1_doc_client.post(
        reverse_lazy("api:doccheck_assignment", kwargs=dict(case_uuid=case_1.uuid)),
        data=schemas.DocCheckAssignmentCreate(
            context_model_uuid=case_1_person_1.uuid,
            completeness_rule_key=completeness_rule_1.key,
            fulfillment_type=schemas.FulfillmentType.BYPASSING,
            comment=comment,
        ).model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 201

    # Now check if we get the correct comment and fulfillment_type back
    res2 = testuser1_doc_client.get(
        reverse_lazy("api:doccheck_assignment", kwargs=dict(case_uuid=case_1.uuid))
    )
    assert res2.status_code == 200
    print(res2)
    res2_checks_out: List[schemas.DocCheckAssignmentOut] = TypeAdapter(
        List[schemas.DocCheckAssignmentOut]
    ).validate_json(res2.content)
    assert len(res2_checks_out) >= 1
    for d in res2_checks_out:
        if (
            d.context_model_uuid == case_1_person_1.uuid
            and d.rule_key == completeness_rule_1.key
        ):
            # found the rule we just added
            assert d.comment == comment
            assert d.fulfillment_type == schemas.FulfillmentType.BYPASSING
            assert d.requirement_status == RequirementStatus.FULFILLED_COMMENT


def test_doccheck_assignment_api_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    case_1_person_1 = case_1.person_set.first()
    completeness_rule_1 = CompletenessRule.objects.first()

    res = testuser2_doc_client.post(
        reverse_lazy("api:doccheck_assignment", kwargs=dict(case_uuid=case_1.uuid)),
        data=schemas.DocCheckAssignmentCreate(
            context_model_uuid=case_1_person_1.uuid,
            completeness_rule_key=completeness_rule_1.key,
            fulfillment_type=schemas.FulfillmentType.BYPASSING,
            comment="test",
        ).model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 404


def test_show_doccheck_assignment_automatically_api(
    db, testuser1_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    res = testuser1_doc_client.get(
        reverse_lazy(
            "api:doccheck_assignment_automatically", kwargs=dict(case_uuid=case_1.uuid)
        )
    )
    assert res.status_code == 200

    assignments = TypeAdapter(List[schemas.DocCheckAssignmentOut]).validate_python(
        res.json()
    )

    assert len(assignments) == 2
    for a in assignments:
        assert a.rule_key


def test_show_doccheck_assignment_automatically_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    res = testuser2_doc_client.get(
        reverse_lazy(
            "api:doccheck_assignment_automatically", kwargs=dict(case_uuid=case_1.uuid)
        )
    )
    assert res.status_code == 404


def test_do_doccheck_assignment_automatically_api(
    db, testuser1_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    res = testuser1_doc_client.post(
        reverse_lazy(
            "api:doccheck_assignment_automatically", kwargs=dict(case_uuid=case_1.uuid)
        )
    )
    assert res.status_code == 201


def test_do_doccheck_assignment_automatically_no_access(
    db, testuser2_doc_client: AuthenticatedClient, simple_case_with_completeness_rules
):
    case_1 = simple_case_with_completeness_rules
    res = testuser2_doc_client.post(
        reverse_lazy(
            "api:doccheck_assignment_automatically", kwargs=dict(case_uuid=case_1.uuid)
        )
    )
    assert res.status_code == 404


@pytest.mark.django_db
def test_realestate_property_to_semantic_document(
    testuser1_client: AuthenticatedClient,
):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    semantic_document = dossier.semantic_documents.first()

    semantic_document.document_category = DocumentCategory.objects.filter(
        id__startswith="6", account=dossier.account
    ).first()
    semantic_document.save()

    property_entity = RealestateProperty.objects.create(dossier=dossier, key="mansion")

    response = testuser1_client.post(
        path=reverse(
            "api:assign-real-estate-property",
            kwargs={"semantic_document_uuid": semantic_document.uuid},
        ),
        data={
            "real_estate_property_key": "mansion",
        },
        content_type="application/json",
    )

    assert response.status_code == 201

    assert (
        RealestateProperty.objects.filter(
            assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
        ).first()
        == property_entity
    )

    RealestateProperty.objects.create(dossier=dossier, key="flat")

    # Test Get assigned property
    response = testuser1_client.get(
        path=reverse(
            "api:get-real-estate-properties-assigned",
            kwargs={"semantic_document_uuid": semantic_document.uuid},
        ),
    )

    assert response.status_code == 200

    parsed = schemas.RealEstatePropertyEntity.model_validate_json(response.content)

    assert parsed.key == "mansion"

    # Test Get properties associated with dossier (i.e. all properties)
    response = testuser1_client.get(
        path=reverse(
            "api:get-real-estate-properties",
            kwargs={"semantic_document_uuid": semantic_document.uuid},
        ),
    )

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.RealEstatePropertyEntity]).validate_json(
        response.content
    )

    assert len(parsed) == 2
    assert "mansion" in [parsed[0].key, parsed[1].key]
    assert "flat" in [parsed[0].key, parsed[1].key]

    # Test getting assigned properties via dossier uuid

    response = testuser1_client.get(
        path=reverse(
            "api:get-real-estate-properties-dossier-assigned",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
    )

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.RealEstatePropertyEntity]).validate_json(
        response.content
    )

    assert len(parsed) == 1
    # Only mansion is assigned
    assert "mansion" == parsed[0].key


@pytest.mark.django_db
def test_realestate_property_to_semantic_document_failure(
    testuser1_client: AuthenticatedClient,
):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    semantic_document = dossier.semantic_documents.first()

    # Don't allow assigning to non property documents
    semantic_document.document_category = DocumentCategory.objects.filter(
        id__startswith="2", account=dossier.account
    ).first()
    semantic_document.save()

    response = testuser1_client.post(
        path=reverse(
            "api:assign-real-estate-property",
            kwargs={"semantic_document_uuid": semantic_document.uuid},
        ),
        data={
            "real_estate_property_key": "mansion",
        },
        content_type="application/json",
    )

    assert response.status_code == 400


@pytest.mark.django_db
def test_get_category_mappings_for_dossier(
    testuser1_client: AuthenticatedClient,
):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    property_entity = RealestateProperty.objects.create(dossier=dossier, key="mansion")

    # currently we filter document categories starting with 6 in function
    # is_document_topic_property
    property_type_semantic_documents = dossier.semantic_documents.filter(
        document_category__id__startswith="6"
    )

    property_type_semantic_document = property_type_semantic_documents.first()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=property_type_semantic_document,
        realestate_property=property_entity,
    )

    response = testuser1_client.get(
        path=reverse(
            "api:get-entity-mappings-dossier",
            kwargs={"dossier_uuid": dossier.uuid},
        )
    )

    assert response.status_code == 200

    parsed = schemas.SemanticDocumentEntityMappings.model_validate_json(
        response.content
    )

    assert len(parsed.type_mappings) == len(property_type_semantic_documents)

    for doc in property_type_semantic_documents.all():
        assert parsed.type_mappings[str(doc.uuid)] == [
            schemas.EntityTypes.REAL_ESTATE_PROPERTY
        ]


@pytest.mark.django_db
def test_semantic_document_rename_real_estate_assignment(
    testuser1_client: AuthenticatedClient,
):
    """Test various aspects of renaming a semantic document

    Changing category from 601 t0 602 should not change the real estate property assignment

    Changing category from 601 to any non 6, e.g. 201 should remove the real estate property assignment
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    request_json = json.loads(
        (Path(__file__).parent / "data/rename_request.json").read_text()
    )

    # currently we filter document categories starting with 6 in function
    # which represents property types
    categories_property = DocumentCategory.objects.filter(
        id__startswith="6", account=dossier.account
    )

    categories_property_1 = categories_property[0]
    categories_property_2 = categories_property[1]

    assert categories_property_1.id != categories_property_2.id

    semantic_document = dossier.semantic_documents.filter(
        uuid=request_json["uuid"]
    ).first()

    semantic_document.document_category = categories_property_1
    semantic_document.save()

    # Create a real estate property and assign it to the semantic document
    realestate_property = RealestateProperty.objects.create(
        dossier=dossier, key="mansion"
    )

    result = testuser1_client.post(
        f"/api/semantic_documents/{dossier.uuid}/update_title",
        data=json.dumps(
            {
                **request_json,
                "name": categories_property_2.name,
                "id": categories_property_2.id,
            }
        ),
        content_type="application/json",
    )

    assert result.status_code == 200

    # Check that the real estate property is still assigned
    assert AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document=semantic_document, realestate_property=realestate_property
    ).exists()

    semantic_document.refresh_from_db()

    assert semantic_document.document_category == categories_property_2

    # Change the category to a non property category
    category_identity = DocumentCategory.objects.filter(
        id__startswith="2", account=dossier.account
    ).first()

    result = testuser1_client.post(
        f"/api/semantic_documents/{dossier.uuid}/update_title",
        data=json.dumps(
            {**request_json, "name": category_identity.name, "id": category_identity.id}
        ),
        content_type="application/json",
    )

    assert result.status_code == 200

    assert not AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document=semantic_document, realestate_property=realestate_property
    ).exists()

    # Check the property still exists, just no longer assigned
    realestate_property.refresh_from_db()

    assert realestate_property


@pytest.mark.django_db
def test_semantic_document_rename_real_estate_auto_assignment(
    testuser1_client: AuthenticatedClient,
):
    """Test various aspects of renaming a semantic document

    If a category is changed to a real estate category,
    and there existis only one real estate property,
    on the dossier, auto assign it
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    request_json = json.loads(
        (Path(__file__).parent / "data/rename_request.json").read_text()
    )

    # currently we filter document categories starting with 6 in function
    # which represents property types
    categories_property = DocumentCategory.objects.filter(
        id__startswith="6", account=dossier.account
    ).first()

    semantic_document = dossier.semantic_documents.filter(
        uuid=request_json["uuid"]
    ).first()

    # Check that the currently assigned one is of non property type
    assert not is_document_topic_property(semantic_document.document_category)

    # Create a real estate property and assign it to the dossier
    realestate_property = RealestateProperty.objects.create(
        dossier=dossier, key="mansion"
    )

    # Doesn't exist yet
    assert not AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document=semantic_document, realestate_property=realestate_property
    ).exists()

    result = testuser1_client.post(
        f"/api/semantic_documents/{dossier.uuid}/update_title",
        data=json.dumps(
            {
                **request_json,
                "name": categories_property.name,
                "id": categories_property.id,
            }
        ),
        content_type="application/json",
    )
    assert result.status_code == 200

    # Has been assigned
    assert AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document=semantic_document, realestate_property=realestate_property
    ).exists()

    semantic_document.refresh_from_db()

    assert semantic_document.document_category == categories_property


@pytest.mark.django_db
@pytest.mark.parametrize("category_id,expected", [("2", False), ("6", True)])
def test_is_document_topic_property(
    bekbuser1_client: AuthenticatedClient, category_id, expected
):
    document_category = DocumentCategory.objects.filter(
        id__startswith=category_id
    ).first()

    result = bekbuser1_client.post(
        reverse_lazy("api:is_document_topic_property"),
        data=schemas.CategoryGet(
            name=document_category.name, id=document_category.id
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 200

    assert result.json() is expected


@pytest.mark.django_db
def test_combine_semantic_pages_to_new_semantic_document(
    testuser1_client: AuthenticatedClient,
):
    """
    Test that combining semantic pages into a new semantic document
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")
    Event.objects.all().delete()
    assert Event.objects.count() == 0

    payload = schemas.CombinePagesRequestBody.model_validate_json(
        (Path(__file__).parent / "data/menu_combine_semantic_pages.json").read_text(),
    )

    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=payload.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 201

    result = schemas.CreatedSemanticDocument.model_validate_json(response.content)

    semantic_document = SemanticDocument.objects.get(uuid=result.uuid)

    semantic_pages = SemanticPage.objects.filter(semantic_document=semantic_document)

    assert semantic_pages.count() == 2


@pytest.mark.django_db
def test_combine_pages_real_estate_property_success_empty(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test combine_pages_real_estate_property API, where no semantic documents are of a photo category
    """

    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages-real-estate-property",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps({"document_category_key": photo_category.name}),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert response.json() == []


@pytest.mark.django_db
def test_combine_pages_real_estate_property_success_no_merges(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test combine_pages_real_estate_property API, two documents with different real estate properties,
    and one un-assigned

    none should be combined
    """

    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    # No real estate for this one
    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages-real-estate-property",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps({"document_category_key": photo_category.name}),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert response.json() == []


@pytest.mark.django_db
def test_combine_pages_real_estate_property_success_single_matching_category(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test combine_pages_real_estate_property API,

    One category matches, 'mansion'
    """

    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    expected_pages_count = first.semantic_pages.count() + third.semantic_pages.count()

    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages-real-estate-property",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps({"document_category_key": photo_category.name}),
        content_type="application/json",
    )

    assert response.status_code == 201
    result = TypeAdapter(List[schemas.CreatedSemanticDocument]).validate_json(
        response.content
    )
    assert len(result) == 1

    new_semantic_document = SemanticDocument.objects.get(uuid=result[0].uuid)

    assert expected_pages_count == new_semantic_document.semantic_pages.count()

    ordered_semantic_pages: QuerySet[SemanticPage] = (
        new_semantic_document.semantic_pages.all().order_by("number")
    )

    # Check that the new pages are ordered starting from zero
    assert ordered_semantic_pages[0].number == 0

    # Check that the new pages have correct sequential ordering
    results = get_semantic_documents_with_incorrect_page_numbering(dossier.account)
    assert len(results["start_at_one"]) == 0
    assert len(results["start_at_two_or_more"]) == 0
    assert len(results["non_consecutive"]) == 0

    assert AssignedRealEstatePropertySemanticDocument.objects.get(
        semantic_document=new_semantic_document
    )

    # Check previous assignments have been deleted
    assert not AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document__uuid__in=[first.uuid, third.uuid]
    ).exists()

    # Semantic docs have been soft deleted
    with pytest.raises(SemanticDocument.DoesNotExist):
        first.refresh_from_db()
    with pytest.raises(SemanticDocument.DoesNotExist):
        third.refresh_from_db()

    second.refresh_from_db()

    assert second


@pytest.mark.django_db
def test_combine_pages_real_estate_property_success_two_matching_categories(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Two categories match, 'mansion' and 'flat'

    also add a single semantic document without a real estate property assigned (should not match)
    """

    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    fourth = semantic_documents_with_page_count[3]
    fourth.document_category = photo_category
    fourth.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=fourth,
        realestate_property=property_entity_flat,
    )

    # With no real estate property attached
    fifth = semantic_documents_with_page_count[4]
    fifth.document_category = photo_category
    fifth.save()

    expected_pages_count_mansion = (
        first.semantic_pages.count() + third.semantic_pages.count()
    )
    expected_pages_count_flat = (
        second.semantic_pages.count() + fourth.semantic_pages.count()
    )

    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages-real-estate-property",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps({"document_category_key": photo_category.name}),
        content_type="application/json",
    )

    assert response.status_code == 201
    result = TypeAdapter(List[schemas.CreatedSemanticDocument]).validate_json(
        response.content
    )
    assert len(result) == 2

    new_semantic_document_uuids = [r.uuid for r in result]

    new_semantic_document_mansion = SemanticDocument.objects.get(
        assignedrealestatepropertysemanticdocument__realestate_property=property_entity_mansion,
        uuid__in=new_semantic_document_uuids,
    )
    new_semantic_document_flat = SemanticDocument.objects.get(
        assignedrealestatepropertysemanticdocument__realestate_property=property_entity_flat,
        uuid__in=new_semantic_document_uuids,
    )

    assert (
        expected_pages_count_mansion
        == new_semantic_document_mansion.semantic_pages.count()
    )

    assert (
        expected_pages_count_flat == new_semantic_document_flat.semantic_pages.count()
    )

    # Check previous assignments have been deleted
    assert not AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document__uuid__in=[first.uuid, second.uuid, third.uuid, fourth.uuid]
    ).exists()

    assert not SemanticDocument.objects.filter(
        uuid__in=[first.uuid, second.uuid, third.uuid, fourth.uuid]
    ).exists()

    fifth.refresh_from_db()

    assert fifth


@pytest.mark.django_db
def test_combine_pages_real_estate_property_success_three_matching_categories_including_unassigned(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Three categories match, 'mansion', 'flat' and unassigned

    also add a single semantic document without a real estate property assigned (should not match)
    """

    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    # We had a bug where page objects where lost under certain circumstances when semantic pages where merged
    # assert all initial pages have page objects
    first_doc_page_object_count = assert_page_objects_exist(first)

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    second_doc_page_object_count = assert_page_objects_exist(second)

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    third_doc_page_object_count = assert_page_objects_exist(third)

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    fourth = semantic_documents_with_page_count[3]
    fourth.document_category = photo_category
    fourth.save()

    fourth_doc_page_object_count = assert_page_objects_exist(fourth)

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=fourth,
        realestate_property=property_entity_flat,
    )

    # With no real estate property attached
    fifth = semantic_documents_with_page_count[4]
    fifth.document_category = photo_category
    fifth.save()

    fifth_doc_page_object_count = assert_page_objects_exist(fifth)

    # With no real estate property attached
    sixth = semantic_documents_with_page_count[5]
    sixth.document_category = photo_category
    sixth.save()

    sixth_doc_page_object_count = assert_page_objects_exist(sixth)

    expected_pages_count_mansion = (
        first.semantic_pages.count() + third.semantic_pages.count()
    )

    expected_page_object_count_mansion = (
        first_doc_page_object_count + third_doc_page_object_count
    )

    expected_pages_count_flat = (
        second.semantic_pages.count() + fourth.semantic_pages.count()
    )

    expected_page_object_count_flat = (
        second_doc_page_object_count + fourth_doc_page_object_count
    )

    expected_pages_count_unassigned = (
        fifth.semantic_pages.count() + sixth.semantic_pages.count()
    )

    expected_page_object_count_unassigned = (
        fifth_doc_page_object_count + sixth_doc_page_object_count
    )

    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages-real-estate-property",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps({"document_category_key": photo_category.name}),
        content_type="application/json",
    )

    assert response.status_code == 201
    result = TypeAdapter(List[schemas.CreatedSemanticDocument]).validate_json(
        response.content
    )
    assert len(result) == 3

    new_semantic_document_uuids = [r.uuid for r in result]

    new_semantic_document_mansion = SemanticDocument.objects.get(
        assignedrealestatepropertysemanticdocument__realestate_property=property_entity_mansion,
        uuid__in=new_semantic_document_uuids,
    )
    new_semantic_document_flat = SemanticDocument.objects.get(
        assignedrealestatepropertysemanticdocument__realestate_property=property_entity_flat,
        uuid__in=new_semantic_document_uuids,
    )
    new_semantic_document_unassigned = SemanticDocument.objects.get(
        assignedrealestatepropertysemanticdocument__realestate_property=None,
        uuid__in=new_semantic_document_uuids,
    )

    # We had a bug where page objects where lost under certain circumstances when semantic pages where merged
    # assert all new pages have page objects, and that their counts match the sum of the original documents
    assert (
        assert_page_objects_exist(new_semantic_document_mansion)
        == expected_page_object_count_mansion
    )
    assert (
        assert_page_objects_exist(new_semantic_document_flat)
        == expected_page_object_count_flat
    )
    assert (
        assert_page_objects_exist(new_semantic_document_unassigned)
        == expected_page_object_count_unassigned
    )

    assert (
        expected_pages_count_mansion
        == new_semantic_document_mansion.semantic_pages.count()
    )

    assert (
        expected_pages_count_flat == new_semantic_document_flat.semantic_pages.count()
    )

    assert (
        expected_pages_count_unassigned
        == new_semantic_document_unassigned.semantic_pages.count()
    )

    # Check previous assignments have been deleted
    assert not AssignedRealEstatePropertySemanticDocument.objects.filter(
        semantic_document__uuid__in=[first.uuid, second.uuid, third.uuid, fourth.uuid]
    ).exists()

    assert not SemanticDocument.objects.filter(
        uuid__in=[
            first.uuid,
            second.uuid,
            third.uuid,
            fourth.uuid,
            fifth.uuid,
            sixth.uuid,
        ]
    ).exists()


@pytest.mark.django_db
def test_check_permitted_combine_pages_real_estate_property_no_matching_categories(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test permutations permitted for combine_pages_real_estate_properties

    Two semantic documents in photo category, but different real estate properties assigned
    """
    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    assert response.status_code == 201

    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    # Should be empty as there are no semantic documents with more than one real estate property assigned
    # in photo category
    assert result.combinations == {}


@pytest.mark.django_db
def test_check_permitted_combine_pages_real_estate_property_single_matching_category(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test permutations permitted for combine_pages_real_estate_properties

    One category matches, 'mansion'
    """
    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    # Now semantic docs first and third should be allowed to be merged
    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    # We have one real estate property
    assert len(result.combinations) == 1
    assert "mansion" in result.combinations
    # We have two semantic documents with this real estate property, to merge
    assert len(result.combinations["mansion"]) == 2
    # Check correct semantic documents are in the list
    assert first.uuid in [
        x.semantic_document_uuid for x in result.combinations["mansion"]
    ]
    assert third.uuid in [
        x.semantic_document_uuid for x in result.combinations["mansion"]
    ]

    # Total pages add up correctly
    assert first.semantic_pages.count() + third.semantic_pages.count() == sum(
        [len(x.semantic_pages_uuid) for x in result.combinations["mansion"]]
    )


@pytest.mark.django_db
def test_check_permitted_combine_pages_real_estate_property_two_matching_categories(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test permutations permitted for combine_pages_real_estate_properties

    Two categories match, 'mansion' and 'flat'

    also add a single semantic document without a real estate property assigned (should not match)
    """
    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    fourth = semantic_documents_with_page_count[3]
    fourth.document_category = photo_category
    fourth.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=fourth,
        realestate_property=property_entity_flat,
    )

    # With no real estate property attached
    fifth = semantic_documents_with_page_count[4]
    fifth.document_category = photo_category
    fifth.save()

    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    # Now semantic docs first and third, second and fourth should be allowed to be merged
    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    # We have two real estate properties
    assert len(result.combinations) == 2
    assert "mansion" in result.combinations
    assert "flat" in result.combinations
    # We have two semantic documents with this real estate property, to merge
    assert len(result.combinations["mansion"]) == 2
    # Check correct semantic documents are in the list
    assert first.uuid in [
        x.semantic_document_uuid for x in result.combinations["mansion"]
    ]
    assert third.uuid in [
        x.semantic_document_uuid for x in result.combinations["mansion"]
    ]

    assert len(result.combinations["flat"]) == 2
    assert second.uuid in [
        x.semantic_document_uuid for x in result.combinations["flat"]
    ]
    assert fourth.uuid in [
        x.semantic_document_uuid for x in result.combinations["flat"]
    ]

    # Total pages add up correctly
    assert first.semantic_pages.count() + third.semantic_pages.count() == sum(
        [len(x.semantic_pages_uuid) for x in result.combinations["mansion"]]
    )

    assert second.semantic_pages.count() + fourth.semantic_pages.count() == sum(
        [len(x.semantic_pages_uuid) for x in result.combinations["flat"]]
    )


@pytest.mark.django_db
def test_check_permitted_combine_pages_real_estate_property_three_matching_categories_including_unassigned(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test permutations permitted for combine_pages_real_estate_properties

    Three categories match, 'mansion', 'flat' and unassigned

    """
    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    fourth = semantic_documents_with_page_count[3]
    fourth.document_category = photo_category
    fourth.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=fourth,
        realestate_property=property_entity_flat,
    )

    # With no real estate property attached
    fifth = semantic_documents_with_page_count[4]
    fifth.document_category = photo_category
    fifth.save()

    # With no real estate property attached
    sixth = semantic_documents_with_page_count[5]
    sixth.document_category = photo_category
    sixth.save()

    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    # We have two real estate properties, and one without an assignment
    assert len(result.combinations) == 3
    assert "mansion" in result.combinations
    assert "flat" in result.combinations
    assert COMBINE_PAGES_UNASSIGNED in result.combinations
    # We have two semantic documents with this real estate property, to merge
    assert len(result.combinations["mansion"]) == 2
    assert len(result.combinations["flat"]) == 2
    assert len(result.combinations[COMBINE_PAGES_UNASSIGNED]) == 2

    assert fifth.uuid in [
        x.semantic_document_uuid for x in result.combinations[COMBINE_PAGES_UNASSIGNED]
    ]
    assert sixth.uuid in [
        x.semantic_document_uuid for x in result.combinations[COMBINE_PAGES_UNASSIGNED]
    ]

    # Total pages add up correctly
    assert first.semantic_pages.count() + third.semantic_pages.count() == sum(
        [len(x.semantic_pages_uuid) for x in result.combinations["mansion"]]
    )

    assert second.semantic_pages.count() + fourth.semantic_pages.count() == sum(
        [len(x.semantic_pages_uuid) for x in result.combinations["flat"]]
    )

    assert fifth.semantic_pages.count() + sixth.semantic_pages.count() == sum(
        [
            len(x.semantic_pages_uuid)
            for x in result.combinations[COMBINE_PAGES_UNASSIGNED]
        ]
    )


@pytest.mark.django_db
def test_combine_pages_real_estate_property_success_single_order_pages(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test combine_pages_real_estate_property API,

    One category matches, 'mansion'

    pages should be ordered by semantic_page_page_objects__key__key
    """

    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    # Get page object key keys

    # First order
    photo_building_exterior = PageObjectTitle.objects.get(key="photo_building_exterior")
    # Second order
    photo_outdoor = PageObjectTitle.objects.get(key="photo_outdoor")
    # Third order
    photo_building_visualisation = PageObjectTitle.objects.get(
        key="photo_building_visualisation"
    )
    # Forth order
    photo_building_interior = PageObjectTitle.objects.get(key="photo_building_interior")

    # Create semdoc "first", assigned to "mansion" with page objects on first 6 pages
    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    pages = first.semantic_pages.all()

    for index, page_object_title in enumerate(
        [
            photo_building_visualisation,  # Fourth order
            photo_building_visualisation,  # Fourth order
            photo_building_interior,  # Third order
            photo_building_interior,  # Third order
            photo_outdoor,  # Second order
            photo_outdoor,  # Second order
        ]
    ):
        page_object = pages[index].semantic_page_page_objects.first().page_object
        page_object.key = page_object_title
        page_object.save()

    # Create semdoc "second", assigned to "flat" withouit page objects
    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    # Create semdoc "third", assigned to "mansion" with page objects on first 2 pages
    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    pages = third.semantic_pages.all()

    for index, page_object_title in enumerate(
        [
            photo_building_exterior,  # First order
            photo_building_exterior,  # First order
        ]
    ):
        page_object = pages[index].semantic_page_page_objects.first().page_object
        page_object.key = page_object_title
        page_object.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    first.semantic_pages.count() + third.semantic_pages.count()

    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages-real-estate-property",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps(
            {"document_category_key": photo_category.name, "photos_sorting": True}
        ),
        content_type="application/json",
    )

    assert response.status_code == 201
    result = TypeAdapter(List[schemas.CreatedSemanticDocument]).validate_json(
        response.content
    )
    assert len(result) == 1

    # Now check the ordering, we should have

    expected_ordering = [
        "photo_building_exterior",  # First order
        "photo_building_exterior",  # First order
        "photo_outdoor",  # Second order
        "photo_outdoor",  # Second order
        "photo_building_visualisation",  # Third order
        "photo_building_visualisation",  # Third order
        "photo_building_interior",  # Fourth order
        "photo_building_interior",  # Fourth order
    ]

    semantic_pages = (
        SemanticPage.objects.filter(semantic_document__uuid=result[0].uuid)
        .prefetch_related(
            Prefetch(
                "semantic_page_page_objects",
                queryset=SemanticPagePageObject.objects.select_related("page_object"),
            ),
            "processed_page__processed_file",
        )
        .order_by("number")
    ).annotate(
        keys=ArrayAgg("semantic_page_page_objects__page_object__key__key", default=None)
    )

    # Check for correct ordering
    for index, ordering in enumerate(expected_ordering):
        assert ordering in semantic_pages[index].keys
        # Check that page order (number) is correct (zero index)
        assert semantic_pages[index].number == index

    ordering_set = set(expected_ordering)

    # Check that remaining pages are not in the ordering
    for i in range(len(expected_ordering), len(semantic_pages)):
        assert not ordering_set.intersection(semantic_pages[i].keys)
        assert semantic_pages[i].number == i


@pytest.mark.django_db
def test_check_permitted_combine_pages_real_estate_property_empty_semantic_document(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test permutations permitted for combine_pages_real_estate_properties

    One category matches, 'mansion'

    Test the case where one of the semantic documents has no pages (is empty or deleted).

    It should not be shown for merger
    """
    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_mansion,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    # Initially all semantic documents should be allowed to be merged
    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    mansion_uuids = [x.semantic_document_uuid for x in result.combinations["mansion"]]

    assert first.uuid in mansion_uuids
    assert second.uuid in mansion_uuids
    assert third.uuid in mansion_uuids

    # Hard delete for second semantic document
    semantic_document_helpers.delete_semantic_pages(
        [str(x) for x in second.semantic_pages.all().values_list("uuid", flat=True)],
        True,
    )

    # Soft delete for second semantic document
    semantic_document_helpers.delete_semantic_pages(
        [str(x) for x in third.semantic_pages.all().values_list("uuid", flat=True)],
        False,
    )

    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    assert result.combinations == {}


@pytest.mark.django_db
def test_check_permitted_combine_pages_real_estate_property_three_matching_categories_disable_real_estate(
    testuser1_client: AuthenticatedClient, combine_pages_real_estate_property_data
):
    """
    Test the edge case where we assign real estate properties but disable the real estate boolean flag

    Three categories match, 'mansion', 'flat' and unassigned

    but merge them all together as real estate is disabled

    """
    (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    ) = combine_pages_real_estate_property_data

    first = semantic_documents_with_page_count.first()
    first.document_category = photo_category
    first.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=first,
        realestate_property=property_entity_mansion,
    )

    second = semantic_documents_with_page_count[1]
    second.document_category = photo_category
    second.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=second,
        realestate_property=property_entity_flat,
    )

    third = semantic_documents_with_page_count[2]
    third.document_category = photo_category
    third.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=third,
        realestate_property=property_entity_mansion,
    )

    fourth = semantic_documents_with_page_count[3]
    fourth.document_category = photo_category
    fourth.save()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=fourth,
        realestate_property=property_entity_flat,
    )

    # With no real estate property attached
    fifth = semantic_documents_with_page_count[4]
    fifth.document_category = photo_category
    fifth.save()

    # With no real estate property attached
    sixth = semantic_documents_with_page_count[5]
    sixth.document_category = photo_category
    sixth.save()

    total_page_count = (
        first.semantic_pages.count()
        + third.semantic_pages.count()
        + second.semantic_pages.count()
        + fourth.semantic_pages.count()
        + fifth.semantic_pages.count()
        + sixth.semantic_pages.count()
    )

    # Check with Real estate enabled
    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    # We have two real estate properties, and one without an assignment
    assert len(result.combinations) == 3
    assert "mansion" in result.combinations
    assert "flat" in result.combinations
    assert COMBINE_PAGES_UNASSIGNED in result.combinations

    account = dossier.account

    account.enable_real_estate_properties = False
    account.save()

    # Check with Real estate DISABLED
    response = testuser1_client.get(
        reverse(
            "api:check-permitted-combine-semantic-pages-real-estate-property",
            kwargs={
                "dossier_uuid": dossier.uuid,
                "document_category_key": photo_category.name,
            },
        ),
        content_type="application/json",
    )

    result = schemas.PermittedCombinePagesCombinations.model_validate_json(
        response.content
    )

    # We have two real estate properties, and one without an assignment
    # They should all merge into one as real estate is disabled (ignore assignments)
    combinations = result.combinations
    assert len(combinations) == 1
    assert len(combinations[COMBINE_PAGES_UNASSIGNED]) == 6

    # Attempt merge with real estate disabled
    response = testuser1_client.post(
        reverse(
            "api:combine-semantic-pages-real-estate-property",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=json.dumps(
            {"document_category_key": photo_category.name, "photos_sorting": True}
        ),
        content_type="application/json",
    )

    assert response.status_code == 201
    result = TypeAdapter(List[schemas.CreatedSemanticDocument]).validate_json(
        response.content
    )
    assert len(result) == 1

    assert (
        SemanticPage.objects.filter(semantic_document__uuid=result[0].uuid).count()
        == total_page_count
    )
