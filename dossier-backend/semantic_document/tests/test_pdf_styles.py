"""
Tests for the PDF styles module.
"""

from reportlab.lib.styles import getSampleStyleSheet

from semantic_document.pdf_styles import (
    apply_hypodossier_style_defaults,
    create_test_style,
    create_optimal_style,
    create_comment_style,
    get_base_style,
)


def test_apply_hypodossier_style_defaults_empty():
    """Test that apply_hypodossier_style_defaults sets default values correctly."""
    params = {}
    result = apply_hypodossier_style_defaults(params)

    assert result["wordWrap"] == "CJK"
    assert result["allowOrphans"] == 0
    assert result["allowWidows"] == 0
    assert result["spaceAfter"] == 0
    assert result["spaceBefore"] == 0


def test_apply_hypodossier_style_defaults_with_font_size():
    """Test that apply_hypodossier_style_defaults sets leading based on fontSize."""
    font_size = 12
    params = {"fontSize": font_size}
    result = apply_hypodossier_style_defaults(params)

    assert result["fontSize"] == font_size
    assert result["leading"] == font_size * 1.2


def test_create_test_style():
    """Test that create_test_style creates a style with the correct properties."""
    base_style = getSampleStyleSheet()["Normal"]
    font_size = 14

    style = create_test_style(base_style=base_style, font_size=font_size)

    assert style.name == "test_style"
    assert style.parent == base_style
    assert style.fontSize == font_size
    assert style.leading == font_size * 1.2
    assert style.wordWrap == "CJK"
    assert style.allowOrphans == 0
    assert style.allowWidows == 0
    assert style.spaceAfter == 0
    assert style.spaceBefore == 0


def test_create_optimal_style():
    """Test that create_optimal_style creates a style with the correct properties."""
    base_style = getSampleStyleSheet()["Normal"]
    font_size = 16

    style = create_optimal_style(base_style=base_style, font_size=font_size)

    assert style.name == "optimal_style"
    assert style.parent == base_style
    assert style.fontSize == font_size
    assert style.leading == font_size * 1.2
    assert style.wordWrap == "CJK"
    assert style.allowOrphans == 0
    assert style.allowWidows == 0
    assert style.spaceAfter == 0
    assert style.spaceBefore == 0


def test_create_comment_style():
    """Test that create_comment_style creates a style with the correct properties."""
    style = create_comment_style()

    assert style.name == "comment_style"
    assert style.fontSize == 10
    assert style.leading == 10 * 1.2
    assert style.textColor == (0, 0, 0)
    assert style.fontName == "Helvetica"
    assert style.wordWrap == "CJK"
    assert style.allowOrphans == 0
    assert style.allowWidows == 0
    assert style.spaceAfter == 0
    assert style.spaceBefore == 0


def test_get_base_style():
    """Test that get_base_style returns the Normal style from getSampleStyleSheet."""
    base_style = get_base_style()
    sample_styles = getSampleStyleSheet()

    # Check that it's the same style by comparing name
    assert base_style.name == sample_styles["Normal"].name
    assert base_style.fontName == sample_styles["Normal"].fontName


def test_custom_parameters_preserved():
    """Test that custom parameters are preserved when applying defaults."""
    custom_params = {
        "fontSize": 18,
        "textColor": (1, 0, 0),  # Red
        "fontName": "Times-Roman",
        "wordWrap": "LTR",  # Custom word wrap
    }

    style = create_test_style(**custom_params)

    assert style.fontSize == 18
    assert style.textColor == (1, 0, 0)
    assert style.fontName == "Times-Roman"
    assert style.wordWrap == "LTR"  # Custom value should override default
