from enum import Enum
from typing import Optional, Annotated, Dict, List
from uuid import UUID

from pydantic import (
    BaseModel,
    Field,
    StringConstraints,
    ConfigDict,
    field_validator,
    model_validator,
)
from pydantic_extra_types.color import Color


class UserAnnotationGroupUUIDSchema(BaseModel):
    annotation_group_uuid: UUID


class AnnotationType(str, Enum):
    HIGHLIGHT = "highlight"
    COMMENT = "comment"


class UserAnnotationsSchema(BaseModel):
    annotation_group_uuid: Optional[UUID] = None
    annotation_type: AnnotationType
    text: Optional[str] = None
    bbox_top: float = Field(
        ..., description="Top coordinate of the bounding box", ge=0.0, le=1.0
    )
    bbox_left: float = Field(
        ..., description="Left coordinate of the bounding box", ge=0.0, le=1.0
    )
    bbox_width: float = Field(
        ..., description="Width of the bounding box", ge=0.0, le=1.0
    )
    bbox_height: float = Field(
        ..., description="Height of the bounding box", ge=0.0, le=1.0
    )
    hexcolor: Annotated[str, StringConstraints(pattern=r"^#[0-9A-Fa-f]{6}$")] | None = (
        "#FFFF00"
    )

    @model_validator(mode="after")
    def validate_comment_text(self):
        # Check if this is a comment annotation
        if self.annotation_type == AnnotationType.COMMENT:
            # For comments, text cannot be None or empty string
            if self.text is None or self.text.strip() == "":
                raise ValueError("Comment text cannot be empty")
        return self

    class Config:
        # Configure JSON serialization for Color type
        model_config = ConfigDict(
            json_encoders={Color: str}  # Convert Color to string for JSON serialization
        )


class PageAnnotations(BaseModel):
    annotations: list[UserAnnotationsSchema]


class SemanticPageUserAnnotationsSchema(BaseModel):
    searchable_pdf_url: str
    user_annotations: Dict[str, List[UserAnnotationsSchema]]

    class Config:
        # Configure JSON serialization for Color type
        model_config = ConfigDict(
            json_encoders={Color: str}  # Convert Color to string for JSON serialization
        )


class UpdateUserAnnotationSchema(BaseModel):
    text: Optional[str] = None
    bbox_left: Optional[float] = None
    bbox_top: Optional[float] = None
    bbox_width: Optional[float] = None
    bbox_height: Optional[float] = None

    @field_validator("text")
    def validate_comment_text(cls, v):
        # For comment updates, if text is provided, it cannot be an empty string
        if v is not None and v.strip() == "":
            raise ValueError("Comment text cannot be empty")
        return v

    def validate_bbox_fields(self):
        """Validate that either all bbox fields are provided or none are"""
        bbox_fields = [self.bbox_left, self.bbox_top, self.bbox_width, self.bbox_height]
        has_any_bbox = any(field is not None for field in bbox_fields)
        has_all_bbox = all(field is not None for field in bbox_fields)

        if has_any_bbox and not has_all_bbox:
            return False
        return True
