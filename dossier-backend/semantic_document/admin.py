import structlog
from django.contrib import admin
from django.db.models import Max
from django.forms import ModelForm
from rangefilter.filters import DateRangeFilter

from dossier.admin import AggregatedPageObjects, SemanticPageInline
from dossier.models import RealestateProperty
from semantic_document.models import (
    DocCheckAssignment,
    AssignedRealEstatePropertySemanticDocument,
    SemanticDocument,
    SemanticPageProxy,
    SemanticPageUserAnnotations,
)
from semantic_document.services_admin import (
    admin_reset_restart_semantic_document_export,
    admin_reset_semantic_document_export,
    admin_force_semantic_document_export_done,
)

logger = structlog.get_logger(__name__)


class SemanticPageUserAnnotationsInline(admin.TabularInline):  # or admin.StackedInline
    model = SemanticPageUserAnnotations
    extra = 0  # Number of empty forms to display
    readonly_fields = ["created_at", "updated_at"]
    fields = [
        "annotation_group_uuid",
        "annotation_type",
        "bbox_top",
        "bbox_left",
        "bbox_width",
        "bbox_height",
        "text",
        "hexcolor",
        "created_at",
        "updated_at",
    ]


@admin.register(SemanticPageProxy)
class SemanticPageAdmin(admin.ModelAdmin):
    list_display = [
        "uuid",
        "number",
        "semantic_document",
        "dossier",
        "lang",
        "deleted_at",
        "created_at",
        "updated_at",
    ]
    search_fields = [
        "uuid",
        "semantic_document__uuid",
        "semantic_document__title_suffix",
        "dossier__name",
        "dossier__uuid",
    ]
    list_filter = [
        "dossier__account",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        ("deleted_at", DateRangeFilter),
        "deleted_at",
        "document_category",
    ]
    raw_id_fields = ["dossier", "semantic_document", "processed_page"]
    readonly_fields = ["created_at", "updated_at"]
    ordering = ("-created_at", "number")
    inlines = [SemanticPageUserAnnotationsInline]

    def get_queryset(self, request):
        # Use .all_objects instead of .objects so we can see deleted objects
        qs = self.model.all_objects.get_queryset()
        ordering = self.get_ordering(request)
        if ordering:
            qs = qs.order_by(*ordering)
        return qs


@admin.register(SemanticDocument)
class SemanticDocumentAdmin(admin.ModelAdmin):
    list_display = [
        "uuid",
        "title",
        "title_custom",
        "title_suffix",
        "document_category",
        "confidence_formatted",
        "confidence_level",
        "deleted_at",
        "created_at",
        "updated_at",
        "export_done",
    ]
    ordering = ("-created_at", "uuid")
    inlines = (SemanticPageInline, AggregatedPageObjects)
    raw_id_fields = ["dossier", "document_category"]
    readonly_fields = ["created_at", "updated_at"]
    search_fields = [
        "uuid",
        "external_semantic_document_id",
        "document_category__id",
        "document_category__name",
        "document_category__de",
        "title_custom",
        "title_suffix",
        "dossier__name",
        "dossier__uuid",
    ]
    list_filter = [
        "dossier__account",
        "access_mode",
        "work_status",
        "custom_semantic_document_date",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        ("deleted_at", DateRangeFilter),
        "deleted_at",
        "document_category",
        "dossier__owner",
    ]

    actions = (
        "reset_semantic_document_export",
        "reset_restart_semantic_document_export",
        "perform_force_semantic_document_done",
    )

    def get_queryset(self, request):
        # Use .all_objects instead of .objects so we can see deleted objects
        qs = self.model.all_objects.get_queryset()
        ordering = self.get_ordering(request)
        qs = qs.annotate(latest_export_done=Max("exports__done"))
        if ordering:
            qs = qs.order_by(*ordering)

        return qs

    def export_done(self, obj):
        return obj.latest_export_done

    export_done.admin_order_field = "latest_export_done"
    export_done.short_description = "Export Done"

    @admin.action(
        description="Reset semantic document export (delete export, read-write, inital state)"
    )
    def reset_semantic_document_export(modeladmin, request, queryset):

        # semdocs = [semdoc for semdoc in queryset]

        admin_reset_semantic_document_export(queryset, request)

    @admin.action(
        description="Restart semantic document export (reset, then start again)"
    )
    def reset_restart_semantic_document_export(modeladmin, request, queryset):
        admin_reset_restart_semantic_document_export(request, queryset)

    @admin.action(description="Force semantic document export done status")
    def perform_force_semantic_document_done(modeladmin, request, queryset):
        admin_force_semantic_document_export_done(request, queryset)


@admin.register(DocCheckAssignment)
class DocCheckAssignmentAdmin(admin.ModelAdmin):
    list_display = [
        "case",
        "context_model_uuid",
        "fulfillment_type",
        "completeness_rule",
        "comment",
        "created_at",
        "updated_at",
    ]
    readonly_fields = ["created_at", "updated_at"]


class SemanticDocumentAdminForm(ModelForm):
    # Custom form to reduce load time and restrict to filter by dossier
    class Meta:
        model = AssignedRealEstatePropertySemanticDocument
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk and self.instance.semantic_document_id:
            self.fields["semantic_document"].queryset = SemanticDocument.objects.filter(
                dossier=self.instance.semantic_document.dossier,
            ).select_related(
                "dossier",
                "dossier__account",
                "document_category",
            )

            self.fields["realestate_property"].queryset = (
                RealestateProperty.objects.filter(
                    dossier=self.instance.semantic_document.dossier
                )
            )
        else:
            self.fields["semantic_document"].queryset = (
                SemanticDocument.objects.select_related(
                    "dossier",
                    "dossier__account",
                    "document_category",
                )
            )

        self.fields["semantic_document"].label_from_instance = (
            self.custom_label_from_instance
        )

    def custom_label_from_instance(self, obj):
        return (
            f"Account: {obj.dossier.account.key}, Dossier: {obj.dossier_id}, Cat: {obj.document_category.name},"
            f" UUID: {obj.uuid}"
        )


@admin.register(AssignedRealEstatePropertySemanticDocument)
class AssignedRealEstatePropertySemanticDocumentAdmin(admin.ModelAdmin):
    form = SemanticDocumentAdminForm

    def get_queryset(self, request):
        # Use select_related to optimize the query
        qs = super().get_queryset(request)
        return qs.select_related(
            "semantic_document",
            "realestate_property",
            "semantic_document__dossier",
            "semantic_document__dossier__account",
            "semantic_document__document_category",
        )

    list_display = (
        "semantic_document",
        "get_realestate_key",
        "get_realestate_title",
        "get_account_key",
        "realestate_property",
    )
    search_fields = (
        "realestate_property__key",
        "realestate_property__title",
        "semantic_document__dossier__account__key",
        "semantic_document__uuid",
    )

    raw_id_fields = ["semantic_document", "realestate_property"]

    def get_realestate_key(self, obj):
        return obj.realestate_property.key

    get_realestate_key.short_description = "Real Estate Key"

    def get_realestate_title(self, obj):
        return obj.realestate_property.title

    def get_account_key(self, obj):
        return obj.semantic_document.dossier.account.key

    get_account_key.short_description = "Account Key"

    get_realestate_title.short_description = "Real Estate Title"
    list_filter = (
        "semantic_document__dossier__account",
        "created_at",
        "updated_at",
    )
    ordering = ("-created_at",)


@admin.register(SemanticPageUserAnnotations)
class SemanticPageUserAnnotationsAdmin(admin.ModelAdmin):
    list_display = [
        "get_dossier",
        "get_semantic_page_document_category",
        "annotation_type",
        "annotation_group_uuid",
        "text",
        "bbox_top",
        "bbox_left",
        "bbox_width",
        "bbox_height",
        "hexcolor",
        "created_at",
        "updated_at",
    ]

    search_fields = ["semantic_page__dossier__name", "text"]
    list_filter = [
        "annotation_type",
        "created_at",
        "updated_at",
    ]

    def get_dossier(self, obj):
        return obj.semantic_page.dossier

    get_dossier.short_description = "Dossier"
    get_dossier.admin_order_field = "semantic_page__dossier"

    def get_semantic_page_document_category(self, obj):
        return obj.semantic_page.document_category

    get_semantic_page_document_category.short_description = "Page Document Category"
