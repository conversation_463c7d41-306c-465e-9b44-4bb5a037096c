from datetime import datetime
from typing import List
from uuid import UUID

from pydantic import BaseModel, HttpUrl


class PageStreamPage(BaseModel):
    uuid: UUID
    number: int


class PageStreamForSuggestions(BaseModel):
    index: int
    pages: List[PageStreamPage]
    deleted: bool


class PageStreamsCategorySuggestionsRequest(BaseModel):
    page_streams: List[PageStreamForSuggestions]
    semantic_document_uuid: UUID


class PageStreamCategorySuggestions(BaseModel):
    stream_index: int
    suggested_document_categories: List[str]


class PageStreamPageForSplit(BaseModel):
    uuid: UUID
    updated_at: datetime


class PageStreamForSplit(BaseModel):
    index: int
    pages: List[PageStreamPageForSplit]
    deleted: bool
    document_category: str


class DocumentSplitRequest(BaseModel):
    page_streams: List[PageStreamForSplit]


class DocumentSplitResponse(BaseModel):
    detail: str


class CutDecision(BaseModel):
    pagestreampage_id: str
    page_number: int  # as always 0-indexed
    decision: float  # =< 0.5 no cut, > 0.5 cut


class PageStreamPrediction(BaseModel):
    cut_decisions: list[CutDecision]
    duration: float
    model: str


class PageStreamPageV2(BaseModel):
    id: str
    src_url: HttpUrl
    number: int


class PSSRequestV2(BaseModel):
    request_id: UUID
    pages: list[PageStreamPageV2]


class PSSResponseV1(BaseModel):
    page_stream_prediction: PageStreamPrediction
