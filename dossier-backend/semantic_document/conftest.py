import pytest
from django.db.models import Count

from dossier.models import Document<PERSON><PERSON><PERSON><PERSON>, Dossier, RealestateProperty
from semantic_document.models import SemanticDocument


@pytest.fixture()
def combine_pages_real_estate_property_data():
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    dossier.account.enable_real_estate_properties = True
    dossier.account.save()

    photo_category = DocumentCategory.objects.get(
        id=615, account=dossier.account, name="PROPERTY_PHOTOS"
    )

    # Delete any pre-existing semantic documents in photo category
    SemanticDocument.objects.filter(
        dossier=dossier, document_category=photo_category
    ).delete()

    initial_semantic_documents = SemanticDocument.objects.filter(dossier=dossier).all()

    semantic_documents_with_page_count = (
        initial_semantic_documents.annotate(page_count=Count("semantic_pages"))
        .order_by("-page_count")
        .filter(dossier=dossier, semantic_pages__deleted_at=None)
    )

    property_entity_mansion = RealestateProperty.objects.create(
        dossier=dossier, key="mansion"
    )
    property_entity_flat = RealestateProperty.objects.create(
        dossier=dossier, key="flat"
    )

    return (
        semantic_documents_with_page_count,
        property_entity_mansion,
        property_entity_flat,
        photo_category,
        dossier,
    )


def assert_page_objects_exist(semantic_document: SemanticDocument) -> int:
    # Test that all semantic pages have page objects
    # and return total count of semantic page objects
    semantic_page_objects_count = 0
    for page in semantic_document.semantic_pages.all():
        count = page.semantic_page_page_objects.count()
        assert count > 0
        semantic_page_objects_count += count

    return semantic_page_objects_count
