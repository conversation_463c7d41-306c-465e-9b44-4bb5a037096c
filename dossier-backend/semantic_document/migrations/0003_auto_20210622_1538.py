# Generated by Django 3.2.3 on 2021-06-22 13:38

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('semantic_document', '0002_rename_source_page_number_semanticpage_page_number'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='semanticpage',
            name='page_number',
        ),
        migrations.AddField(
            model_name='semanticpage',
            name='number',
            field=models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)]),
            preserve_default=False,
        ),
    ]
