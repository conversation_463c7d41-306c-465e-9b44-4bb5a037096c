# Generated by Django 3.2.19 on 2023-05-18 16:22

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('doccheck', '0001_initial'),
        ('semantic_document', '0012_alter_semanticpage_document_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocCheckAssignment',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('context_model_uuid', models.UUIDField()),
                ('comment', models.TextField(blank=True, null=True)),
                ('assigned_documents', models.ManyToManyField(to='semantic_document.SemanticDocument')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.case')),
                ('completeness_rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.completenessrule')),
            ],
            options={
                'unique_together': {('case', 'context_model_uuid', 'completeness_rule')},
            },
        ),
    ]
