# Generated by Django 3.2.3 on 2021-06-22 12:23

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('processed_file', '0001_initial'),
        ('dossier', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SemanticDocument',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('confidence_level', models.CharField(choices=[('low', 'low'), ('medium', 'medium'), ('high', 'high')], max_length=16)),
                ('confidence_formatted', models.CharField(blank=True, max_length=16, null=True)),
                ('confidence_value', models.FloatField()),
                ('title', models.CharField(max_length=255)),
                ('filename', models.CharField(max_length=255)),
                ('title_elements', models.JSONField()),
                ('document_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.documentcategory')),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossier')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SemanticPage',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('lang', models.CharField(choices=[('DE', 'German'), ('EN', 'English'), ('FR', 'French'), ('IT', 'Italian')], default='DE', max_length=2)),
                ('source_page_number', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)])),
                ('confidence_value', models.FloatField()),
                ('confidence_formatted', models.CharField(blank=True, max_length=16, null=True)),
                ('confidence_level', models.CharField(choices=[('low', 'low'), ('medium', 'medium'), ('high', 'high')], max_length=16)),
                ('document_category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='semantic_pages', to='dossier.documentcategory')),
                ('dossier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='semantic_pages', to='dossier.dossier')),
                ('page_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='semantic_pages', to='dossier.pagecategory')),
                ('processed_page', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='semantic_pages', to='processed_file.processedpage')),
                ('semantic_document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='semantic_pages', to='semantic_document.semanticdocument')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SemanticPagePageObject',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('page_object', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='processed_file.pageobject')),
                ('semantic_page', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='semantic_page_page_objects', to='semantic_document.semanticpage')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SemanticDocumentPageObject',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('page_object', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='processed_file.pageobject')),
                ('semantic_document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='aggregated_page_objects', to='semantic_document.semanticdocument')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
