from typing import List, Dict, Any
from django.core.exceptions import ValidationError
from semantic_document.models import SemanticDocument, SemanticPage


def gather_move_pages_diagnostic_info(
    semantic_document: SemanticDocument,
    source_pages: List[SemanticPage],
    list_of_semantic_page_uuid: List[str],
    move: bool,
) -> Dict[str, Any]:
    """
    Gather diagnostic information about the page move/copy operation.
    This information is collected at the start of the operation to help with debugging.
    """
    return {
        "operation": "move" if move else "copy",
        "target_semantic_document": {
            "uuid": str(semantic_document.uuid),
            "title": semantic_document.title,
            "dossier_uuid": str(semantic_document.dossier.uuid),
            "account": {
                "uuid": str(semantic_document.dossier.account.uuid),
                "key": semantic_document.dossier.account.key,
            },
        },
        "source_pages": [
            {
                "uuid": str(page.uuid),
                "semantic_document_uuid": str(page.semantic_document.uuid),
                "number": page.number,
                "deleted_at": page.deleted_at.isoformat() if page.deleted_at else None,
            }
            for page in source_pages
        ],
        "page_counts": {
            "initial": SemanticPage.all_objects.filter(
                dossier=semantic_document.dossier
            ).count(),
            "pages_to_process": len(list_of_semantic_page_uuid),
        },
    }


def validate_source_pages_belong_to_same_dossier(
    source_pages: List[SemanticPage],
    target_semantic_document: SemanticDocument,
) -> None:
    """
    Verify that all source pages belong to the same dossier as the target semantic document.
    Raises ValidationError if any page belongs to a different dossier.
    """
    if source_pages.exists() and not all(
        page.dossier == target_semantic_document.dossier for page in source_pages
    ):
        raise ValidationError(
            "All semantic pages must belong to the same dossier as the target semantic document"
        )


def validate_page_count_after_movecopy_pages_operation(
    semantic_document: SemanticDocument,
    initial_page_count: int,
    list_of_semantic_page_uuid: List[str],
    move: bool,
    diagnostic_info: Dict[str, Any],
) -> None:
    """
    Verify that the final page count matches our expectations after the operation.
    For move operations, the count should remain the same.
    For copy operations, the count should increase by the number of pages being copied.
    """
    final_page_count = SemanticPage.all_objects.filter(
        dossier=semantic_document.dossier
    ).count()

    if move:
        if final_page_count != initial_page_count:
            raise ValidationError(
                f"Page count mismatch after move operation. Expected {initial_page_count}, got {final_page_count}. "
                f"Diagnostic info: {diagnostic_info}"
            )
    else:
        expected_count = initial_page_count + len(list_of_semantic_page_uuid)
        if final_page_count != expected_count:
            raise ValidationError(
                f"Page count mismatch after copy operation. Expected {expected_count}, got {final_page_count}. "
                f"Diagnostic info: {diagnostic_info}"
            )
