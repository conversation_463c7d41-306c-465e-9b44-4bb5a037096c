import djclick as click


import random
from typing import List, Tuple
from uuid import UUID
import logging

from django.contrib.auth import get_user_model
from django.db import transaction

from dossier.fakes import (
    create_fake_semantic_document_custom_page_creator,
    create_processed_page_letter,
)
from dossier.helpers_timezone import create_local_datetime
from dossier.models import <PERSON><PERSON><PERSON>, <PERSON>ssier<PERSON><PERSON>, <PERSON>ssier, PageCategory, DocumentCategory
from dossier.services import create_dossier
from semantic_document.models import SemanticDocument

logger = logging.getLogger(__name__)

User = get_user_model()


@click.command()
@click.argument("account_key")
@click.option(
    "--correct-count",
    default=0,
    help="Number of documents with correct page numbering",
    type=int,
)
@click.option(
    "--non-zero-count",
    default=0,
    help="Number of documents with non-zero start",
    type=int,
)
@click.option(
    "--non-consecutive-count",
    default=0,
    help="Number of documents with non consecutive pages",
    type=int,
)
@click.option(
    "--min-pages",
    default=10,
    help="Minimum pages per document",
    type=int,
)
@click.option(
    "--max-pages",
    default=10,
    help="Maximum pages per document",
    type=int,
)
def create_test_documents_for_fix_semantic_pages_non_zero_index(
    account_key: str,
    correct_count: int,
    non_zero_count: int,
    non_consecutive_count: int,
    min_pages: int,
    max_pages: int,
) -> None:
    """
    Create synthetic documents with various page numbering patterns for testing.

    Example usage:

    python manage.py create_test_documents_for_fix_semantic_pages_non_zero_index default --correct-count 2 --non-zero-count 2 --non-consecutive-count 2

    # Non zero start only
    python manage.py create_test_documents_for_fix_semantic_pages_non_zero_index default --non-zero-count 1

    # Non consecutive start only
    python manage.py create_test_documents_for_fix_semantic_pages_non_zero_index default --non-consecutive-count 1
    """
    try:
        account = Account.objects.get(key=account_key)
    except Account.DoesNotExist:
        logger.error(f"Account '{account_key}' not found")
        return

    num_documents = correct_count + non_zero_count + non_consecutive_count

    logger.info(f"Creating {num_documents} test documents for account '{account_key}'")

    created_docs: List[Tuple[UUID, str]] = []

    dossier_user = DossierUser.objects.filter(account=account).first()

    with transaction.atomic():
        # Create a test dossier
        test_dossier: Dossier = create_dossier(
            account=account,
            language="en",
            owner=dossier_user.user,
            dossier_name=f"Test Documents {create_local_datetime().strftime('%Y-%m-%d %H:%M:%S')}",
            # Generates random external ID in format: BankNR.KundenNR.RahmenNR.AntragsNR (e.g. 949.401678.001.110794)
            external_id=f"{random.randint(100,999)}.{random.randint(100000,999999)}.{str(random.randint(1,999)).zfill(3)}.{random.randint(100000,999999)}",
        )

        # Create documents with correct numbering
        for _ in range(correct_count):
            doc = _create_document_with_pattern(
                test_dossier, "correct", min_pages, max_pages
            )
            created_docs.append((doc.uuid, "correct"))

        # Create documents with non-zero start
        for _ in range(non_zero_count):
            doc = _create_document_with_pattern(
                test_dossier, "non_zero", min_pages, max_pages
            )
            created_docs.append((doc.uuid, "non_zero"))

        # Create documents with non consecutive pages
        for _ in range(non_consecutive_count):
            doc = _create_document_with_pattern(
                test_dossier, "gap", min_pages, max_pages
            )
            created_docs.append((doc.uuid, "gap"))

    # Log summary
    logger.info(f"Created test dossier with UUID: {test_dossier.uuid}")
    logger.info("Created documents:")
    logger.info(f"- Correct numbering: {correct_count}")
    logger.info(f"- Non-zero start: {non_zero_count}")
    logger.info(f"- Non consecutive: {non_consecutive_count}")

    # Log individual documents
    for doc_uuid, pattern in created_docs:
        logger.info(f"Created document {doc_uuid} with pattern: {pattern}")


def _create_document_with_pattern(
    dossier: Dossier,
    pattern: str,
    min_pages: int,
    max_pages: int,
) -> SemanticDocument:
    """
    Create a document with a specific page numbering pattern.

    Args:
        dossier: The dossier to create the document in
        pattern: Type of page numbering pattern ('correct', 'non_zero', 'gap', 'duplicate')
        min_pages: Minimum number of pages
        max_pages: Maximum number of pages

    Returns:
        SemanticDocument with the specified page numbering pattern
    """
    num_pages = random.randint(min_pages, max_pages)

    # Create document with required number of pages

    document_category = DocumentCategory.objects.get(
        account=dossier.account, name="PROPERTY_PHOTOS"
    )

    semantic_document = create_fake_semantic_document_custom_page_creator(
        confidence=0.95,
        doc_cat=document_category,
        dossier=dossier,
        generic_page_cat=PageCategory.objects.get(id=1),
        processed_page_creator=create_processed_page_letter,  # Pass the function
        title_suffix="Test Document",
        allow_empty_docs=False,
        max_pages=num_pages,
        min_num_pages=num_pages,
        no_page_objects_per_page=1,
    )

    semantic_document.title_suffix = pattern
    semantic_document.save()

    # Get existing pages ordered by number
    pages = semantic_document.semantic_pages.all().order_by("number")

    if pattern == "correct":
        # Already correct (0-based), no modification needed
        return semantic_document

    elif pattern == "non_zero":
        # Shift all page numbers up by a random start number
        # start = random.randint(1, 1)
        start = 1
        for i, page in enumerate(pages):
            page.number = start + i
            page.save()

    # If we need to test fixing other edge cases
    elif pattern == "gap":
        # Create gaps by multiplying current number by 2
        for page in pages:
            page.number = page.number * 2
            page.save()

    elif pattern == "duplicate":
        if len(pages) >= 2:
            # Make the last page number same as second-to-last
            last_page = pages.last()
            second_to_last_number = pages[len(pages) - 2].number
            last_page.number = second_to_last_number
            last_page.save()

    else:
        raise ValueError(f"Unknown pattern: {pattern}")

    return semantic_document
