from typing import List, Optional

import structlog
from ninja.security import <PERSON>tt<PERSON><PERSON><PERSON><PERSON>
from pydantic import (
    BaseModel,
    ValidationError as PydanticValidationError,
    model_validator,
)

from dossier.exceptions import HttpError
from projectconfig.authentication import Auth, JWTAuthRequest

logger = structlog.get_logger(__name__)


class MultiTenancyKeycloakJwtSchema(BaseModel):
    account_keys: Optional[List[str]] = None
    account_key: Optional[str] = None

    @model_validator(mode="after")
    def check_account_access(self) -> "MultiTenancyKeycloakJwtSchema":
        """Validate that either account_keys or account_key is present."""
        if self.account_keys is None and self.account_key is None:
            raise ValueError("Either account_keys or account_key must be present")
        return self


class MultiTenancyJWTAuth(HttpBearer):
    """Authentication class for multi-tenancy JWT tokens.

    Validates JWT tokens against Keycloak public key and ensures they conform
    to the MultiTenancyKeycloakJwtSchema structure.
    """

    def authenticate(
        self, request: JWTAuthRequest, token: str, *args, **kwargs
    ) -> bool:
        """Authenticate the request using JWT token.

        Args:
            request: The incoming request
            token: JWT token string

        Returns:
            bool: True if authentication successful, False otherwise
        """
        if not token:
            logger.warning("Token not provided")
            return False

        try:
            auth = Auth(token)
            MultiTenancyKeycloakJwtSchema.model_validate(auth.decoded_payload)
            request.jwt = auth.decoded_payload
            return True

        except HttpError as e:
            logger.warning("JWT validation failed", error=str(e))
            return False

        except PydanticValidationError as e:
            logger.warning("JWT payload validation failed", error=str(e))
            return False

        except Exception as e:
            logger.error("Unexpected error during authentication", error=str(e))
            return False
