import json

import jwt
import pytest
from django.conf import settings
from django.urls import reverse
from jwt import PyJWK

from core.authentication import create_token
from projectconfig.jwk import extract_public_jwk

pytestmark = pytest.mark.django_db

instance_jwk_dict = json.loads(settings.INSTANCE_SIGNING_KEY)


@pytest.fixture
def instance_jwk_public():
    return PyJWK(extract_public_jwk(instance_jwk_dict))


@pytest.fixture
def instance_jwk_private():
    return PyJWK(instance_jwk_dict)


@pytest.fixture
def multi_account_token():
    """Create a token with access to multiple accounts"""
    return create_token(
        "Test",
        "User",
        "<EMAIL>",
        extra_fields={"account_keys": ["default", "account2"]},
        signed_with_keycloak_key=True,
    )


@pytest.fixture
def single_account_token():
    """Create a token with access to a single account"""
    return create_token(
        "Test",
        "User",
        "<EMAIL>",
        account_key="default",
        signed_with_keycloak_key=True,
    )


@pytest.fixture
def dual_access_token():
    """Create a token with both account_key and account_keys"""
    return create_token(
        "Test",
        "User",
        "<EMAIL>",
        account_key="default",
        extra_fields={"account_keys": ["account2", "nonexistent"]},
        signed_with_keycloak_key=True,
    )


def test_exchange_token_success_with_account_keys(
    client, multi_account_token, instance_jwk_public
):
    """Test successful token exchange with account_keys in original token"""
    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "default"},
        ),
        HTTP_AUTHORIZATION=f"Bearer {multi_account_token}",
    )

    assert response.status_code == 200
    data = response.json()
    assert "token" in data

    # Verify the new token contains correct claims
    new_token_payload = jwt.decode(
        data.get("token"),
        instance_jwk_public,
        audience="account",
    )

    assert new_token_payload["iss"] == "default"
    assert new_token_payload["account_key"] == "default"
    assert "account_keys" not in new_token_payload
    assert "kid" in new_token_payload


def test_exchange_token_success_with_account_key(client, single_account_token):
    """Test successful token exchange with account_key in original token"""
    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "default"},
        ),
        HTTP_AUTHORIZATION=f"Bearer {single_account_token}",
    )

    assert response.status_code == 200
    assert "token" in response.json()


def test_exchange_token_dual_token_priority(client, dual_access_token):
    """Test token exchange with both account_key and account_keys"""
    # jwt.account_key will be ignored if account_keys is present
    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "default"},
        ),
        HTTP_AUTHORIZATION=f"Bearer {dual_access_token}",
    )
    assert response.status_code == 404

    # Try accessing account from account_keys
    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "account2"},
        ),
        HTTP_AUTHORIZATION=f"Bearer {dual_access_token}",
    )
    assert response.status_code == 200


def test_exchange_token_unauthorized(client):
    """Test token exchange with invalid token"""
    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "default"},
        ),
        HTTP_AUTHORIZATION="Bearer invalid_token",
    )

    assert response.status_code == 401


def test_exchange_token_account_account_access_denied(client, single_account_token):
    """Test token exchange with non-existent account"""
    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "account2"},
        ),
        HTTP_AUTHORIZATION=f"Bearer {single_account_token}",
    )

    assert response.status_code == 404


def test_exchange_token_account_not_found(client, multi_account_token):
    """Test token exchange with unauthorized account access"""
    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "nonexistent"},
        ),
        HTTP_AUTHORIZATION=f"Bearer {multi_account_token}",
    )

    assert response.status_code == 404


def test_exchange_token_preserves_original_claims(client, instance_jwk_public):
    """Test that the new token preserves original claims from input token"""
    original_token = create_token(
        "Test",
        "User",
        "<EMAIL>",
        account_key="default",
        extra_fields={
            "custom_claim": "custom_value",
            "roles": ["role1", "role2"],
        },
        signed_with_keycloak_key=True,
    )

    response = client.post(
        reverse(
            "api:exchange-keycloak-with-instance-token",
            kwargs={"account_key": "default"},
        ),
        HTTP_AUTHORIZATION=f"Bearer {original_token}",
    )

    assert response.status_code == 200
    data = response.json()

    new_token_payload = jwt.decode(
        data.get("token"),
        instance_jwk_public,
        audience="account",
    )

    assert new_token_payload["custom_claim"] == "custom_value"
    assert new_token_payload["roles"] == ["role1", "role2"]
