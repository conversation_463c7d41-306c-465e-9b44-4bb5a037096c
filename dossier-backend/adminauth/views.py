# Create your views here.
from django.conf import settings
from django.contrib import auth
from django.http import HttpResponseRedirect
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from urllib.parse import urljoin


@require_http_methods(["POST"])
@csrf_protect
def keycloak_logout(request):
    """
    Perform the logout of the app and redirect to keycloak.
    Only accepts POST requests and requires CSRF token.

    Used by django admin.
    """
    if request.user.is_authenticated:
        auth.logout(request)

    return HttpResponseRedirect(
        urljoin(
            settings.ADMIN_KEYCLOAK_REALM_ENDPOINT, "protocol/openid-connect/logout"
        )
        + f"?client_id={settings.ADMIN_KEYCLOAK_CLIENT_ID}"
        + "&post_logout_redirect_uri="
        + request.build_absolute_uri("/admin")
    )
