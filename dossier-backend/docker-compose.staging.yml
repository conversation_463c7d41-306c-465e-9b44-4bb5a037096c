version: '3.8'
services:
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py dossier_event_consumer_v2
    environment:
      - DOSSIER_EVENTS_WORKER_QUEUE_NAME=DossierEvents.staging.DossierEventV1
      - DOSSIER_EVENT_CONSUMER_PREFETCH=2
      - DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD=6
      - DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE=40
    secrets:
      - source: ENV_V27
        target: /app/.env

  diew:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py image_exporter_worker
    secrets:
      - source: ENV_V27
        target: /app/.env

  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    networks:
      caddy:
    environment:
      - DOSSIER_EVENTS_WORKER_QUEUE_NAME=DossierEvents.staging.DossierEventV1
    # this part is needed to register the service at the reverse proxy
    labels:
      caddy: dms.hypo-staging-du.duckdns.org
      caddy.reverse_proxy: "{{upstreams 8000}}"
      caddy.import: tls
    secrets:
      - source: ENV_V27
        target: /app/.env
    healthcheck:
      test: curl --fail http://localhost:8000/api/docs || exit 1
      start_period: 30s
      interval: 5s
      timeout: 5s
      retries: 20

  hystatic:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: sanic hystatic.server:app -p 8000 -H 0.0.0.0 --access-log
    networks:
      caddy:
    labels:
      caddy: "*.dmf.hypo-staging-du.duckdns.org"
      caddy.reverse_proxy: "{{upstreams 8000}}"
      caddy.import: tls
    secrets:
      - source: ENV_V27
        target: /app/.env

  worker:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py worker
    secrets:
      - source: ENV_V27
        target: /app/.env

secrets:
  ENV_V27:
    file: .env-staging

networks:
  default:
  caddy:
    external: true