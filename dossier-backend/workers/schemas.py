from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, AnyHttpUrl

from clientis.schemas.schemas import ClientisExportMetadata
from dossier import schemas as dossier_schemas
from dossier.models import SemanticDocumentExportStrategy


# Really long names, but I prefer to be explicit (KISS principle)
class SemanticDocumentPDFResponseV1(BaseModel):
    semantic_document_pdf_request_uuid: UUID


class SemanticDocumentPDFRequestV1(BaseModel):
    semantic_document_pdf_request_uuid: UUID  # Needed
    # Needed to fetch the correct semantic document from within the semantic dossier
    semantic_document_uuid: UUID
    semantic_dossier: dossier_schemas.SemanticDossier

    put_upload_url: str

    add_metadata: Optional[bool] = True
    # From account.document_download_ui_add_uuid_suffix
    # Added a UUID suffix to the pdf file name
    add_uuid_suffix: Optional[bool] = False

    enable_pdfa_conversion_for_export: Optional[bool] = False

    # Export Strategy e.g. whether to include the metadata in a zip file with the pdf

    semantic_document_export_strategy: Optional[str] = (
        SemanticDocumentExportStrategy.DEFAULT.value
    )

    # metadata to include in a zip file with the pdf, based off the semantic_document_export_strategy
    metadata: Optional[ClientisExportMetadata] = None


class ProcessingResult(BaseModel):
    file_url: AnyHttpUrl
    processing_timestamp: datetime


class FREPPDFAConversionRequest(BaseModel):
    correlation_uuid: UUID
    dossier_uuid: Optional[UUID] = None
    # We use AnyHttpUrl instead of HttpUrl as CI uses ports as part of the URL
    source_file_url: AnyHttpUrl


class FREPPDFAConversionResponse(BaseModel):
    correlation_uuid: UUID
    data: Optional[ProcessingResult] = None
    error: Optional[str] = None
