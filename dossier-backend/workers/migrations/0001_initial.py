# Generated by Django 3.2.21 on 2023-09-25 17:10

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dossier', '0063_auto_20230921_1301'),
        ('semantic_document', '0016_auto_20230911_2130'),
    ]

    operations = [
        migrations.CreateModel(
            name='SemanticDocumentExport',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('done', models.DateTimeField(blank=True, null=True)),
                ('file', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dossier.dossierfile')),
                ('semantic_document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exports', to='semantic_document.semanticdocument')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
