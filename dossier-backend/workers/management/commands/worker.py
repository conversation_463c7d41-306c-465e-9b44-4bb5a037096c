import asyncio
import platform
from asyncio import Abstract<PERSON><PERSON><PERSON><PERSON>
from time import time

import aio_pika
import djclick as click
import structlog
from actorizer import Broker as PersistentBrokerConnection
from aio_pika.abc import AbstractRobustConnection
from aio_pika.patterns.rpc import CallbackType, RPC
from django.conf import settings
from structlog.contextvars import bound_contextvars

from dossier.helpers_timezone import log_timezone_info
from dossier_zipper.workers import async_process_dossier_zip_request
from projectconfig.settings import (
    ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY,
    ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY,
)
from projectconfig.settings import RABBIT_URL
from workers.schemas import SemanticDocumentPDFRequestV1
from workers.workers import (
    async_process_semantic_dossier_pdf_request,
)


logger = structlog.getLogger(__name__)


async def fabric_start_server(
    rpc_callback_methods: list[tuple[str, CallbackType]],
    loop: asyncio.AbstractEventLoop = None,
    client_properties=None,
    auto_delete: bool = False,
):
    """

    @param rpc_callback_methods: e.g. [("dossier_zipper", async_process_dossier_zip_request)]
    @param loop:
    @param client_properties:
    @param auto_delete:
    @return:
    """
    if client_properties is None:
        client_properties = {}

    connection = await aio_pika.connect_robust(
        f"{RABBIT_URL}?name=management fabric start server {platform.node()}",
        client_properties=client_properties,
        loop=loop,
    )

    channel = await connection.channel()
    rpc = await RPC.create(channel)

    for rpc_method_name, rpc_callback in rpc_callback_methods:
        await rpc.register(rpc_method_name, rpc_callback, auto_delete=auto_delete)

    return connection


async def start_server(loop: AbstractEventLoop) -> AbstractRobustConnection:
    client_properties = {"connection_name": f"dossier_workers_{platform.node()}"}

    return await fabric_start_server(
        rpc_callback_methods=[
            # We can't remove ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY as we use a sync rpc call in an internal dossier API
            (
                ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY,
                async_process_dossier_zip_request,
            ),
            # (
            #     ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
            #     process_semantic_dossier_pdf_request,
            # ),
        ],
        loop=loop,
        client_properties=client_properties,
        auto_delete=False,
    )


broker_connection = PersistentBrokerConnection(
    url=f"{settings.RABBIT_URL}?name=management worker {platform.node()}",
)


async def process_semantic_dossier_pdf_request_actor(message: aio_pika.IncomingMessage):
    async with message.process(reject_on_redelivered=True, requeue=True):
        start = time()
        with bound_contextvars(message_type=message.type):
            # Create a PDF, which is not PDFA compliant
            # For some customers, this is an intermediate step
            # as they require a PDFA compliant PDF for archiving
            request = SemanticDocumentPDFRequestV1.model_validate_json(message.body)
            response_json = await async_process_semantic_dossier_pdf_request(
                semantic_document_pdf_request=request
            )

            await broker_connection.publish(
                routing_key=settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME,
                message=aio_pika.Message(
                    body=response_json.encode(),
                    content_type="application/json",
                    type=settings.ASYNC_DOSSIER_CONSUMER_SEMANTIC_DOCUMENT_EVENT_MESSAGE_TYPE,
                ),
            )

            await logger.ainfo(
                "processed process_semantic_dossier_pdf_request",
                duration=time() - start,
            )


async def process_dossier_zip_request_actor(message: aio_pika.IncomingMessage):
    async with message.process(reject_on_redelivered=True, requeue=True):
        start = time()
        with bound_contextvars(message_type=message.type):
            response_json = await async_process_dossier_zip_request(
                dossier_zip_request=message.body
            )

            await broker_connection.publish(
                routing_key=settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME,
                message=aio_pika.Message(
                    body=response_json.encode(),
                    content_type="application/json",
                    type=settings.ASYNC_DOSSIER_CONSUMER_DOSSIER_ZIPPER_EVENT_MESSAGE_TYPE,
                ),
            )

            await logger.ainfo(
                "processed process_dossier_zip_request_actor",
                duration=time() - start,
            )


@click.command()
def start():
    logger.info("starting the worker")
    asyncio.run(main())


async def main():

    # Register process_dossier_zip_request_actor
    await broker_connection.register(
        f=process_dossier_zip_request_actor,
        queue_name=ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY,
    )

    await broker_connection.register(
        f=process_semantic_dossier_pdf_request_actor,
        queue_name=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    )

    await broker_connection.start()

    try:
        await asyncio.gather(start_server(loop=asyncio.get_event_loop()))

        log_timezone_info(logger)

        # Let the code run forever
        logger.info("consuming...")
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        # If user presses Ctrl+C stop the broker connection
        await broker_connection.stop()
