# Generated by Django 3.2.16 on 2023-01-27 13:32

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessCaseType',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=255)),
                ('name_de', models.CharField(blank=True, max_length=255, null=True)),
                ('description_de', models.TextField(blank=True, null=True)),
                ('name_en', models.CharField(blank=True, max_length=255, null=True)),
                ('description_en', models.TextField(blank=True, null=True)),
                ('name_fr', models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('description_fr', models.TextField(blank=True, null=True)),
                ('name_it', models.CharField(blank=True, max_length=255, null=True)),
                ('description_it', models.TextField(blank=True, null=True)),
                ('order', models.PositiveIntegerField(default=0, editable=False)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Case',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_case_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.businesscasetype')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CompletenessRule',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=255)),
                ('title_de', models.CharField(blank=True, max_length=255, null=True)),
                ('title_en', models.CharField(blank=True, max_length=255, null=True)),
                ('title_fr', models.CharField(blank=True, max_length=255, null=True)),
                ('title_it', models.CharField(blank=True, max_length=255, null=True)),
                ('desc_de', models.TextField(blank=True, null=True)),
                ('desc_en', models.TextField(blank=True, null=True)),
                ('desc_fr', models.TextField(blank=True, null=True)),
                ('desc_it', models.TextField(blank=True, null=True)),
                ('entity', models.IntegerField(choices=[(1, 'Case'), (2, 'Person'), (3, 'Realestateproperty')])),
                ('field', models.CharField(blank=True, max_length=50, null=True)),
                ('values', models.CharField(blank=True, max_length=255, null=True)),
                ('strictness', models.CharField(choices=[('required', 'required'), ('optional', 'optional')], max_length=20)),
                ('order', models.PositiveIntegerField(default=0, editable=False)),
                ('business_case_types', models.ManyToManyField(to='doccheck.BusinessCaseType')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='DocCheck',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DocumentCategory',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('doc_check', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.doccheck')),
            ],
            options={
                'unique_together': {('doc_check', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Field',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('entity', models.IntegerField(choices=[(1, 'Case'), (2, 'Person'), (3, 'Realestateproperty')])),
                ('key', models.CharField(max_length=50)),
                ('name_de', models.CharField(blank=True, max_length=255, null=True)),
                ('name_en', models.CharField(blank=True, max_length=255, null=True)),
                ('name_fr', models.CharField(blank=True, max_length=255, null=True)),
                ('name_it', models.CharField(blank=True, max_length=255, null=True)),
                ('order', models.PositiveIntegerField(default=0, editable=False)),
                ('type', models.IntegerField(choices=[(1, 'Choice'), (2, 'Boolean')])),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='FieldChoice',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=50)),
                ('name_de', models.CharField(blank=True, max_length=255, null=True)),
                ('name_en', models.CharField(blank=True, max_length=255, null=True)),
                ('name_fr', models.CharField(blank=True, max_length=255, null=True)),
                ('name_it', models.CharField(blank=True, max_length=255, null=True)),
                ('order', models.PositiveIntegerField(default=0, editable=False)),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.field')),
            ],
            options={
                'ordering': ['order'],
                'unique_together': {('field', 'key')},
            },
        ),
        migrations.CreateModel(
            name='RealEstateProperty',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('for_rent', models.BooleanField(default=False)),
                ('purchase_year_lt_2y', models.BooleanField(default=False)),
                ('has_use_restrictions', models.BooleanField(default=False)),
                ('has_reconstructions', models.BooleanField(default=False)),
                ('has_construction_contract', models.BooleanField(default=False)),
                ('address_canton', models.ForeignKey(limit_choices_to={'field__key': 'address_canton'}, on_delete=django.db.models.deletion.CASCADE, related_name='canton_field_choice', to='doccheck.fieldchoice')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.case')),
                ('type', models.ForeignKey(limit_choices_to={'field__key': 'type'}, on_delete=django.db.models.deletion.CASCADE, related_name='type_field_choice', to='doccheck.fieldchoice')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Person',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pledge_pillar_3_account', models.BooleanField(default=False)),
                ('pledge_pillar_3_insurance', models.BooleanField(default=False)),
                ('has_divorce', models.BooleanField(default=False)),
                ('has_liabilities', models.BooleanField(default=False)),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.case')),
                ('main_income', models.ForeignKey(limit_choices_to={'field__key': 'main_income'}, on_delete=django.db.models.deletion.CASCADE, related_name='income_field_choice', to='doccheck.fieldchoice')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='field',
            name='choices_default',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='default', to='doccheck.fieldchoice'),
        ),
        migrations.AddField(
            model_name='field',
            name='doc_check',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.doccheck'),
        ),
        migrations.AddField(
            model_name='field',
            name='shown_for_the_following_business_case_types',
            field=models.ManyToManyField(to='doccheck.BusinessCaseType'),
        ),
        migrations.CreateModel(
            name='DocumentOption',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('max_doc_age_in_months', models.IntegerField(blank=True, null=True)),
                ('completeness_rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.completenessrule')),
                ('document_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='doccheck.documentcategory')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='completenessrule',
            name='doc_check',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.doccheck'),
        ),
        migrations.AddField(
            model_name='case',
            name='doc_check',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.doccheck'),
        ),
        migrations.AddField(
            model_name='businesscasetype',
            name='doc_check',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='doccheck.doccheck'),
        ),
        migrations.AlterUniqueTogether(
            name='field',
            unique_together={('doc_check', 'entity', 'key')},
        ),
        migrations.AlterUniqueTogether(
            name='completenessrule',
            unique_together={('doc_check', 'key')},
        ),
        migrations.AlterUniqueTogether(
            name='businesscasetype',
            unique_together={('doc_check', 'key')},
        ),
    ]
