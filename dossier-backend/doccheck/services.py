from typing import List
from uuid import UUID, uuid4

from django.shortcuts import get_object_or_404

from doccheck import schemas
from doccheck.models import (
    Person,
    Field,
    RealEstateProperty,
    Entity,
    Case,
    DocCheck,
    CompletenessRule,
    BusinessCaseType,
    FieldChoice,
)


def create_case(doc_check: Doc<PERSON>he<PERSON>, business_case_type: BusinessCaseType) -> Case:
    case = Case.objects.create(
        doc_check=doc_check, business_case_type=business_case_type
    )
    add_person_to_case(case=case, person_to_create=schemas.PersonCreate())
    add_real_estate_property_to_case(
        case=case, property_to_create=schemas.RealEstatePropertyCreate()
    )
    return case


def map_case_detail_out(case: Case) -> schemas.CaseDetailOut:
    property = case.realestateproperty_set.first()
    case_detail_out = schemas.CaseDetailOut(
        uuid=case.uuid,
        business_case_type=(
            case.business_case_type.key if case.business_case_type else None
        ),
        persons=[
            map_person_out(idx, person)
            for idx, person in enumerate(case.person_set.order_by("created_at").all())
        ],
        real_estate_property=map_property_out(property) if property else None,
    )
    return case_detail_out


def update_case(case: Case, case_to_update: schemas.CaseUpdate) -> Case:
    bct = BusinessCaseType.objects.get(
        doc_check=case.doc_check, key=case_to_update.business_case_type
    )
    case.business_case_type = bct
    case.save()
    return case


def map_person_out(idx: int, person: Person) -> schemas.PersonOut:
    counter = idx + 1
    return schemas.PersonOut(
        uuid=person.uuid,
        name_de=f"Kreditnehmer {counter}",
        name_en=f"Borrower {counter}",
        name_fr=f"Emprunteur {counter}",
        name_it=f"Mutuatario {counter}",
        main_income=person.main_income.key if person.main_income else None,
        pledge_pillar_3_account=person.pledge_pillar_3_account,
        pledge_pillar_3_insurance=person.pledge_pillar_3_insurance,
        has_divorce=person.has_divorce,
        has_liabilities=person.has_liabilities,
    )


def add_person_to_case(case: Case, person_to_create: schemas.PersonCreate) -> Person:
    field_values = {}
    for field in schemas.PersonCreate.model_fields:
        if getattr(person_to_create, field):
            field_values[field] = getattr(person_to_create, field)
        elif Field.objects.filter(
            doc_check=case.doc_check, entity=Entity.Person, key=field
        ).exists():
            section_field = Field.objects.get(
                doc_check=case.doc_check, entity=Entity.Person, key=field
            )
            if section_field.choices_default:
                field_values[field] = section_field.choices_default.key
        if field not in field_values:
            field_values[field] = Person._meta.get_field(field).get_default()
    person = Person.objects.create(
        case_id=case.uuid,
        main_income_id=FieldChoice.objects.get(
            field__doc_check=case.doc_check,
            field__key="main_income",
            key=field_values["main_income"],
        ).uuid,
        pledge_pillar_3_account=field_values["pledge_pillar_3_account"],
        pledge_pillar_3_insurance=field_values["pledge_pillar_3_insurance"],
        has_divorce=field_values["has_divorce"],
        has_liabilities=field_values["has_liabilities"],
    )
    return person


def update_person(
    case: Case, person_uuid: UUID, person_to_update: schemas.PersonUpdate
) -> Person:
    person = get_object_or_404(Person, uuid=person_uuid, case=case)
    for attr, value in person_to_update.model_dump().items():
        if value is not None:
            # for fields with type "choice", the attribute cannot be set directly as it is a foreign key uuid reference
            if Field.objects.get(key=attr).type == 1:
                fk_value_id = FieldChoice.objects.get(
                    field__doc_check=case.doc_check, field__key=attr, key=value
                ).uuid
                setattr(person, f"{attr}_id", fk_value_id)
            else:
                setattr(person, attr, value)
    person.save()
    return person


def map_property_out(property: RealEstateProperty) -> schemas.RealEstatePropertyOut:
    return schemas.RealEstatePropertyOut(
        uuid=property.uuid,
        name_de="Liegenschaft",
        name_en="Property",
        name_fr="Immeuble",
        name_it="Proprietà",
        type=property.type.key if property.type else None,
        address_canton=property.address_canton.key if property.address_canton else None,
        for_rent=property.for_rent,
        purchase_year_lt_2y=property.purchase_year_lt_2y,
        has_use_restrictions=property.has_use_restrictions,
        has_reconstructions=property.has_reconstructions,
        has_construction_contract=property.has_construction_contract,
        gustavo=property.gustavo,
    )


def add_real_estate_property_to_case(
    case: Case, property_to_create: schemas.RealEstatePropertyCreate
) -> RealEstateProperty:
    field_values = {}
    for field in schemas.RealEstatePropertyCreate.model_fields:
        if getattr(property_to_create, field):
            field_values[field] = getattr(property_to_create, field)
        elif Field.objects.filter(
            doc_check=case.doc_check, entity=Entity.RealEstateProperty, key=field
        ).exists():
            section_field = Field.objects.get(
                doc_check=case.doc_check, entity=Entity.RealEstateProperty, key=field
            )
            if section_field.choices_default:
                field_values[field] = section_field.choices_default.key
        if field not in field_values:
            field_values[field] = RealEstateProperty._meta.get_field(
                field
            ).get_default()
    property = RealEstateProperty.objects.create(
        case_id=case.uuid,
        type_id=FieldChoice.objects.get(
            field__doc_check=case.doc_check, field__key="type", key=field_values["type"]
        ).uuid,
        address_canton_id=FieldChoice.objects.get(
            field__doc_check=case.doc_check,
            field__key="address_canton",
            key=field_values["address_canton"],
        ).uuid,
        for_rent=field_values["for_rent"],
        purchase_year_lt_2y=field_values["purchase_year_lt_2y"],
        has_use_restrictions=field_values["has_use_restrictions"],
        has_reconstructions=field_values["has_reconstructions"],
        has_construction_contract=field_values["has_construction_contract"],
    )
    return property


def update_property(
    case: Case,
    property_uuid: UUID,
    property_to_update: schemas.RealEstatePropertyUpdate,
) -> RealEstateProperty:
    property = get_object_or_404(
        RealEstateProperty, uuid=property_uuid, case__uuid=case.uuid
    )
    for attr, value in property_to_update.model_dump().items():
        if value is not None:
            # for fields with type "choice", the attribute cannot be set directly as it is a foreign key uuid reference
            if Field.objects.get(doc_check=case.doc_check, key=attr).type == 1:
                fk_value_id = FieldChoice.objects.get(
                    field__doc_check=case.doc_check, field__key=attr, key=value
                ).uuid
                setattr(property, f"{attr}_id", fk_value_id)
            else:
                setattr(property, attr, value)
    property.save()
    return property


def map_fields_out(case: Case) -> List[schemas.Field]:
    result = []
    result.append(
        schemas.Field(
            uuid=uuid4(),
            entity=Entity(1).name,
            key="business_case_type",
            name_de="Geschäftsfalltyp",
            name_en="Business case type",
            name_fr="Type de transaction",
            name_it="Tipo di caso aziendale",
            choices=[
                schemas.FieldChoice(
                    uuid=bct.uuid,
                    key=bct.key,
                    name_de=bct.name_de or bct.key,
                    name_en=bct.name_en or bct.key,
                    name_fr=bct.name_fr or bct.key,
                    name_it=bct.name_it or bct.key,
                )
                for bct in BusinessCaseType.objects.filter(doc_check=case.doc_check)
                .order_by("order")
                .all()
            ],
        )
    )
    for field in (
        Field.objects.filter(
            doc_check=case.doc_check,
            shown_for_the_following_business_case_types=case.business_case_type,
        )
        .order_by("order")
        .all()
    ):
        result.append(
            schemas.Field(
                uuid=field.uuid,
                entity=Entity(field.entity).name,
                key=field.key,
                name_de=field.name_de or field.key,
                name_en=field.name_en or field.key,
                name_fr=field.name_fr or field.key,
                name_it=field.name_it or field.key,
                choices=[
                    schemas.FieldChoice(
                        uuid=choice.uuid,
                        key=choice.key,
                        name_de=choice.name_de or choice.key,
                        name_en=choice.name_en or choice.key,
                        name_fr=choice.name_fr or choice.key,
                        name_it=choice.name_it or choice.key,
                    )
                    for choice in field.fieldchoice_set.order_by("order").all()
                ],
            )
        )
    return result


def add_completeness_rule_to_check(
    doc_check: DocCheck, rule_to_create: schemas.CompletenessRule
) -> CompletenessRule:
    rule = CompletenessRule.objects.create(
        doc_check=doc_check,
        key=rule_to_create.key,
        title_de=rule_to_create.title_de,
        title_en=rule_to_create.title_en,
        title_fr=rule_to_create.title_fr,
        title_it=rule_to_create.title_it,
        desc_de=rule_to_create.desc_de,
        desc_en=rule_to_create.desc_en,
        desc_fr=rule_to_create.desc_fr,
        desc_it=rule_to_create.desc_it,
        entity=rule_to_create.entity,
        field=rule_to_create.field,
        values=rule_to_create.values,
        strictness=rule_to_create.strictness,
        order=rule_to_create.order,
    )
    return rule


def get_applicable_context_uuids_for_rule(
    case: Case, completeness_rule: CompletenessRule
) -> List[UUID]:
    applicable_context_uuids = []
    entity_set_mappings = {2: "person_set", 3: "realestateproperty_set"}

    for entity, entity_set in entity_set_mappings.items():
        if entity == completeness_rule.entity:
            if completeness_rule.field:
                for instance in getattr(case, entity_set).all():
                    if hasattr(instance, completeness_rule.field):
                        if str(
                            getattr(instance, completeness_rule.field)
                        ) in completeness_rule.values.split(","):
                            applicable_context_uuid = instance.uuid
                            if applicable_context_uuid:
                                applicable_context_uuids.append(applicable_context_uuid)
            else:
                for instance in getattr(case, entity_set).all():
                    applicable_context_uuid = instance.uuid
                    if applicable_context_uuid:
                        applicable_context_uuids.append(applicable_context_uuid)

    return applicable_context_uuids


def check_document_requirements(case: Case) -> List[schemas.DocumentRequirement]:
    completeness_rules = (
        CompletenessRule.objects.filter(
            doc_check=case.doc_check, business_case_types=case.business_case_type
        )
        .order_by("order")
        .all()
    )
    result = []
    for rule in completeness_rules:
        applicable_context_uuids = get_applicable_context_uuids_for_rule(case, rule)
        for applicable_context_uuid in applicable_context_uuids:
            result.append(
                schemas.DocumentRequirement(
                    case_uuid=case.uuid,
                    context_model_uuid=applicable_context_uuid,
                    rule_key=rule.key,
                    rule_title_de=rule.title_de,
                    rule_title_en=rule.title_en,
                    rule_title_fr=rule.title_fr,
                    rule_title_it=rule.title_it,
                    rule_desc_de=rule.desc_de,
                    rule_desc_en=rule.desc_en,
                    rule_desc_fr=rule.desc_fr,
                    rule_desc_it=rule.desc_it,
                    rule_strictness=rule.strictness,
                    rule_order=rule.order,
                    document_options=[
                        schemas.DocumentOption(
                            uuid=option.uuid,
                            document_category=option.document_category.name,
                            max_doc_age_in_months=option.max_doc_age_in_months,
                        )
                        for option in rule.documentoption_set.all()
                    ],
                )
            )
    return result
