from pathlib import Path

import djclick as click

from doccheck.export import import_business_case_types


@click.group()
def cli():
    pass


@cli.command()
@click.argument("source_path")
@click.argument("doc_check_key")
def import_business_case_types_from_json(source_path: str, doc_check_key: str):
    """

    @param source_path:
    @param doc_check_key:
    @return:
    """
    import_business_case_types(Path(source_path), doc_check_key)
