from adminsortable.admin import SortableAdmin
from django.contrib import admin

from doccheck.models import (
    DocumentOption,
    CompletenessRule,
    Person,
    Case,
    RealEstateProperty,
    DocCheck,
    BusinessCaseType,
    FieldChoice,
    Field,
    DocumentCategory,
)


@admin.register(BusinessCaseType)
class BusinessCaseTypeAdmin(SortableAdmin):
    list_display = [
        "uuid",
        "key",
        "name_de",
        "name_en",
        "name_fr",
        "name_it",
        "order",
        "doc_check",
        "created_at",
        "updated_at",
    ]
    search_fields = ["key", "name_de", "name_en", "name_fr", "name_it", "doc_check"]
    list_filter = ["doc_check", "key"]
    readonly_fields = ["created_at", "updated_at"]


class ChoiceInline(admin.TabularInline):
    model = FieldChoice
    extra = 0


@admin.register(Field)
class FieldAdmin(SortableAdmin):
    list_display = [
        "uuid",
        "doc_check",
        "entity",
        "key",
        "type",
        "name_de",
        "name_en",
        "name_fr",
        "name_it",
        "order",
        "choices_default",
        "get_business_cases",
    ]
    search_fields = [
        "doc_check",
        "entity",
        "key",
        "name_de",
        "name_en",
        "name_fr",
        "name_it",
        "choices_default",
    ]
    list_filter = ["doc_check", "entity"]
    inlines = (ChoiceInline,)

    def get_business_cases(self, obj):
        return [
            bct.key for bct in obj.shown_for_the_following_business_case_types.all()
        ]


@admin.register(FieldChoice)
class FieldChoiceAdmin(SortableAdmin):
    list_display = [
        "uuid",
        "field",
        "key",
        "name_de",
        "name_en",
        "name_fr",
        "name_it",
        "order",
    ]
    search_fields = ["field", "key", "name_de", "name_en", "name_fr", "name_it"]
    list_filter = ["field"]


class PersonInline(admin.TabularInline):
    model = Person
    extra = 0


class PropertyInline(admin.TabularInline):
    model = RealEstateProperty
    extra = 0


@admin.register(DocumentCategory)
class DocumentCategoryAdmin(admin.ModelAdmin):
    list_display = [
        "uuid",
        "doc_check",
        "name",
        "created_at",
        "updated_at",
    ]
    search_fields = ["name"]
    list_filter = ["doc_check"]
    readonly_fields = ["created_at", "updated_at"]


@admin.register(Case)
class CaseAdmin(admin.ModelAdmin):
    list_display = [
        "uuid",
        "business_case_type",
        "doc_check",
        "get_persons",
        "get_properties",
        "created_at",
        "updated_at",
    ]
    search_fields = ["business_case_type__key", "doc_check__key"]
    readonly_fields = ["created_at", "updated_at"]
    list_filter = ["business_case_type", "doc_check"]
    inlines = (PersonInline, PropertyInline)

    def get_persons(self, obj):
        return [person for person in Person.objects.filter(case=obj)]

    def get_properties(self, obj):
        return [property for property in RealEstateProperty.objects.filter(case=obj)]


class CompletenessRuleInline(admin.TabularInline):
    model = CompletenessRule
    extra = 0


@admin.register(DocCheck)
class CompletenessCheckAdmin(admin.ModelAdmin):
    list_display = ["uuid", "key", "description", "created_at", "updated_at"]
    inlines = [CompletenessRuleInline]

    readonly_fields = ["created_at", "updated_at"]


class DocumentOptionsInline(admin.TabularInline):
    model = DocumentOption
    readonly_fields = ["uuid"]
    extra = 0


@admin.register(CompletenessRule)
class CompletenessRuleAdmin(SortableAdmin):
    list_display = [
        "uuid",
        "doc_check",
        "get_business_cases",
        "key",
        "title_de",
        "title_en",
        "title_fr",
        "title_it",
        "desc_de",
        "desc_en",
        "desc_fr",
        "desc_it",
        "entity",
        "field",
        "values",
        "strictness",
        "get_document_options",
        "order",
        "created_at",
        "updated_at",
    ]
    search_fields = [
        "key",
        "title_de",
        "title_en",
        "title_fr",
        "title_it",
        "desc_de",
        "desc_en",
        "desc_fr",
        "desc_it",
    ]
    list_filter = ["doc_check", "entity", "field", "values", "strictness"]
    readonly_fields = ["created_at", "updated_at"]
    inlines = [DocumentOptionsInline]

    def get_business_cases(self, obj):
        return [bc for bc in obj.business_case_types.all()]

    def get_document_options(self, obj):
        return [do for do in DocumentOption.objects.filter(completeness_rule=obj)]
