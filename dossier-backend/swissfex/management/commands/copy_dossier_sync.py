from uuid import UUID


import djclick as click
import structlog

from dossier.helpers_model_copier import copy_dossier_models
from dossier.models import Dossier

logger = structlog.get_logger(__name__)


@click.command()
@click.argument("source_dossier_uuid", type=click.UUID)
@click.argument("new_external_id", type=click.STRING)
def copy_dossier(source_dossier_uuid: UUID, new_external_id: str):
    """
    Used to manually test/verify the async copy of a dossier in swissfex api

    Copy a dossier with all processed entities to a new dossier.

    Example:
        python manage.py copy_dossier_async 254e93ec-c0f2-4133-be04-24170c60e650 254e93ec-c0f2-4133-be04-24170c60e65z

    @param source_dossier_uuid:
    @param new_external_id:
    @return:
    """
    logger.info(
        "Creating copy",
        source_dossier_uuid={source_dossier_uuid},
        new_external_id={new_external_id},
    )

    source_dossier = Dossier.objects.get(uuid=source_dossier_uuid)

    if Dossier.objects.filter(
        account=source_dossier.account, external_id=new_external_id
    ).exists():
        return 409, {
            "code": 409,
            "message": f"Dossier with external dossier id {new_external_id} already exists",
        }

    new_instances = copy_dossier_models(
        dossier=source_dossier, external_id=new_external_id
    )

    new_dossier: Dossier = next(iter(new_instances.values()))

    return 201, {
        "external_dossier_id": new_dossier.external_id,
        "dossier_uuid": new_dossier.uuid,
        "updated_at": new_dossier.updated_at,
        "created_at": new_dossier.created_at,
    }
