import structlog

import djclick as click

from core.publisher import publish
from dossier.models import <PERSON>ssier
from dossier_zipper.schemas import DossierZipRequestV1
from dossier_zipper.workers import generate_dossier_zip_request
from projectconfig.settings import ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY

logger = structlog.get_logger()


@click.command()
@click.option("-dossier_uuid", type=click.UUID)
@click.option("-add_uuid_suffix", default=False, type=click.BOOL)
def export_swissfex_dossier(
    dossier_uuid: str,
    add_uuid_suffix: bool,
):
    # python manage.py export_swissfex_dossier -dossier_uuid 737521ab-f0e8-477d-94e0-0c193ac5c068 -add_uuid_suffix True

    dossier = Dossier.objects.get(uuid=dossier_uuid)

    dossier_zip_request: DossierZipRequestV1 = generate_dossier_zip_request(
        dossier=dossier, add_uuid_suffix=add_uuid_suffix
    )

    publish(
        message=dossier_zip_request.model_dump_json().encode(),
        routing_key=ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY,
    )

    logger.info(
        f"Exported dossier with uuid {dossier_uuid} for SwissFEX, DossierExport uuid "
        f"{dossier_zip_request.zip_request_uuid}"
    )
