from uuid import UUID

import pika

from core.publisher import publish

import djclick as click
import structlog

from dossier.helpers_model_copier import copy_single_dossier
from dossier.models import <PERSON><PERSON>r, DossierCopyStatus
from dossier.schemas import DossierCopyContentsIntoExistingDossierRequest
from django.conf import settings

logger = structlog.get_logger(__name__)


@click.command()
@click.argument("source_dossier_uuid", type=click.UUID)
@click.argument("new_external_id", type=click.STRING)
def copy_dossier_async(source_dossier_uuid: UUID, new_external_id: str):
    """
    Used to manually test/verify the async copy of a dossier in swissfex api

    Copy a dossier with all processed entities to a new dossier.

    Example:
        python manage.py copy_dossier_async 254e93ec-c0f2-4133-be04-24170c60e650 254e93ec-c0f2-4133-be04-24170c60e65z

    @param source_dossier_uuid:
    @param new_external_id:
    @return:
    """
    logger.info(
        "Creating async copy",
        source_dossier_uuid={source_dossier_uuid},
        new_external_id={new_external_id},
    )

    source_dossier = Dossier.objects.get(uuid=source_dossier_uuid)

    # Create new dossier
    target_dossier = copy_single_dossier(
        dossier=source_dossier, external_id=new_external_id
    )

    # Dispatch request to copy contents
    publish(
        message=DossierCopyContentsIntoExistingDossierRequest(
            source_dossier_uuid=source_dossier.uuid,
            target_dossier_uuid=target_dossier.uuid,
        )
        .model_dump_json()
        .encode(),
        routing_key=settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME,
        properties=pika.BasicProperties(
            type=settings.ASYNC_DOSSIER_COPY_INTO_EXISTING_DOSSIER_V1_QUEUE_NAME,
        ),
    )

    # Create copy status entry, so we can keep track of progress

    DossierCopyStatus.objects.get_or_create(
        defaults={
            "source_dossier_uuid": source_dossier.uuid,
            "target_dossier": target_dossier,
        },
        account=source_dossier.account,
        target_external_id=new_external_id,
        target_dossier=target_dossier,
    )
