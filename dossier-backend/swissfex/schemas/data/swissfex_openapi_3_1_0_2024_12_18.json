{"openapi": "3.1.0", "info": {"title": "Hypodossier - Swissfex API", "version": "0.7.0", "description": ""}, "paths": {"/partner/swissfex/api/0.7/dossier/create": {"post": {"operationId": "swissfex_api_create_dossier", "summary": "Create Dossier", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/": {"patch": {"operationId": "swissfex_api_update_dossier", "summary": "Update Dossier", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Updates a new Dossier based on the provided parameters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/copy": {"post": {"operationId": "swissfex_api_copy_dossier", "summary": "<PERSON><PERSON>", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierCopyResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Creates a new Dossier based on the provided parameters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CopyDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/copy_async": {"post": {"operationId": "swissfex_api_copy_dossier_async", "summary": "<PERSON><PERSON>", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierCopyAsyncResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Create a new dossier and dispatch a task to copy the contents of\nthe source dossier into the new dossier\n\nThe reason we do it this way is that Swissfex doesn't have a way of Polling for status\nbut they need a valid (i.e. already created) dossier for their system.\nThey are happy to have an empty Dossier, that progressively gets filled with content", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CopyDossier"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/{external_dossier_id}/copy_status": {"get": {"operationId": "swissfex_api_get_dossier_copy_status", "summary": "Get Dossier Copy Status", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierCopyStatus"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/original-file": {"post": {"operationId": "swissfex_api_add_original_file", "summary": "Add Original File", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatedObjectReference"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"file": {"format": "binary", "title": "File", "type": "string"}, "allow_duplicate_and_rename": {"default": false, "title": "Allow Duplicate And Rename", "type": "boolean"}, "force_document_category_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Force Document Category Key"}, "force_title_suffix": {"anyOf": [{"maxLength": 170, "type": "string"}, {"type": "null"}], "title": "Force Title Suffix"}, "force_external_semantic_document_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Force External Semantic Document Id"}, "force_access_mode": {"anyOf": [{"$ref": "#/components/schemas/OriginalFileForceAccessMode"}, {"type": "null"}]}, "force_semantic_document_custom_attribute": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Force Semantic Document Custom Attribute"}}, "required": ["file"], "title": "MultiPartBodyParams", "type": "object"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/details": {"get": {"operationId": "swissfex_api_get_dossier", "summary": "Get Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/file-status": {"get": {"operationId": "swissfex_api_get_file_status", "summary": "Get File Status", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierProcessingStatus"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-documents": {"get": {"operationId": "swissfex_api_get_semantic_documents", "summary": "Get Semantic Documents", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "query", "name": "show_pages", "schema": {"default": false, "title": "Show Pages", "type": "boolean"}, "required": false}, {"in": "query", "name": "show_soft_deleted", "schema": {"default": false, "title": "Show Soft Deleted", "type": "boolean"}, "required": false}, {"in": "query", "name": "show_all_documents_for_soft_deleted", "schema": {"default": false, "title": "Show All Documents For Soft Deleted", "type": "boolean"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SemanticDocument"}, "title": "Response", "type": "array"}}}}}, "description": "Return schemantic documents for a dossier\nif show_pages is true, then also return the pages for each document", "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}": {"patch": {"operationId": "swissfex_api_update_semantic_document", "summary": "Update Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocument"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"title": "Response", "type": "string"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "description": "Update a schemantic document for a dossier\nif show_pages is true, then also return the pages for each document", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSemanticDocument"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}, "delete": {"operationId": "swissfex_api_soft_delete_semantic_document", "summary": "Soft Delete Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"format": "uuid", "title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/document-categories": {"get": {"operationId": "swissfex_api_get_document_categories", "summary": "Get Document Categories", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentCategories"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/": {"post": {"operationId": "swissfex_api_export_dossier_export", "summary": "Export Dossier Export", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequest"}}}}}, "description": "Export a dossier as a zip file\n\nFor an external dossier, dispatch a request to rabbitmq for dossier export and return a download url\n\nIf a dossier export already exists, create a new dossier export and return a download url", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDossierExport"}}}, "required": true}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/{export_uuid}/status": {"get": {"operationId": "swissfex_api_get_dossier_export_status", "summary": "Get Dossier Export Status", "parameters": [{"in": "path", "name": "export_uuid", "schema": {"title": "Export U<PERSON>", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportStatus"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}": {"delete": {"operationId": "swissfex_api_delete_dossier", "summary": "Delete Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}": {"post": {"operationId": "swissfex_api_export_semantic_document_pdf", "summary": "Export Semantic Document Pdf", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocumentPDFExportRequest"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/export/{semantic_document_export_request_uuid}/status": {"get": {"operationId": "swissfex_api_get_semantic_document_export_status", "summary": "Get Semantic Document Export Status", "parameters": [{"in": "path", "name": "semantic_document_export_request_uuid", "schema": {"title": "Semantic Document Export Request Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportStatus"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-page/{semantic_page_uuid}": {"delete": {"operationId": "swissfex_api_soft_delete_semantic_page", "summary": "Soft Delete Semantic Page", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_page_uuid", "schema": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}/restore": {"put": {"operationId": "swissfex_api_undelete_semantic_document", "summary": "Undelete Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"format": "uuid", "title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}, "/partner/swissfex/api/0.7/dossier/{external_dossier_id}/semantic-page/{semantic_page_uuid}/restore": {"put": {"operationId": "swissfex_api_undelete_semantic_page", "summary": "Undelete Semantic Page", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_page_uuid", "schema": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"SwissfexJWTAuth": []}]}}}, "components": {"schemas": {"Dossier": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}}, "required": ["uuid", "external_dossier_id", "updated_at", "created_at"], "title": "Dossier", "type": "object"}, "Error": {"properties": {"code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}}, "required": ["code", "message"], "title": "Error", "type": "object"}, "CreateDossier": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "name": {"maxLength": 255, "title": "Name", "type": "string"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["external_dossier_id", "name", "language"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "Language": {"enum": ["de", "fr", "it", "en"], "title": "Language", "type": "string"}, "ChangeDossier": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "name": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Name"}, "lang": {"anyOf": [{"$ref": "#/components/schemas/Language"}, {"type": "null"}]}}, "required": ["external_dossier_id"], "title": "Change<PERSON><PERSON><PERSON>", "type": "object"}, "DossierCopyResponse": {"properties": {"dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}}, "required": ["dossier_uuid", "external_dossier_id", "updated_at", "created_at"], "title": "DossierCopyResponse", "type": "object"}, "AccessMode": {"description": "Access mode of a semantic document (not the same as access mode of a dossier)", "enum": ["read_only", "read_write"], "title": "AccessMode", "type": "string"}, "CopyDossier": {"properties": {"new_external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "New External Dossier Id", "type": "string"}, "name": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Name"}, "language": {"anyOf": [{"$ref": "#/components/schemas/Language"}, {"type": "null"}]}, "access_mode": {"anyOf": [{"$ref": "#/components/schemas/AccessMode"}, {"type": "null"}]}, "include_deleted": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Include Deleted"}}, "required": ["new_external_dossier_id"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "DossierCopyAsyncResponse": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}}, "required": ["external_dossier_id"], "title": "DossierCopyAsyncResponse", "type": "object"}, "DossierCopyStatus": {"properties": {"external_dossier_id": {"title": "External Dossier Id", "type": "string"}, "status": {"$ref": "#/components/schemas/ExportProcessingStatus"}, "updated_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Updated At"}}, "required": ["external_dossier_id", "status"], "title": "DossierCopyStatus", "type": "object"}, "ExportProcessingStatus": {"enum": ["PROCESSING", "ERROR", "PROCESSED"], "title": "ExportProcessingStatus", "type": "string"}, "CreatedObjectReference": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}}, "required": ["uuid"], "title": "CreatedObjectReference", "type": "object"}, "Message": {"properties": {"detail": {"title": "Detail", "type": "string"}}, "required": ["detail"], "title": "Message", "type": "object"}, "OriginalFileForceAccessMode": {"enum": ["read_write", "read_only"], "title": "OriginalFileForceAccessMode", "type": "string"}, "DossierProcessingStatus": {"properties": {"dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "external_id": {"title": "External Id", "type": "string"}, "progress": {"title": "Progress", "type": "integer"}, "original_files": {"items": {"$ref": "#/components/schemas/OriginalFile"}, "title": "Original Files", "type": "array"}}, "required": ["dossier_uuid", "external_id", "progress", "original_files"], "title": "DossierProcessingStatus", "type": "object"}, "ExtractedFile": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "path_from_original": {"title": "Path From Original", "type": "string"}, "file_name": {"title": "File Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "file_url": {"title": "File Url", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["uuid", "path_from_original", "file_name", "status", "file_url", "created_at", "updated_at"], "title": "ExtractedFile", "type": "object"}, "FileStatus": {"enum": ["processing", "error", "processed"], "title": "FileStatus", "type": "string"}, "OriginalFile": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "name": {"title": "Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "extracted_files": {"items": {"$ref": "#/components/schemas/ExtractedFile"}, "title": "Extracted Files", "type": "array"}, "file_url": {"title": "File Url", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["uuid", "name", "status", "extracted_files", "file_url", "created_at", "updated_at"], "title": "OriginalFile", "type": "object"}, "Confidence": {"enum": ["certain", "high", "medium", "low"], "title": "Confidence", "type": "string"}, "SemanticDocument": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "title": {"title": "Title", "type": "string"}, "title_lang": {"title": "Title Lang", "type": "string"}, "title_suffix": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title Suffix"}, "external_semantic_document_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "External Semantic Document Id"}, "document_category_id": {"title": "Document Category Id", "type": "string"}, "document_category_key": {"title": "Document Category Key", "type": "string"}, "document_category_title_de": {"title": "Document Category Title De", "type": "string"}, "document_category_title_en": {"title": "Document Category Title En", "type": "string"}, "document_category_title_fr": {"title": "Document Category Title Fr", "type": "string"}, "document_category_title_it": {"title": "Document Category Title It", "type": "string"}, "document_category_confidence": {"anyOf": [{"$ref": "#/components/schemas/Confidence"}, {"type": "null"}]}, "semantic_pages": {"items": {"$ref": "#/components/schemas/SemanticPage"}, "title": "Semantic Pages", "type": "array"}, "access_mode": {"$ref": "#/components/schemas/AccessMode"}, "custom_attribute": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Custom Attribute"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "last_change": {"format": "date-time", "title": "Last Change", "type": "string"}, "deleted_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deleted At"}}, "required": ["uuid", "title", "title_lang", "document_category_id", "document_category_key", "document_category_title_de", "document_category_title_en", "document_category_title_fr", "document_category_title_it", "semantic_pages", "access_mode", "created_at", "updated_at", "last_change"], "title": "SemanticDocument", "type": "object"}, "SemanticPage": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "number": {"description": "Page number is zero based. First page has page number 0", "title": "Number", "type": "integer"}, "image_url": {"title": "Image Url", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "deleted_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deleted At"}}, "required": ["uuid", "number", "image_url", "updated_at"], "title": "SemanticPage", "type": "object"}, "UpdateSemanticDocument": {"properties": {"document_category_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Category Key"}, "title_suffix": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title Suffix"}, "external_semantic_document_id": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "External Semantic Document Id"}, "access_mode": {"anyOf": [{"$ref": "#/components/schemas/AccessMode"}, {"type": "null"}]}, "custom_attribute": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Custom Attribute"}}, "title": "UpdateSemanticDocument", "type": "object"}, "SavingResultWithMessage": {"properties": {"message": {"title": "Message", "type": "string"}}, "required": ["message"], "title": "SavingResultWithMessage", "type": "object"}, "DocumentCategories": {"additionalProperties": {"$ref": "#/components/schemas/DocumentCategory"}, "title": "DocumentCategories", "type": "object"}, "DocumentCategory": {"properties": {"key": {"title": "Key", "type": "string"}, "id": {"title": "Id", "type": "string"}, "title_de": {"title": "Title De", "type": "string"}, "title_en": {"title": "Title En", "type": "string"}, "title_fr": {"title": "Title Fr", "type": "string"}, "title_it": {"title": "Title It", "type": "string"}, "description_de": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description De"}, "description_en": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description En"}, "description_fr": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description Fr"}, "description_it": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description It"}}, "required": ["key", "id", "title_de", "title_en", "title_fr", "title_it"], "title": "DocumentCategory", "type": "object"}, "ExportRequest": {"properties": {"export_uuid": {"format": "uuid", "title": "Export U<PERSON>", "type": "string"}}, "required": ["export_uuid"], "title": "ExportRequest", "type": "object"}, "ExportDossierExport": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "add_uuid_suffix": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Add Uuid Suffix"}}, "required": ["external_dossier_id"], "title": "ExportDossierExport", "type": "object"}, "ExportStatus": {"properties": {"export_uuid": {"format": "uuid", "title": "Export U<PERSON>", "type": "string"}, "status": {"$ref": "#/components/schemas/ExportProcessingStatus"}, "dossier_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dossier Url"}, "updated_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Updated At"}}, "required": ["export_uuid", "status"], "title": "ExportStatus", "type": "object"}, "SemanticDocumentPDFExportRequest": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}}, "required": ["uuid"], "title": "SemanticDocumentPDFExportRequest", "type": "object"}}, "securitySchemes": {"SwissfexJWTAuth": {"type": "http", "scheme": "bearer"}}}, "servers": []}