from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional
from uuid import UUID

import structlog
from django.shortcuts import get_object_or_404
from pydantic import StringConstraints, Field, RootModel, BaseModel

from core.generics import AnyHttpUrlStr
from dossier.models import Account, DossierUser
from dossier.schemas import Language, AccessMode
from projectconfig import authentication
from semantic_document.schemas import Confidence
from typing_extensions import Annotated

logger = structlog.get_logger()


ExternalDossierID = Annotated[
    str, StringConstraints(max_length=255, pattern="[A-Za-z0-9-]{1,255}")
]
ExternalSemanticDocumentID = Annotated[str, StringConstraints(max_length=255)]
PrincipalID = Annotated[str, StringConstraints(max_length=255)]
DossierName = Annotated[str, StringConstraints(max_length=255)]
CustomAttribute = Annotated[str, StringConstraints(max_length=255)]


class CreateDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: DossierName
    language: Language


class ChangeDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: Optional[DossierName] = None
    lang: Optional[Language] = None


class CopyDossier(BaseModel):
    new_external_dossier_id: ExternalDossierID
    name: Optional[DossierName] = None
    language: Optional[Language] = None
    access_mode: Optional[AccessMode] = None
    include_deleted: Optional[bool] = True


class FileStatus(str, Enum):
    PROCESSING = "processing"
    ERROR = "error"
    PROCESSED = "processed"


class ExtractedFile(BaseModel):
    uuid: UUID
    path_from_original: str
    file_name: str
    status: FileStatus
    file_url: AnyHttpUrlStr
    created_at: datetime
    updated_at: datetime


class OriginalFile(BaseModel):
    uuid: UUID
    name: str
    status: FileStatus
    extracted_files: List[ExtractedFile]
    file_url: AnyHttpUrlStr
    created_at: datetime
    updated_at: datetime


class DossierProcessingStatus(BaseModel):
    dossier_uuid: UUID
    external_id: str
    progress: int
    original_files: List[OriginalFile]


class SemanticPage(BaseModel):
    uuid: UUID
    number: int = Field(
        description="Page number is zero based. First page has page number 0"
    )
    image_url: str

    updated_at: datetime
    deleted_at: Optional[datetime] = None


class SemanticDocument(BaseModel):
    uuid: UUID
    title: str
    title_lang: str  # Language of title (same as language of dossier)
    title_suffix: Optional[str] = None
    external_semantic_document_id: Optional[str] = None

    document_category_id: str
    document_category_key: str
    document_category_title_de: str
    document_category_title_en: str
    document_category_title_fr: str
    document_category_title_it: str
    document_category_confidence: Optional[Confidence] = None

    semantic_pages: List[SemanticPage]

    access_mode: AccessMode  # Default is AccessMode.READ_WRITE
    custom_attribute: Optional[CustomAttribute] = None
    created_at: datetime
    updated_at: datetime
    last_change: datetime  # = datetime.now(tz=tz.tzutc())
    deleted_at: Optional[datetime] = None


class Dossier(BaseModel):
    uuid: UUID
    external_dossier_id: ExternalDossierID

    updated_at: datetime
    created_at: datetime


class DossierCopyResponse(BaseModel):
    dossier_uuid: UUID
    external_dossier_id: ExternalDossierID

    updated_at: datetime
    created_at: datetime


class DossierCopyAsyncResponse(BaseModel):
    uuid: UUID
    external_dossier_id: ExternalDossierID


class ExportProcessingStatus(str, Enum):
    PROCESSING = "PROCESSING"
    ERROR = "ERROR"
    PROCESSED = "PROCESSED"


class ExportStatus(BaseModel):
    export_uuid: UUID
    status: ExportProcessingStatus
    # We use AnyHttpUrlStr instead of HttpUrl as CI uses ports as part of the URL
    dossier_url: Optional[AnyHttpUrlStr] = None

    updated_at: Optional[datetime] = None


class DossierCopyStatus(BaseModel):
    external_dossier_id: str
    status: ExportProcessingStatus
    updated_at: Optional[datetime] = None


class ExportDossierExport(BaseModel):
    external_dossier_id: ExternalDossierID
    add_uuid_suffix: Optional[bool] = None


class ExportRequest(BaseModel):
    export_uuid: UUID


class DocumentCategory(BaseModel):
    key: str
    id: str
    title_de: str
    title_en: str
    title_fr: str
    title_it: str
    description_de: Optional[str] = None
    description_en: Optional[str] = None
    description_fr: Optional[str] = None
    description_it: Optional[str] = None


class DocumentCategories(RootModel):
    root: Dict[str, DocumentCategory]


class SemanticDocumentPDFExportRequest(BaseModel):
    # Explicitly spell out that this is the export uuid and not the semantic_document uuid
    uuid: UUID


class DossierCreateJWT(BaseModel):
    exp: int = Field(
        description="Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, "
        "ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, "
        "2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day "
        "is accounted for by exactly 86400 seconds, other than that non-integer values can be "
        "represented. See RFC 3339 [RFC3339] for details regarding date/times in general "
        "and UTC in particular."
    )

    account_key: str  # "swissfex"

    first_name: Optional[str] = None  # "service-swissfex-first"
    last_name: Optional[str] = None  # "service-swissfex-last"
    email: str  # "<EMAIL>"
    username: Optional[str] = None  # "service-account-swissfex"

    external_dossier_id: Optional[str] = None

    def get_user_or_create(self) -> DossierUser:
        try:
            account = get_object_or_404(Account, key=self.account_key)

            # Not 100% sure on this - do we need this for swissfex?
            # validate_whitelistaccess(account, {'account_name': self.account_name})

            return authentication.get_user_or_create(
                account=account,
                username=self.username,
                email=self.email,
                fname=self.first_name,
                lname=self.last_name,
            )
        except Exception as e:
            logger.warning("Could not get or create user from JWT", error=e)
            raise e


class UpdateSemanticDocument(BaseModel):
    document_category_key: Optional[str] = None
    # setting title_suffix to "" will clear the title_suffix
    title_suffix: Optional[str] = None
    external_semantic_document_id: Optional[ExternalSemanticDocumentID] = None
    access_mode: Optional[AccessMode] = None
    custom_attribute: Optional[CustomAttribute] = None
