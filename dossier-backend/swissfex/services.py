from typing import Dict

from django.conf import settings
import jwt

from dossier import services
from dossier.models import Account, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r
from dossier.schemas import Language
from swissfex.schemas import schemas


def create_or_update_dossier(
    account: Account,
    dossier_create: schemas.<PERSON><PERSON><PERSON><PERSON><PERSON>,
    user: Do<PERSON><PERSON><PERSON><PERSON>,
    language: Language,
) -> Dossier:
    existing_dossier = Dossier.objects.filter(
        external_id=dossier_create.external_dossier_id, account=account
    ).first()

    # Update the name of the dossier if it already exists
    if existing_dossier:
        existing_dossier.name = dossier_create.name
        existing_dossier.save()
        return existing_dossier

    new_dossier = services.create_dossier(
        account=account,
        dossier_name=dossier_create.name,
        language=language,
        owner=user.user,
        external_id=dossier_create.external_dossier_id,
    )
    new_dossier.save()

    return new_dossier


# Move to document services?
def create_encoded_jwt(
    data: Dict, shared_secret=settings.SWISSFEX_SHARED_SECRET
) -> str:
    """
    Encodes a dictionary into a JWT.

    Args:
        data (Dict): The data to encode.
        shared_secret (str, optional): The shared secret to use for encoding.
        Defaults to settings.SWISSFEX_SHARED_SECRET.

    Returns:
        str: The encoded JWT.
    """
    encoded_jwt = jwt.encode(data, shared_secret, algorithm="HS256")
    return encoded_jwt
