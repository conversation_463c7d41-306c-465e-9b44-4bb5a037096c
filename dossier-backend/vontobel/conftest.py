import json
import os
import uuid
from typing import List

import pytest
from django.conf import settings

from faker import Faker

from vontobel.schemas.schemas import AccountName
from core.authentication import AuthenticatedClient

from dossier.models import (
    Account,
    DocumentCategory,
    JWK,
)
from dossier.services import create_expiration_date
from projectconfig.jwk import load_jwk_from_env
from vontobel.factories import VontobelAccountFactoryFaker
import jwt
from jwcrypto import jwk
from vontobel.tests.data import DATA_PATH


@pytest.fixture(scope="session")
def faker():
    return Faker(locale="de_CH")


@pytest.fixture
def vontobel_account():
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="vontobel development account",
            default_bucket_name="dms-default-bucket",
            dmf_endpoint="https://www.localhost",
        ),
        key=AccountName.vontobel.value,
    )
    return account


@pytest.fixture(scope="session")
def mock_jwks_public_private():
    # JWKS with both public and private keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()


@pytest.fixture
def mock_jwks_public():
    # JWKS with only public keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    ).model_dump(exclude_unset=True)


@pytest.fixture
def set_vontobel_JWK(vontobel_account, mock_jwks_public):
    # Set public key used for vontobel authentication
    return JWK.objects.create(jwk=mock_jwks_public["keys"][1], account=vontobel_account)


@pytest.fixture(scope="session")
def token_data(faker):
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-vontobel-first service-vontobel-last",
        "given_name": "service-vontobel-first",
        "family_name": "service-vontobel-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        # "external_dossier_id": schemas.DossierName(
        #     faker.sentence()
        # )
        # during dossier creation via API parameter
        "user_roles": ["api_role"],
        "account_key": AccountName.vontobel.value,
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


@pytest.fixture(scope="session")
def mock_token(mock_jwks_public_private, token_data):
    # need a PEM-formatted key
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,
        algorithm="RS256",
    )


@pytest.fixture
def vontobel_account_factory(vontobel_account):
    return VontobelAccountFactoryFaker(account=vontobel_account)


@pytest.fixture
def document_categories(vontobel_account_factory) -> List[DocumentCategory]:
    return vontobel_account_factory.load_initial_document_categories()


@pytest.fixture(scope="session")
def vontobel_authenticated_client(mock_token):
    return AuthenticatedClient(mock_token)


@pytest.fixture(scope="session")
def vontobel_miss_signed_authenticated_client(mock_jwks_public_private, token_data):
    """Test auth failure using key signed by vontobel"""
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][0]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    wrong_token = jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,  # use wrong key, key [0] from vontobel
        algorithm="RS256",
    )
    return AuthenticatedClient(wrong_token)
