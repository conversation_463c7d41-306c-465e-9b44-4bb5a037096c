from typing import List


import structlog
from pydantic import BaseModel


logger = structlog.get_logger(__name__)


class ContractException(BaseModel):
    _class: str


class Msg(BaseModel):
    key: str
    message: str
    args: List[str] = []


class ServiceException(ContractException):
    msg: Msg


class PropertyFailure(BaseModel):
    path: str
    value: str
    proposal: str = None


class ValidationFailure(BaseModel):
    msg: Msg
    properties: List[PropertyFailure]


class ValidationException(ServiceException):
    failures: List[ValidationFailure]
