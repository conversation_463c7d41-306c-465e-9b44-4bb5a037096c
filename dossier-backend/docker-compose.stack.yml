version: '3.8'
services:
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py dossier_event_consumer_v2
    secrets:
      - source: DMS_DEMO_ENV_V05
        target: /app/.env
    networks:
      core-services:
      caddy:


  diew:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py image_exporter_worker
    networks:
      core-services:
      caddy:
    secrets:
      - source: DMS_DEMO_ENV_V05
        target: /app/.env

  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    networks:
      core-services:
      caddy:
    deploy:
      labels:
        caddy: dms.hypo.duckdns.org
        caddy.reverse_proxy: "{{upstreams 8000}}"
        caddy.import: tls
    secrets:
      - source: DMS_DEMO_ENV_V05
        target: /app/.env

  worker:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: python manage.py worker
    networks:
      core-services:
      caddy:
    secrets:
      - source: DMS_DEMO_ENV_V05
        target: /app/.env

secrets:
  DMS_DEMO_ENV_V05:
    file: ./.env-sample


networks:
  caddy:
    external: true
  core-services:
    external: true