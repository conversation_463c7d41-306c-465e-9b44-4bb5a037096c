import logging
import os
import uuid
from random import seed

import minio
import psycopg2
import pytest
import structlog
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

from core.authentication import Authenticated<PERSON><PERSON>, create_token
from doccheck.models import BusinessCaseType as DocCheckBusinessCaseType
from doccheck.tests.test_services import create_doc_check
from dossier.helpers_model_copier import copy_dossier_models
from dossier.models import Account, BusinessCaseType, DossierUser, Dossier
from dossier.services import create_dossier
from fixtures import DATA_PATH
from projectconfig.authentication import get_user_or_create
from projectconfig.roles import INTERNAL_ROLE
from django.contrib.auth import get_user_model

from semantic_document.models import SemanticDocument

# Seed value for random generator; set int for deterministic or None for random behavior
DEFAULT_SEED_VALUE = 1

User: AbstractUser = get_user_model()


@pytest.fixture(autouse=True)
def fix_seed() -> None:
    """
    fix random's random seed used for reproducible
    behaviour.
    """
    seed(DEFAULT_SEED_VALUE)


def run_sql(sql):
    username = settings.DATABASES["default"]["USER"]
    host = settings.DATABASES["default"]["HOST"]
    password = settings.DATABASES["default"]["PASSWORD"]
    port = settings.DATABASES["default"]["PORT"]
    conn = psycopg2.connect(
        database="postgres", user=username, password=password, host=host, port=port
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cur = conn.cursor()
    cur.execute(sql)
    conn.close()


@pytest.fixture(scope="session")
def django_db_setup(django_db_setup, django_db_blocker):
    """Load cached schema from the filesystem before running the migrations."""
    from django.core.management import call_command

    #
    # test_database_name = settings.DATABASES["default"]["NAME"]
    # run_sql(
    #     f"SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '{test_database_name}'  AND pid <> pg_backend_pid();"
    # )
    # run_sql("DROP DATABASE IF EXISTS %s" % test_database_name)
    # run_sql(f"CREATE DATABASE {test_database_name} WITH TEMPLATE template1")
    #
    # # cmd = (
    # #     'pg_restore -n public -U %s -h %s '
    # #     f'-d {test_database_name} assets/db_latest.tar'
    # # )
    #
    # username = settings.DATABASES["default"]["USER"]
    # host = settings.DATABASES["default"]["HOST"]
    # password = settings.DATABASES["default"]["PASSWORD"]
    #
    # db_latest = ASSETS_PATH / "db_latest.tar"
    # cmd = f"{settings.POSTGRES_PATH}pg_restore -h {host} -U {username} -d {test_database_name} {db_latest}"
    # print("command", cmd)
    #
    # res = subprocess.run(
    #     cmd, shell=True, env={"PGPASSWORD": password}, capture_output=True
    # )
    # print(res.returncode, res.stdout, res.stderr)
    # res.check_returncode()

    with django_db_blocker.unblock():
        # Fixtures test data is generated using
        # python manage.py reset-db
        # python manage.py dumpdata -a -e contenttypes --indent 2 -o fixtures/unit_test_django_db_setup_data.json
        # python manage.py flush --no-input
        # python manage.py loaddata fixtures/unit_test_django_db_setup_data.json

        call_command("flush", "--noinput")

        call_command(
            "loaddata", os.path.join(DATA_PATH, "unit_test_django_db_setup_data.json")
        )

        call_command("migrate", "--noinput")
        # settings.DATABASES[connection.alias]["NAME"] = test_database_name
        # connection.settings_dict["NAME"] = test_database_name


@pytest.fixture
def testuser1_client():
    token = create_token(
        "testuser", "testuser", "<EMAIL>", account_key="default"
    )

    return AuthenticatedClient(token)


@pytest.fixture
def bekbe1_client() -> AuthenticatedClient:
    token = create_token(
        "bekbetestuser",
        "bekbetestuser",
        "<EMAIL>",
        account_key="bekbe",
        roles=[settings.BEKB_API_ROLE],
    )

    return AuthenticatedClient(token)


@pytest.fixture
def bekbe_fipla_client() -> AuthenticatedClient:
    token = create_token(
        "bekbetestuser",
        "bekbetestuser",
        "<EMAIL>",
        account_key="fiplae",
        roles=[settings.BEKB_API_ROLE],
    )

    return AuthenticatedClient(token)


@pytest.fixture
def bekbe1_user() -> DossierUser:
    return get_user_or_create(
        account=Account.objects.get(key="bekbe"),
        username="<EMAIL>",
        email="<EMAIL>",
        fname="bekbetestuser",
        lname="bekbetestuser",
    )


@pytest.fixture
def dossier_manager_client():
    token = create_token(
        "testuser", "testuser", "<EMAIL>", roles=["Dossier-manager"]
    )
    return AuthenticatedClient(token)


@pytest.fixture
def bekbuser1_client():
    token = create_token(
        "bekbuser", "bekbuser", "<EMAIL>", account_key="bekb test"
    )
    return AuthenticatedClient(token)


@pytest.fixture
def bekbuser1_manager_client():
    token = create_token(
        "bekbuser",
        "bekbuser",
        "<EMAIL>",
        account_key="bekb test",
        roles=[settings.NAME_OF_ROLE_MANAGER_FROM_JWT],
    )
    return AuthenticatedClient(token)


@pytest.fixture
def bekbuser1_user() -> DossierUser:
    return get_user_or_create(
        account=Account.objects.get(key="bekb test"),
        username="<EMAIL>",
        email="<EMAIL>",
        fname="bekbuser",
        lname="bekbuser",
    )


@pytest.fixture
def bekbuser2_user() -> DossierUser:
    return get_user_or_create(
        account=Account.objects.get(key="bekb test"),
        username="<EMAIL>",
        email="<EMAIL>",
        fname="bekbuser2",
        lname="bekbuser2",
    )


@pytest.fixture
def test_user_2() -> DossierUser:
    return get_user_or_create(
        account=Account.objects.get(key="default"),
        username="<EMAIL>",
        email="<EMAIL>",
        fname="testuser2",
        lname="testuser2",
    )


@pytest.fixture
def bekb_api_client():
    token = create_token(
        "BEKB API given name",
        "BEKB API family name",
        "<EMAIL>",
        account_key="bekbe",
        roles=[settings.BEKB_API_ROLE],
    )
    return AuthenticatedClient(token)


@pytest.fixture
def testuser2_client():
    token = create_token("testuser", "testuser", "<EMAIL>")
    return AuthenticatedClient(token)


ACCOUNT_KEY_DOCCHECK = "test"

ACCOUNT_KEY_NO_DOCCHECK = "test-without-doccheck"


@pytest.fixture
def testuser1_doc_client():
    """Custom testuser1 with account_key to the doccheck account"""
    token = create_token(
        "testuser1",
        "testuser",
        "<EMAIL>",
        account_key=ACCOUNT_KEY_DOCCHECK,
    )
    return AuthenticatedClient(token)


@pytest.fixture
def testuser2_doc_client():
    """Custom testuser2 with account_key to the doccheck account"""
    token = create_token(
        "testuser2",
        "testuser",
        "<EMAIL>",
        account_key=ACCOUNT_KEY_DOCCHECK,
    )
    return AuthenticatedClient(token)


@pytest.fixture
def testuser3__default_client():
    token = create_token(
        "testuser", "testuser", "<EMAIL>", account_key="default"
    )
    return AuthenticatedClient(token)


@pytest.fixture
def internal_user_client():
    token = create_token(
        "internal", "internal", "<EMAIL>", roles=[INTERNAL_ROLE]
    )
    return AuthenticatedClient(token)


@pytest.fixture
def mgmt_api_user_client():
    token = create_token(
        "management", "management", "<EMAIL>", roles=["mgmt-api"]
    )
    return AuthenticatedClient(token)


@pytest.fixture
def disable_structlog_for_testing():
    # Fixture for disabling struclog logging for certain tests (as they can be noisy)
    # Save the original configuration
    original_processors = structlog.get_config().get("processors")

    # Configure structlog to use a simpler processor for testing
    structlog.configure(processors=[structlog.processors.JSONRenderer()])

    # Optionally, set the logging level to CRITICAL to suppress most logs
    logging.getLogger().setLevel(logging.CRITICAL)

    yield

    # Restore the original configuration after the test
    structlog.configure(processors=original_processors)


def synthetic_dossier():
    # Create a dossier with all relevant fields set,
    # including things specific to BEKB, like business case type
    # and swissfex like external_id
    user = User.objects.get(username="<EMAIL>")
    doc_check = create_doc_check()

    account = Account.objects.create(
        key=ACCOUNT_KEY_DOCCHECK, name="Test Account", active_doc_check=doc_check
    )
    dossier_business_case_type = BusinessCaseType.objects.create(
        account=account, key="test business case type"
    )
    DocCheckBusinessCaseType.objects.create(
        doc_check=doc_check, key=dossier_business_case_type.key
    )
    dossier = create_dossier(
        account,
        "dossier with case",
        "de",
        user,
        businesscase_type_id=dossier_business_case_type.uuid,
    )
    dossier.external_id = "test external id"
    dossier.save()
    return dossier


@pytest.fixture
def temp_minio_bucket():
    # Pytest fixture to temporarily create an s3 bucket for testing
    minio_client = minio.Minio(
        settings.S3_ENDPOINT,
        settings.S3_ACCESS_KEY,
        settings.S3_SECRET_KEY,
        secure=settings.S3_SECURE,
        region=settings.S3_REGION,
    )

    # Generate a unique bucket name
    bucket_name = str(uuid.uuid4())

    # Make a new bucket
    minio_client.make_bucket(bucket_name)

    yield bucket_name  # Provide the fixture value

    # Teardown after test - delete the bucket
    objects = minio_client.list_objects(bucket_name, recursive=True)
    for obj in objects:
        minio_client.remove_object(bucket_name, obj.object_name)
    minio_client.remove_bucket(bucket_name)


def prepare_demo_dossier_for_account(account: Account, external_id):
    # Copies a dossier with the name "sales pitch mix with errors dossier" to the given account
    old_dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    copy_dossier_models(
        dossier=old_dossier,
        external_id=external_id,
    )

    dossier = Dossier.objects.get(external_id=external_id)

    dossier.account = account

    dossier.save()

    # Clean up all the title_custom (legacy attribute). Otherwise the translations of the document title
    # do not show up because title_custom will be applied for all langs
    semdocs = SemanticDocument.objects.filter(dossier=dossier).all()
    for s in semdocs:
        s.title_custom = None
        s.save()

    return dossier
